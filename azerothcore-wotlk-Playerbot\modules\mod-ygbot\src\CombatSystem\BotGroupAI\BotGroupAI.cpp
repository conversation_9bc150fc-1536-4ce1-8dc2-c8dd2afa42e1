#include "BotGroupAI.h"
#include "Log.h"
#include "GroupRogueAI.h"

BotGroupAI::BotGroupAI(Player* player) : PlayerBotAI(player),
    m_MasterPlayer(nullptr), m_UpdateTick(0), m_<PERSON><PERSON><PERSON>(false), 
    m_<PERSON>leeing(false), m_<PERSON><PERSON><PERSON>t(false), m_IsMeleeBot(false), 
    m_IsRangeBot(false), m_IsHealerBot(false)
{
    LOG_INFO("server", "BotGroupAI: 为玩家 {} 创建组队AI", player->GetName());
    m_scenarioType = SCENARIO_GROUP;
}

void BotGroupAI::UpdateBotAI(uint32 diff)
{
    // 基础实现，子类应该重写
    if (!m_player || !m_player->IsInWorld())
        return;

    // 组队AI的基本逻辑
    // 例如跟随队长、协助队友等
}

void BotGroupAI::ResetBotAI()
{
    m_HasReset = true;
}

bool BotGroupAI::IsIDLEBot()
{
    return !m_player->IsInCombat() && !m_player->HasUnitState(UNIT_STATE_CASTING);
}

// 添加缺失的方法实现
void BotGroupAI::ProcessBotCommand(Player* srcPlayer, const std::string& cmd)
{
    // 基础实现，子类可以重写
}

void BotGroupAI::SetTeleportToMaster()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ClearCruxMovement()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::SetCruxMovement(const Position& pos)
{
    // 基础实现，子类可以重写
}

void BotGroupAI::RndCruxMovement(float dist)
{
    // 基础实现，子类可以重写
}

void BotGroupAI::AddTankTarget(Creature* pCreature)
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ClearTankTarget()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::SetTankPosition(const Position& pos)
{
    // 基础实现，子类可以重写
}

bool BotGroupAI::ExistPullTarget()
{
    return false; // 基础实现，子类可以重写
}

void BotGroupAI::AddCruxFlee(uint32 dur, Unit* pRefUnit)
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ProcessFollowToMaster()
{
    // 基础实现，子类可以重写
}

bool BotGroupAI::TryUpMount()
{
    return false; // 基础实现，子类可以重写
}

void BotGroupAI::Dismount()
{
    // 基础实现，子类可以重写
}

bool BotGroupAI::CanReciveCommand(const std::string& cmd, std::string& param)
{
    return false; // 基础实现，子类可以重写
}

void BotGroupAI::ProcessSummonCommand()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ProcessAttackCommand()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ProcessFleeCommand()
{
    // 基础实现，子类可以重写
}

void BotGroupAI::ProcessStopCommand()
{
    // 基础实现，子类可以重写
}

Unit* BotGroupAI::GetCombatTarget(float range)
{
    return nullptr; // 基础实现，子类可以重写
}

bool BotGroupAI::DoFaceToTarget(Unit* pTarget)
{
    if (!m_player || !pTarget) return false;
    m_player->SetFacingToObject(pTarget);
    return true;
}

SpellCastResult BotGroupAI::TryCastSpell(uint32 spellID, Unit* pTarget, bool force)
{
    if (!m_player || !spellID)
        return SPELL_FAILED_DONT_REPORT;

    // 如果没有目标则默认对自己
    if (!pTarget)
        pTarget = m_player;

    // 技能存在性与冷却
    if (!m_player->HasSpell(spellID))
        return SPELL_FAILED_SPELL_LEARNED;
    if (m_player->HasSpellCooldown(spellID))
        return SPELL_FAILED_NOT_READY;

    // 目标合法性（对敌方技能场景下）
    if (pTarget != m_player && !m_player->IsValidAttackTarget(pTarget))
        return SPELL_FAILED_BAD_TARGETS;

    // 停止移动并面向目标，避免“错误98/移动中施法失败”
    if (m_player->isMoving() || m_player->HasUnitState(UNIT_STATE_MOVING))
    {
        m_player->StopMoving();
        m_player->GetMotionMaster()->Clear();
        m_player->GetMotionMaster()->MoveIdle();
        if (m_player->HasUnitState(UNIT_STATE_MOVING))
            m_player->ClearUnitState(UNIT_STATE_MOVING);
    }
    if (pTarget && pTarget != m_player)
        m_player->SetFacingToObject(pTarget);

    // 施法
    SpellCastResult result = m_player->CastSpell(pTarget, spellID, false);
    if (result != SPELL_CAST_OK && force)
    {
        result = m_player->CastSpell(pTarget, spellID, TRIGGERED_IGNORE_POWER_AND_REAGENT_COST);
    }
    return result;
}

SpellCastResult BotGroupAI::TryCastPullSpell(uint32 spellID, Unit* pTarget)
{
    // 简化：拉怪也复用TryCastSpell，后续如需忽略CD/消耗可扩展
    return TryCastSpell(spellID, pTarget, true);
}

SpellCastResult BotGroupAI::PetTryCastSpell(uint32 spellID, Unit* pTarget, bool force)
{
    // 暂未实现宠物通用施法，先返回失败；各职业可在子类实现
    return SPELL_FAILED_DONT_REPORT;
}

void BotGroupAI::ChaseTarget(Unit* pTarget, bool isMelee, float range)
{
    // TODO: 可接入BotCombatMovement
}

bool BotGroupAI::IsNotMovement()
{
    if (!m_player) return true;
    if (HasAuraMechanic(m_player, Mechanics::MECHANIC_ROOT))
    {
        m_player->StopMoving();
        return true;
    }
    return false;
}

bool BotGroupAI::IsInvincible(Unit* pTarget)
{
    if (!pTarget) return false;
    if (HasAuraMechanic(pTarget, Mechanics::MECHANIC_BANISH))
        return true;
    if (HasAuraMechanic(pTarget, Mechanics::MECHANIC_IMMUNE_SHIELD))
        return true;
    return false;
}

std::vector<Unit*> BotGroupAI::SearchFriend(float range)
{
    return {}; // 预留
}

std::vector<Player*> BotGroupAI::SearchFarFriend(float minRange, float maxRange, bool isIDLE)
{
    return {}; // 预留
}

std::vector<Unit*> BotGroupAI::SearchNeedHealth(float range)
{
    return {}; // 预留
}

std::vector<Unit*> BotGroupAI::RangeEnemyListByHasAura(uint32 aura, float range)
{
    // 最小实现：从攻击者集中筛选拥有aura的单位
    std::vector<Unit*> out;
    if (!m_player) return out;
    for (Unit* u : m_player->getAttackers())
    {
        if (!u || !u->IsAlive()) continue;
        if (m_player->GetDistance(u) > range) continue;
        if (aura == 0 || u->HasAura(aura)) out.push_back(u);
    }
    return out;
}

std::vector<Unit*> BotGroupAI::RangeEnemyListByNonAura(uint32 aura, float range)
{
    // 最小实现：从攻击者集中筛选“没有该aura”的单位；aura==0则返回所有
    std::vector<Unit*> out;
    if (!m_player) return out;
    for (Unit* u : m_player->getAttackers())
    {
        if (!u || !u->IsAlive()) continue;
        if (m_player->GetDistance(u) > range) continue;
        if (aura == 0 || !u->HasAura(aura)) out.push_back(u);
    }
    return out;
}

std::vector<Unit*> BotGroupAI::RangeEnemyListByTargetIsMe(float range)
{
    std::vector<Unit*> out;
    if (!m_player) return out;
    for (Unit* u : m_player->getAttackers())
    {
        if (!u || !u->IsAlive()) continue;
        if (m_player->GetDistance(u) > range) continue;
        out.push_back(u);
    }
    return out;
}

void BotGroupAI::ProcessIDLE()
{
    // 预留：跟随队长/上坐骑等
}

void BotGroupAI::ProcessHealth()
{
    // 预留：治疗逻辑
}

void BotGroupAI::ProcessCombat(Unit* pTarget)
{
    // 预留：公共战斗流程
}

bool BotGroupAI::HasAuraMechanic(Unit* pTarget, Mechanics mask)
{
    if (!pTarget) return false;
    return pTarget->HasAuraWithMechanic(1u << mask);
}

bool BotGroupAI::CanBlind(Unit* pTarget)
{
    return false; // 基础实现，子类可以重写
}

bool BotGroupAI::TargetIsStealth(Player* pTarget)
{
    return false; // 基础实现，子类可以重写
}

bool BotGroupAI::TargetIsMelee(Player* pTarget)
{
    return false; // 基础实现，子类可以重写
}

bool BotGroupAI::CanStartSpell()
{
    return false; // 基础实现，子类可以重写
}

BotGroupAI* BotGroupAI::CreateBotGroupAIByPlayerClass(Player* player)
{
    if (!player)
        return nullptr;

    uint8 playerClass = player->getClass();
    BotGroupAI* ai = nullptr;

    switch (playerClass)
    {
        case CLASS_ROGUE:
            ai = new GroupRogueAI(player);
            break;
        default:
            // 对于未实现特定职业AI的情况，使用通用的BotGroupAI
            ai = new BotGroupAI(player);
            LOG_INFO("server", "BotGroupAI: 为职业 {} 创建通用组队AI", playerClass);
            break;
    }

    return ai;
}

// 通用攻击系统已移至基类PlayerBotAI，这里不再需要重复实现