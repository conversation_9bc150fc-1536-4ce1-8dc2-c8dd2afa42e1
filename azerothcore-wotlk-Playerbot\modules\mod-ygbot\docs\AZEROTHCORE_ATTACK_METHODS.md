# AzerothCore中的普通攻击方法总结

## 🎯 Unit类中的核心攻击方法

### 1. 主要攻击方法

#### `bool Attack(Unit* victim, bool meleeAttack)`
**位置**: `Unit.h:838` / `Unit.cpp`
**功能**: 开始攻击目标的核心方法
**参数**:
- `victim`: 攻击目标
- `meleeAttack`: true=近战攻击，false=远程攻击

**关键功能**:
```cpp
// 设置攻击目标
m_attacking = victim;

// 如果是近战攻击，设置近战攻击状态
if (meleeAttack)
    AddUnitState(UNIT_STATE_MELEE_ATTACKING);

// 发送攻击开始消息给客户端
if (meleeAttack)
    SendMeleeAttackStart(victim);

// 添加到攻击者列表
victim->_addAttacker(this);
```

#### `void AttackerStateUpdate(Unit* victim, WeaponAttackType attType = BASE_ATTACK, bool extra = false, bool ignoreCasting = false)`
**位置**: `Unit.h:912`
**功能**: 执行实际的攻击伤害计算和应用
**参数**:
- `victim`: 攻击目标
- `attType`: 攻击类型（主手/副手/远程）
- `extra`: 是否为额外攻击
- `ignoreCasting`: 是否忽略施法状态

**用途**: 这是实际造成伤害的方法，通常在攻击计时器准备好时调用

### 2. 攻击计时器相关方法

#### `bool isAttackReady(WeaponAttackType type = BASE_ATTACK) const`
**功能**: 检查攻击是否准备好
**返回**: true=可以攻击，false=还在冷却中

#### `void resetAttackTimer(WeaponAttackType type = BASE_ATTACK)`
**功能**: 重置攻击计时器（攻击后调用）

#### `void setAttackTimer(WeaponAttackType type, int32 time)`
**功能**: 设置攻击计时器

#### `int32 getAttackTimer(WeaponAttackType type) const`
**功能**: 获取当前攻击计时器值

### 3. 攻击状态管理

#### `bool AttackStop()`
**功能**: 停止攻击

#### `Unit* GetVictim() const`
**功能**: 获取当前攻击目标

#### `void SendMeleeAttackStart(Unit* victim, Player* sendTo = nullptr)`
**功能**: 发送攻击开始消息给客户端

#### `void SendMeleeAttackStop(Unit* victim = nullptr)`
**功能**: 发送攻击停止消息给客户端

## 🎮 鼠标右键攻击的操作码处理

### 1. 攻击操作码

#### `CMSG_ATTACKSWING` (0x141)
**处理函数**: `WorldSession::HandleAttackSwingOpcode`
**位置**: `CombatHandler.cpp:28`
**功能**: 处理客户端发送的攻击请求（鼠标右键攻击）

**实现**:
```cpp
void WorldSession::HandleAttackSwingOpcode(WorldPacket& recvData)
{
    ObjectGuid guid;
    recvData >> guid;

    Unit* pEnemy = ObjectAccessor::GetUnit(*_player, guid);
    
    if (!pEnemy)
    {
        SendAttackStop(nullptr);
        return;
    }

    if (!_player->IsValidAttackTarget(pEnemy))
    {
        SendAttackStop(pEnemy);
        return;
    }

    // 核心调用：开始攻击
    _player->Attack(pEnemy, true);
}
```

#### `CMSG_ATTACKSTOP` (0x142)
**处理函数**: `WorldSession::HandleAttackStopOpcode`
**功能**: 处理停止攻击请求

**实现**:
```cpp
void WorldSession::HandleAttackStopOpcode(WorldPacket& /*recvData*/)
{
    GetPlayer()->AttackStop();
}
```

### 2. 服务器发送的攻击消息

#### `SMSG_ATTACKSTART` (0x143)
**功能**: 通知客户端攻击开始

#### `SMSG_ATTACKSTOP` (0x144)
**功能**: 通知客户端攻击停止

#### `SMSG_ATTACKSWING_NOTINRANGE` (0x145)
**功能**: 通知客户端目标不在攻击范围内

## 🤖 UnitAI中的攻击辅助方法

### 1. `DoMeleeAttackIfReady()`
**位置**: `UnitAI.cpp:39`
**功能**: AI使用的标准近战攻击方法

**完整实现**:
```cpp
void UnitAI::DoMeleeAttackIfReady()
{
    if (me->HasUnitState(UNIT_STATE_CASTING))
        return;

    Unit* victim = me->GetVictim();
    if (!victim || !victim->IsInWorld())
        return;

    if (!me->IsWithinMeleeRange(victim))
        return;

    // 主手攻击
    if (me->isAttackReady())
    {
        // 防止主手和副手同时攻击
        if (me->HasOffhandWeaponForAttack())
            if (me->getAttackTimer(OFF_ATTACK) < ATTACK_DISPLAY_DELAY)
                me->setAttackTimer(OFF_ATTACK, ATTACK_DISPLAY_DELAY);

        me->AttackerStateUpdate(victim);
        me->resetAttackTimer();
    }

    // 副手攻击
    if (me->HasOffhandWeaponForAttack() && me->isAttackReady(OFF_ATTACK))
    {
        if (me->getAttackTimer(BASE_ATTACK) < ATTACK_DISPLAY_DELAY)
            me->setAttackTimer(BASE_ATTACK, ATTACK_DISPLAY_DELAY);

        me->AttackerStateUpdate(victim, OFF_ATTACK);
        me->resetAttackTimer(OFF_ATTACK);
    }
}
```

### 2. `AttackStart(Unit* victim)`
**位置**: `UnitAI.cpp:27`
**功能**: AI开始攻击目标

**实现**:
```cpp
void UnitAI::AttackStart(Unit* victim)
{
    if (victim && me->Attack(victim, true))
        me->GetMotionMaster()->MoveChase(victim);
}
```

## 🔄 Player的自动攻击系统

### PlayerUpdates.cpp中的自动攻击逻辑
**位置**: `PlayerUpdates.cpp:194-227`

**关键条件**:
```cpp
if (HasUnitState(UNIT_STATE_MELEE_ATTACKING) && 
    !HasUnitState(UNIT_STATE_CASTING) && 
    !HasUnitState(UNIT_STATE_CHARGING))
{
    if (Unit* victim = GetVictim())
    {
        // 检查距离和角度
        if (isAttackReady(BASE_ATTACK))
        {
            if (!IsWithinMeleeRange(victim))
                setAttackTimer(BASE_ATTACK, 100);
            else if (!HasInArc(2 * M_PI / 3, victim))
                setAttackTimer(BASE_ATTACK, 100);
            else
            {
                // 执行攻击
                AttackerStateUpdate(victim, BASE_ATTACK);
                resetAttackTimer(BASE_ATTACK);
            }
        }
    }
}
```

## 🛠️ 机器人普通攻击的实现方案

### 方案1: 使用Unit::Attack + DoMeleeAttackIfReady
```cpp
// 1. 开始攻击（设置攻击状态）
bool attackResult = bot->Attack(target, true);

// 2. 在AI更新循环中调用
if (bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING))
{
    DoMeleeAttackIfReady(); // 或者直接调用AttackerStateUpdate
}
```

### 方案2: 模拟鼠标右键攻击
```cpp
// 创建攻击数据包
WorldPacket data(CMSG_ATTACKSWING, 8);
data << target->GetGUID();

// 直接调用处理函数
bot->GetSession()->HandleAttackSwingOpcode(data);
```

### 方案3: 直接使用AttackerStateUpdate
```cpp
// 设置攻击目标
bot->Attack(target, true);

// 在攻击循环中
if (bot->isAttackReady() && bot->IsWithinMeleeRange(target))
{
    bot->AttackerStateUpdate(target);
    bot->resetAttackTimer();
}
```

## 📋 推荐的机器人攻击实现

### 最佳实践组合
```cpp
class BotMeleeAttack
{
public:
    bool StartAttack(Player* bot, Unit* target)
    {
        // 1. 使用标准Attack方法开始攻击
        if (!bot->Attack(target, true))
            return false;
            
        // 2. 确保设置了正确的状态
        if (!bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING))
            bot->AddUnitState(UNIT_STATE_MELEE_ATTACKING);
            
        return true;
    }
    
    void UpdateAttack(Player* bot)
    {
        // 3. 在更新循环中执行实际攻击
        if (bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING))
        {
            Unit* victim = bot->GetVictim();
            if (victim && bot->IsWithinMeleeRange(victim) && bot->isAttackReady())
            {
                bot->AttackerStateUpdate(victim);
                bot->resetAttackTimer();
            }
        }
    }
};
```

这种方法结合了AzerothCore的标准攻击流程，既使用了官方的Attack方法来设置状态，又手动处理了实际的攻击执行，确保机器人能够正常进行普通攻击。
