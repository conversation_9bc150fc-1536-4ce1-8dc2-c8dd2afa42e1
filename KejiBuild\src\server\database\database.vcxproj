﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{91634308-AE96-3E40-B8FC-644394DD71B3}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>database</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\server\database\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">database.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">database</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\server\database\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">database.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">database</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\server\database\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">database.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">database</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\server\database\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">database.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">database</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/mysql/include" /external:I "C:/Program Files/OpenSSL-Win64/include" /bigobj /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/database/database.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/mysql/include" /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/database/database.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/mysql/include" /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/database/database.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/mysql/include" /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/database/database.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild\src\server\database;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\mysql\include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/database/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/database/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/database/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/database/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/database/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/database/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/database/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/database/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\cmake_pch.cxx">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/database.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/database.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/database.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/database.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\AdhocStatement.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\AdhocStatement.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnv.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnv.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnvFwd.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseLoader.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseLoader.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorker.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorker.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorkerPool.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorkerPool.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Field.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Field.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLConnection.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLConnection.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLHacks.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLPreparedStatement.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLPreparedStatement.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLThreading.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLThreading.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLWorkaround.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\PreparedStatement.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\PreparedStatement.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryCallback.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryCallback.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryHolder.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryHolder.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryResult.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryResult.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\SQLOperation.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Transaction.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Transaction.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\CharacterDatabase.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\CharacterDatabase.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\LoginDatabase.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\LoginDatabase.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\PlayerbotsDatabase.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\PlayerbotsDatabase.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\WorldDatabase.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\WorldDatabase.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Logging\AppenderDB.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Logging\AppenderDB.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Updater\DBUpdater.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Updater\DBUpdater.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Updater\UpdateFetcher.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/database/CMakeFiles/database.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Updater\UpdateFetcher.h" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\Debug\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\Release\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\MinSizeRel\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\RelWithDebInfo\cmake_pch.hxx" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\KejiBuild\ZERO_CHECK.vcxproj">
      <Project>{2B34230A-489D-329E-A203-4300066A20FA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{7762352D-F6A8-3C78-B40C-F6D9EC6696BB}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\argon2\argon2.vcxproj">
      <Project>{0193C8EB-A537-3520-85BF-0E144631F6EE}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\common\common.vcxproj">
      <Project>{C1B3B1F1-588C-3E0B-8C28-A2834C66B5BA}</Project>
      <Name>common</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\fmt\fmt.vcxproj">
      <Project>{7360C5EE-63A8-3467-9347-9D01A58DD42F}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\g3dlite\g3dlib.vcxproj">
      <Project>{81F9C921-10D9-36E3-88FE-F780EF060AE7}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\SFMT\sfmt.vcxproj">
      <Project>{125ADA47-4D19-3387-AB3D-C8276D002DDC}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\zlib\zlib.vcxproj">
      <Project>{792C559D-6BD0-3B63-9273-7B424A678DC9}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>