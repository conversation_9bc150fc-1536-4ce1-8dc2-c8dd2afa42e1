# 机器人普通攻击调试指南

## 🔍 当前问题分析

根据你的描述，机器人存在以下问题：
1. **不会普通攻击**
2. **战斗状态循环** - 进入战斗后立即脱离战斗，反复循环

## 🛠️ 新增的调试功能

### 1. 详细的攻击日志
我已经添加了大量调试日志来追踪问题：

#### StartMeleeAttack方法
```
WarriorOperationalAI: 机器人 哀木替 尝试开始普通攻击，目标: 老杂斑野猪
WarriorOperationalAI: 使用CMSG_ATTACKSWING操作码
WarriorOperationalAI: 操作码已发送
```

#### ExecuteDecision方法
```
WarriorOperationalAI: 执行普通攻击决策，目标: 老杂斑野猪
WarriorOperationalAI: 机器人状态 - 在战斗中: false, 有攻击状态: false, 距离: 4.50
WarriorOperationalAI: 普通攻击执行结果: true
```

#### 决策生成过程
```
WarriorOperationalAI: 没有技能决策，生成普通攻击决策
WarriorOperationalAI: 机器人 哀木替 准备执行普通攻击
WarriorOperationalAI: 普通攻击决策已添加
```

### 2. 双重攻击方案
```cpp
// 方法1: 使用CMSG_ATTACKSWING操作码
session->HandleAttackSwingOpcode(data);

// 方法2: 备用方案 - 直接调用Attack
bool attackResult = m_bot->Attack(target, true);
if (attackResult) {
    // 手动设置攻击状态和战斗状态
}
```

### 3. 状态检查和修复
```cpp
// 确保攻击状态
if (!m_bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING)) {
    m_bot->AddUnitState(UNIT_STATE_MELEE_ATTACKING);
}

// 确保战斗状态
if (!m_bot->IsInCombat()) {
    m_bot->SetInCombatWith(target);
    target->SetInCombatWith(m_bot);
}
```

## 📊 测试步骤

### 步骤1: 重新编译
```bash
make clean
make -j$(nproc)
```

### 步骤2: 测试并收集日志
1. **创建机器人**: `.bot add 哀木替`
2. **攻击测试**: `攻击` (队伍聊天)
3. **观察日志输出**

### 步骤3: 分析日志
查找以下关键日志来诊断问题：

#### A. 决策生成阶段
```
WarriorOperationalAI: 机器人 哀木替 在攻击范围内，执行技能轮换，特化: 0
WarriorOperationalAI: 机器人 哀木替 执行武器战技能轮换，目标: 老杂斑野猪
WarriorOperationalAI: 没有技能决策，生成普通攻击决策
WarriorOperationalAI: 机器人 哀木替 准备执行普通攻击
WarriorOperationalAI: 普通攻击决策已添加
WarriorOperationalAI: 机器人 哀木替 技能轮换返回 1 个决策
```

#### B. 决策执行阶段
```
WarriorOperationalAI: 执行普通攻击决策，目标: 老杂斑野猪
WarriorOperationalAI: 机器人状态 - 在战斗中: false, 有攻击状态: false, 距离: 4.50
WarriorOperationalAI: 机器人 哀木替 尝试开始普通攻击，目标: 老杂斑野猪
```

#### C. 攻击执行阶段
```
WarriorOperationalAI: 使用CMSG_ATTACKSWING操作码
WarriorOperationalAI: 操作码已发送
WarriorOperationalAI: 普通攻击执行结果: true
```

#### D. 备用方案阶段（如果操作码失败）
```
WarriorOperationalAI: 会话无效，使用直接Attack方法
WarriorOperationalAI: Attack()调用结果: true
WarriorOperationalAI: 手动添加MELEE_ATTACKING状态
WarriorOperationalAI: 手动设置战斗状态
```

## 🔍 问题诊断

### 场景1: 如果看到操作码日志但没有攻击
**可能原因**: 
- CMSG_ATTACKSWING操作码没有正确处理
- HandleAttackSwingOpcode内部检查失败

**解决方案**: 
- 检查是否有HandleAttackSwingOpcode的内部日志
- 可能需要直接使用Unit::Attack方法

### 场景2: 如果看到Attack()调用但仍然没有攻击
**可能原因**:
- Unit::Attack返回false
- 攻击状态没有正确设置
- 缺少攻击循环更新

**解决方案**:
- 检查Attack()的返回值
- 手动设置攻击状态
- 添加攻击更新循环

### 场景3: 如果战斗状态循环
**可能原因**:
- 机器人进入战斗后立即脱离
- 目标状态检查有问题
- 战斗AI状态管理错误

**解决方案**:
- 检查目标有效性检查
- 确保战斗状态持续性
- 修复AI状态转换

## 🛠️ 可能的修复方案

### 方案1: 如果操作码不工作
```cpp
// 完全移除操作码，直接使用Unit::Attack
bool WarriorOperationalAI::StartMeleeAttack(Unit* target)
{
    bool result = m_bot->Attack(target, true);
    if (result) {
        // 强制设置所有必要状态
        m_bot->AddUnitState(UNIT_STATE_MELEE_ATTACKING);
        m_bot->SetInCombatWith(target);
        target->SetInCombatWith(m_bot);
    }
    return result;
}
```

### 方案2: 如果需要攻击循环
```cpp
// 在Update中添加攻击更新
void WarriorOperationalAI::Update(uint32 diff)
{
    if (m_bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING)) {
        Unit* victim = m_bot->GetVictim();
        if (victim && m_bot->isAttackReady() && m_bot->IsWithinMeleeRange(victim)) {
            m_bot->AttackerStateUpdate(victim);
            m_bot->resetAttackTimer();
        }
    }
}
```

### 方案3: 如果战斗状态有问题
```cpp
// 简化战斗状态管理
if (!target || !target->IsAlive()) {
    // 只在目标真正无效时停止攻击
    StopMeleeAttack();
    return decisions;
}
```

## 📋 预期的成功日志

如果修复成功，应该看到完整的日志序列：

```
WarriorOperationalAI: 机器人 哀木替 在攻击范围内，执行技能轮换，特化: 0
WarriorOperationalAI: 没有技能决策，生成普通攻击决策
WarriorOperationalAI: 普通攻击决策已添加
WarriorOperationalAI: 执行普通攻击决策，目标: 老杂斑野猪
WarriorOperationalAI: 机器人状态 - 在战斗中: false, 有攻击状态: false, 距离: 4.50
WarriorOperationalAI: 使用CMSG_ATTACKSWING操作码
WarriorOperationalAI: 操作码已发送
WarriorOperationalAI: 普通攻击执行结果: true

[然后应该看到实际的攻击伤害和战斗效果]
```

## 🎯 下一步行动

1. **重新编译并测试**
2. **收集完整的日志输出**
3. **根据日志确定具体的失败点**
4. **应用对应的修复方案**

现在的实现包含了详细的调试信息和多重备用方案，应该能够帮助我们准确定位问题所在！
