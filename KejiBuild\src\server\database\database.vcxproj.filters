﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\AdhocStatement.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnv.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseLoader.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorker.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorkerPool.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Field.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLConnection.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLPreparedStatement.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLThreading.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\PreparedStatement.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryCallback.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryHolder.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryResult.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Transaction.cpp">
      <Filter>Database</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\CharacterDatabase.cpp">
      <Filter>Database\Implementation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\LoginDatabase.cpp">
      <Filter>Database\Implementation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\PlayerbotsDatabase.cpp">
      <Filter>Database\Implementation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\WorldDatabase.cpp">
      <Filter>Database\Implementation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Logging\AppenderDB.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Updater\DBUpdater.cpp">
      <Filter>Updater</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\database\Updater\UpdateFetcher.cpp">
      <Filter>Updater</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\AdhocStatement.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnv.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseEnvFwd.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseLoader.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorker.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\DatabaseWorkerPool.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Field.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLConnection.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLHacks.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLPreparedStatement.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLThreading.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\MySQLWorkaround.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\PreparedStatement.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryCallback.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryHolder.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\QueryResult.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\SQLOperation.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Transaction.h">
      <Filter>Database</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\CharacterDatabase.h">
      <Filter>Database\Implementation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\LoginDatabase.h">
      <Filter>Database\Implementation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\PlayerbotsDatabase.h">
      <Filter>Database\Implementation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Database\Implementation\WorldDatabase.h">
      <Filter>Database\Implementation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Logging\AppenderDB.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Updater\DBUpdater.h">
      <Filter>Updater</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\database\Updater\UpdateFetcher.h">
      <Filter>Updater</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\database\CMakeFiles\database.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Database">
      <UniqueIdentifier>{AEFA050C-DE64-3FC5-81EF-D6EC76591D8B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Database\Implementation">
      <UniqueIdentifier>{90B3BAC4-B36E-3377-9220-BA0D827BD6EB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{19902B88-49CA-38FC-950B-8EC705CC1038}</UniqueIdentifier>
    </Filter>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{90DBB2E8-B7B8-3A5B-B6BF-533AA6796052}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{13BB9762-CF46-3603-980C-A0F8777D13B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Updater">
      <UniqueIdentifier>{09EE0E5D-9AF6-3407-B599-68E2440568D4}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
