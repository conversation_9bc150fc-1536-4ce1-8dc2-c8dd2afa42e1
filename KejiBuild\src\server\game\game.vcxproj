﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{32663C99-0698-35F6-82F9-57D2B46880A2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>game</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\server\game\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">game.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">game</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\server\game\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">game.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">game</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\server\game\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">game.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">game</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\server\game\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">game.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">game</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /bigobj /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/game/game.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/game/game.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/game/game.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/game/game.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild\src\server\game;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/game/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/game/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/game/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/game/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/game/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/game/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/game/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/game/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\cmake_pch.cxx">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/game.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/game.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/game.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/game.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAI.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIFactory.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIImpl.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIRegistry.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIRegistry.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAISelector.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAISelector.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\GameObjectAIFactory.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\enuminfo_CreatureAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\CombatAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\CombatAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GameObjectAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GameObjectAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GuardAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GuardAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PassiveAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PassiveAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PetAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PetAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\ReactorAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\ReactorAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\TotemAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\TotemAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\UnitAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\UnitAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedCreature.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedCreature.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedEscortAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedEscortAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedFollowerAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedFollowerAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedGossip.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedGossip.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartAI.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartAI.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScriptMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScriptMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Accounts\AccountMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Accounts\AccountMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Achievements\AchievementMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Achievements\AchievementMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Addons\AddonMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Addons\AddonMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator\ArenaSpectator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator\ArenaSpectator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseSearcher.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseSearcher.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Autobroadcast\AutobroadcastMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Autobroadcast\AutobroadcastMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Battlefield.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Battlefield.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones\BattlefieldWG.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones\BattlefieldWG.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Arena.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Arena.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaScore.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeam.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeam.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeamMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeamMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Battleground.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Battleground.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundQueue.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundQueue.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundScore.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundSpamProtect.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundSpamProtect.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundUtils.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundUtils.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\enuminfo_ArenaTeam.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonRewardsDistributor.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonRewardsDistributor.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaTeamFilter.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAB.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAB.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAV.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAV.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundBE.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundBE.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundDS.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundDS.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundEY.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundEY.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundIC.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundIC.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundNA.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundNA.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRL.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRL.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRV.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRV.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundSA.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundSA.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundWS.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundWS.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Cache\CharacterCache.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Cache\CharacterCache.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Cache\WhoListCacheMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Cache\WhoListCacheMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Calendar\CalendarMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Calendar\CalendarMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Chat.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Chat.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\HyperlinkTags.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Hyperlinks.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Hyperlinks.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\Channel.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\Channel.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\ChannelMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\ChannelMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\enuminfo_Channel.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommand.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommand.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandArgs.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandArgs.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandHelpers.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandHelpers.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandTags.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandTags.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Combat\HostileRefMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\HostileRefMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Combat\ThreatMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\ThreatMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\UnitEvents.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\ConditionMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\ConditionMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\DisableMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\DisableMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\DBCStores.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\DBCStores.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Stores.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Stores.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Structure.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFG.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFG.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGGroupData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGGroupData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGPlayerData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGPlayerData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGQueue.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGQueue.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGScripts.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGScripts.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse\Corpse.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse\Corpse.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\Creature.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\Creature.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureGroups.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureGroups.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\GossipDef.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\GossipDef.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\TemporarySummon.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\TemporarySummon.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\enuminfo_CreatureData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject\DynamicObject.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject\DynamicObject.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObject.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObject.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObjectData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Item.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Item.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemEnchantmentMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemEnchantmentMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemTemplate.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\enuminfo_Item.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container\Bag.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container\Bag.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Object.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Object.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectGuid.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectGuid.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectPosSelector.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectPosSelector.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Position.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Position.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFieldFlags.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFieldFlags.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFields.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateMask.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\Pet.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\Pet.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\PetDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\CinematicMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\CinematicMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\KillRewarder.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\KillRewarder.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\Player.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\Player.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerGossip.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerMisc.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerQuest.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerSettings.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerSettings.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerStorage.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerTaxi.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerTaxi.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerUpdates.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\SocialMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\SocialMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\TradeData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\TradeData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Totem\Totem.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Totem\Totem.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Transport\Transport.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Transport\Transport.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\CharmInfo.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\CharmInfo.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\StatSystem.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\Unit.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\Unit.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\UnitDefines.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\UnitUtils.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\enuminfo_Unit.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\Vehicle.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\Vehicle.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\VehicleDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Events\GameEventMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Events\GameEventMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectAccessor.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectAccessor.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\WorldGlobals.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\WorldGlobals.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridCell.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridObjectLoader.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridObjectLoader.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridRefMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridReference.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainData.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainData.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainLoader.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainLoader.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGrid.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGridManager.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGridManager.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Cells\Cell.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Cells\CellImpl.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiers.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiers.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiersImpl.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\Group.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\Group.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupRefMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupReference.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupReference.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\Guild.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\Guild.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\GuildMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\GuildMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AddonHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AddonHandler.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ArenaTeamHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AuctionHouseHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AuthHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\BankHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\BattleGroundHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CalendarHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ChannelHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CharacterHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ChatHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CombatHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\DuelHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\GroupHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\GuildHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ItemHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\LFGHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\LootHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MailHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MiscHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MovementHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\NPCHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\NPCHandler.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\PetHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\PetitionsHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\QueryHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\QuestHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ReferAFriendHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\SkillHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\Socialhandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\SpellHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TaxiHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TicketHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TradeHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\VehicleHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\VoiceChatHandler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceSaveMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceSaveMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootItemStorage.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootItemStorage.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Mails\Mail.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Mails\Mail.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Mails\ServerMailMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Mails\ServerMailMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaBoundary.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaBoundary.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\Map.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\Map.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapInstanced.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapInstanced.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapRefMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapReference.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapUpdater.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapUpdater.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\TransportMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\TransportMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\ZoneScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\BanMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\BanMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\DynamicVisibility.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\DynamicVisibility.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\GameGraveyard.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\GameGraveyard.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Formulas.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Formulas.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Language.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Modules\ModuleMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Modules\ModuleMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Motd\MotdMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Motd\MotdMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerRefMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerReference.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerReference.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MotionMaster.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MotionMaster.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\ConfusedMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\ConfusedMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\EscortMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\EscortMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\FleeingMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\FleeingMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\HomeMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\HomeMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\IdleMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\IdleMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PathGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PathGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PointMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PointMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\RandomMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\RandomMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\TargetedMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\TargetedMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\WaypointMovementGenerator.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\WaypointMovementGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSpline.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSpline.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineFlag.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInit.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInit.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInitArgs.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementPacketBuilder.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementPacketBuilder.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementTypedefs.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementUtil.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\Spline.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\Spline.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\SplineImpl.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints\WaypointMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints\WaypointMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvP.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvP.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvPMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvPMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Petitions\PetitionMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Petitions\PetitionMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Pools\PoolMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Pools\PoolMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Quests\QuestDef.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Quests\QuestDef.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Quests\enuminfo_QuestDef.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Reputation\ReputationMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Reputation\ReputationMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\MapScripts.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgrMacros.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObject.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObject.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObjectFwd.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptSystem.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptSystem.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AccountScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AccountScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementCriteriaScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementCriteriaScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllBattlegroundScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllBattlegroundScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCommandScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCommandScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCreatureScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCreatureScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllGameObjectScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllGameObjectScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllItemScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllItemScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllMapScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllMapScript.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllScriptsObjects.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllSpellScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllSpellScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AreaTriggerScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AreaTriggerScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaTeamScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaTeamScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AuctionHouseScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AuctionHouseScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundMapScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundMapScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CommandScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CommandScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ConditionScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ConditionScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CreatureScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CreatureScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DatabaseScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DatabaseScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DynamicObjectScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DynamicObjectScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ElunaScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ElunaScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\FormulaScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\FormulaScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameEventScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameEventScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameObjectScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameObjectScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GlobalScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GlobalScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GroupScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GroupScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GuildScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GuildScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\InstanceMapScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\InstanceMapScript.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ItemScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\LootScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\LootScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MailScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MailScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MiscScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MiscScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ModuleScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ModuleScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MovementHandlerScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MovementHandlerScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\OutdoorPvPScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\OutdoorPvPScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PetScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PetScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerbotsScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ServerScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ServerScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\SpellScriptLoader.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\SpellScriptLoader.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TicketScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TicketScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TransportScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TransportScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\UnitScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\UnitScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\VehicleScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\VehicleScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WeatherScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WeatherScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldMapScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldMapScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldObjectScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldObjectScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldScript.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packet.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packet.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldPacket.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSession.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSession.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSessionMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSessionMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocket.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocket.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocketMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocketMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\AllPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\BankPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\BankPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CharacterPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CharacterPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\ChatPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\ChatPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatLogPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatLogPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\GuildPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\GuildPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\LFGPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\LFGPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\MiscPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\MiscPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PacketUtilities.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PacketUtilities.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PetPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PetPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\TotemPackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\TotemPackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\WorldStatePackets.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\WorldStatePackets.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\Opcodes.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\Opcodes.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\PacketLog.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\PacketLog.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\ServerPktHeader.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillDiscovery.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillDiscovery.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillExtraItems.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillExtraItems.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Spell.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Spell.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellEffects.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfo.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfo.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfoCorrections.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellScript.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellScript.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraEffects.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraEffects.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuras.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuras.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Texts\ChatTextBuilder.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Texts\ChatTextBuilder.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Texts\CreatureTextMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Texts\CreatureTextMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tickets\TicketMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tickets\TicketMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Time\GameTime.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Time\GameTime.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Time\UpdateTime.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Time\UpdateTime.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tools\CharacterDatabaseCleaner.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tools\CharacterDatabaseCleaner.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tools\PlayerDump.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tools\PlayerDump.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Warden.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Warden.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenCheckMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenCheckMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenMac.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenMac.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenPayloadMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenPayloadMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenWin.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenWin.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\enuminfo_WardenCheckMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Modules\WardenModuleMac.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Modules\WardenModuleWin.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Weather\Weather.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Weather\Weather.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Weather\WeatherMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Weather\WeatherMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\IWorld.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\World.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\World.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldConfig.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldConfig.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldState.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/game/CMakeFiles/game.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldState.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldStateDefines.h" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\Debug\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\Release\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\MinSizeRel\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\RelWithDebInfo\cmake_pch.hxx" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\KejiBuild\ZERO_CHECK.vcxproj">
      <Project>{2B34230A-489D-329E-A203-4300066A20FA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{7762352D-F6A8-3C78-B40C-F6D9EC6696BB}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\argon2\argon2.vcxproj">
      <Project>{0193C8EB-A537-3520-85BF-0E144631F6EE}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\common\common.vcxproj">
      <Project>{C1B3B1F1-588C-3E0B-8C28-A2834C66B5BA}</Project>
      <Name>common</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\database\database.vcxproj">
      <Project>{91634308-AE96-3E40-B8FC-644394DD71B3}</Project>
      <Name>database</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\fmt\fmt.vcxproj">
      <Project>{7360C5EE-63A8-3467-9347-9D01A58DD42F}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\g3dlite\g3dlib.vcxproj">
      <Project>{81F9C921-10D9-36E3-88FE-F780EF060AE7}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\genrev\revision.h.vcxproj">
      <Project>{001DE7BF-7FB1-3018-AAEA-7D06F0FC7440}</Project>
      <Name>revision.h</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\SFMT\sfmt.vcxproj">
      <Project>{125ADA47-4D19-3387-AB3D-C8276D002DDC}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\shared\shared.vcxproj">
      <Project>{5792A934-83A4-3A5A-B22B-41461D3B9094}</Project>
      <Name>shared</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\zlib\zlib.vcxproj">
      <Project>{792C559D-6BD0-3B63-9273-7B424A678DC9}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>