#include "FieldRogueAI.h"
#include "Log.h"

FieldRogueAI::FieldRogueAI(Player* player)
    : BotFieldA<PERSON>(player), m_BotTalentType(0), m_IsUpedPoison(0)
{
    LOG_INFO("server", "FieldRogueAI: 创建野外盗贼AI for {}", player->GetName());

    // 初始化盗贼技能
    UpdateTalentType();
    InitializeSpells(m_player);

    // 🔧 修复：登录后立即检查毒药状态
    if (player->GetLevel() >= 20)
    {
        LOG_INFO("server", "FieldRogueAI: 开始登录后毒药检查");

        // 检查主手武器是否需要毒药
        if (IsMainHandWeaponNoEnchant())
        {
            LOG_INFO("server", "FieldRogueAI: 登录时主手武器需要毒药");
            if (UseInstantPoisonOnMainHand())
            {
                LOG_INFO("server", "FieldRogueAI: 登录时主手武器上毒成功");
            }
            else
            {
                LOG_WARN("server", "FieldRogueAI: 登录时主手武器上毒失败");
            }
        }
        else
        {
            LOG_INFO("server", "FieldRogueAI: 登录时主手武器已有毒药");
        }

        // 检查副手武器是否需要毒药
        if (IsOffHandWeaponNoEnchant())
        {
            LOG_INFO("server", "FieldRogueAI: 登录时副手武器需要毒药");
            if (UseDeadlyPoisonOnOffHand())
            {
                LOG_INFO("server", "FieldRogueAI: 登录时副手武器上毒成功");
            }
            else
            {
                LOG_WARN("server", "FieldRogueAI: 登录时副手武器上毒失败");
            }
        }
        else
        {
            LOG_INFO("server", "FieldRogueAI: 登录时副手武器已有毒药");
        }

        LOG_INFO("server", "FieldRogueAI: 登录后毒药检查完成");
    }

    LOG_INFO("server", "FieldRogueAI: 盗贼技能初始化完成 for {}", player->GetName());
}

FieldRogueAI::~FieldRogueAI()
{
}

void FieldRogueAI::UpdateBotAI(uint32 diff)
{
    // 🔧 添加调试日志，确认方法被调用
    static uint32 debugCallCount = 0;
    if (++debugCallCount % 100 == 0) // 每100次调用输出一次日志
    {
        LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 方法被调用，调用次数: {}, 战斗状态: {}",
                 debugCallCount, m_player ? m_player->IsInCombat() : false);
    }

    if (!m_player || !m_player->IsInWorld())
        return;

    // 🔧 修复：只在非战斗时检查毒药
    // 战斗中不检查毒药，避免影响战斗性能和逻辑
    if (!m_player->IsInCombat())
    {
        static uint32 lastPoisonCheck = 0;
        uint32 currentTime = getMSTime();

        // 每3秒检查一次毒药状态
        if (lastPoisonCheck == 0 || (currentTime - lastPoisonCheck) > 3000)
        {
            LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 非战斗状态，开始毒药检查");

            // 检查主手武器是否需要毒药
            if (IsMainHandWeaponNoEnchant())
            {
                LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 主手武器需要毒药，执行上毒");
                if (UseInstantPoisonOnMainHand())
                {
                    LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 主手武器上毒成功");
                }
                else
                {
                    LOG_WARN("server", "FieldRogueAI::UpdateBotAI: 主手武器上毒失败");
                }
            }
            else
            {
                LOG_DEBUG("server", "FieldRogueAI::UpdateBotAI: 主手武器已有毒药");
            }

            // 检查副手武器是否需要毒药
            if (IsOffHandWeaponNoEnchant())
            {
                LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 副手武器需要毒药，执行上毒");
                if (UseDeadlyPoisonOnOffHand())
                {
                    LOG_INFO("server", "FieldRogueAI::UpdateBotAI: 副手武器上毒成功");
                }
                else
                {
                    LOG_WARN("server", "FieldRogueAI::UpdateBotAI: 副手武器上毒失败");
                }
            }
            else
            {
                LOG_DEBUG("server", "FieldRogueAI::UpdateBotAI: 副手武器已有毒药");
            }

            lastPoisonCheck = currentTime;
        }
    }
    else
    {
        LOG_DEBUG("server", "FieldRogueAI::UpdateBotAI: 战斗中，跳过毒药检查");
    }

    // 调用基类的更新逻辑
    BotFieldAI::UpdateBotAI(diff);
}

void FieldRogueAI::ResetBotAI()
{
    BotFieldAI::ResetBotAI();
    m_IsUpedPoison = 0;
    UpdateTalentType();
    InitializeSpells(m_player);

    LOG_INFO("server", "FieldRogueAI: 重置盗贼AI for {}", m_player->GetName());
}

// 实现关键的TryCastSpell方法
SpellCastResult FieldRogueAI::TryCastSpell(uint32 spellID, Unit* pTarget, bool force)
{
    if (!spellID || !m_player)
    {
        LOG_ERROR("server", "FieldRogueAI::TryCastSpell: 技能ID或玩家为空");
        return SPELL_FAILED_DONT_REPORT;
    }

    LOG_INFO("server", "FieldRogueAI::TryCastSpell: 尝试释放技能 {} 对目标 {}", 
             spellID, pTarget ? pTarget->GetName() : "自己");

    // 检查技能是否存在
    if (!m_player->HasSpell(spellID))
    {
        LOG_ERROR("server", "FieldRogueAI::TryCastSpell: 机器人 {} 没有技能 {}", m_player->GetName(), spellID);
        return SPELL_FAILED_SPELL_LEARNED;
    }

    // 检查冷却时间
    if (m_player->HasSpellCooldown(spellID))
    {
        LOG_INFO("server", "FieldRogueAI::TryCastSpell: 技能 {} 正在冷却中", spellID);
        return SPELL_FAILED_NOT_READY;
    }

    // 检查能量 - 降低要求
    uint32 energyPer = BotRogueSpells::GetEnergyPowerPer();
    if (energyPer < 20) // 需要20%能量
    {
        LOG_INFO("server", "FieldRogueAI::TryCastSpell: 能量不足 {}%", energyPer);
        return SPELL_FAILED_NO_POWER;
    }

    // 检查目标
    if (pTarget && !m_player->IsValidAttackTarget(pTarget))
    {
        LOG_ERROR("server", "FieldRogueAI::TryCastSpell: 无效目标");
        return SPELL_FAILED_BAD_TARGETS;
    }

    // 获取技能信息
    SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(spellID);
    if (!spellInfo)
    {
        LOG_ERROR("server", "FieldRogueAI::TryCastSpell: 找不到技能信息 {}", spellID);
        return SPELL_FAILED_SPELL_UNAVAILABLE;
    }

    // 检查距离
    if (pTarget && !m_player->IsWithinMeleeRange(pTarget))
    {
        LOG_INFO("server", "FieldRogueAI::TryCastSpell: 目标超出范围，距离: {:.2f}", m_player->GetDistance(pTarget));
        return SPELL_FAILED_OUT_OF_RANGE;
    }

    // 面向目标
    if (pTarget)
    {
        m_player->SetFacingToObject(pTarget);
    }

    // 停止移动以确保技能释放
    if (m_player->isMoving())
    {
        m_player->StopMoving();
    }

    // 释放技能 - 先尝试正常释放，失败则使用触发模式
    SpellCastResult result = m_player->CastSpell(pTarget, spellID, false);

    // 如果正常释放失败，尝试使用触发模式
    if (result != SPELL_CAST_OK)
    {
        LOG_INFO("server", "FieldRogueAI::TryCastSpell: 正常释放失败，尝试触发模式");
        result = m_player->CastSpell(pTarget, spellID, TRIGGERED_IGNORE_POWER_AND_REAGENT_COST);
    }
    
    if (result == SPELL_CAST_OK)
    {
        LOG_INFO("server", "FieldRogueAI::TryCastSpell: 机器人 {} 成功释放技能 {} 对目标 {}", 
                 m_player->GetName(), spellID, pTarget ? pTarget->GetName() : "自己");
    }
    else
    {
        LOG_ERROR("server", "FieldRogueAI::TryCastSpell: 机器人 {} 释放技能 {} 失败，错误: {}",
                 m_player->GetName(), spellID, static_cast<int>(result));
    }

    return result;
}

// 实现CanStartSpell方法
bool FieldRogueAI::CanStartSpell()
{
    if (!m_player)
    {
        LOG_ERROR("server", "FieldRogueAI::CanStartSpell: 玩家对象为空");
        return false;
    }

    // 检查是否在施法中
    if (m_player->HasUnitState(UNIT_STATE_CASTING))
    {
        LOG_INFO("server", "FieldRogueAI::CanStartSpell: 机器人正在施法中");
        return false;
    }

    // 检查是否被控制
    if (m_player->HasUnitState(UNIT_STATE_CONTROLLED))
    {
        LOG_INFO("server", "FieldRogueAI::CanStartSpell: 机器人被控制中");
        return false;
    }

    // 检查能量 - 降低要求
    uint32 energyPer = GetEnergyPowerPer();
    if (energyPer < 20) // 需要20%能量
    {
        LOG_INFO("server", "FieldRogueAI::CanStartSpell: 能量不足 {}%，无法开始施法", energyPer);
        return false;
    }

    LOG_INFO("server", "FieldRogueAI::CanStartSpell: 可以开始施法，当前能量: {}%", energyPer);
    return true;
}

void FieldRogueAI::UpdateTalentType()
{
    // 使用TalentSystem的天赋检测
    m_BotTalentType = GetTalentType(m_player);

    LOG_DEBUG("server", "FieldRogueAI: {} 天赋类型检测结果: {} (0=刺杀, 1=战斗, 2=敏锐)",
              m_player->GetName(), m_BotTalentType);
}

void FieldRogueAI::ProcessMeleeSpell(Unit* pTarget)
{
    if (!pTarget)
    {
        LOG_ERROR("server", "FieldRogueAI::ProcessMeleeSpell: 目标为空");
        return;
    }

    LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 开始处理盗贼战斗逻辑，目标: {}", pTarget->GetName());

    // 首先确保机器人正在攻击目标
    if (!m_player->GetVictim() || m_player->GetVictim() != pTarget)
    {
        bool attackResult = m_player->Attack(pTarget, true);
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 开始攻击目标 {}，结果: {}", pTarget->GetName(), attackResult);
        
        // 关键修复：Player需要手动设置战斗状态
        if (attackResult)
        {
            m_player->SetInCombatWith(pTarget);
            pTarget->SetInCombatWith(m_player);
            m_player->AddThreat(pTarget, 1.0f);
            LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: Player攻击修复 - 手动设置战斗状态");
        }
        
        // 设置面向目标
        m_player->SetFacingToObject(pTarget);
    }

    // 强制执行普通攻击 - 参考jbbot的实现
    ForceBasicAttack(pTarget);

    // 检查是否可以使用技能
    bool canUseSpells = CanStartSpell();
    LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 可以使用技能: {}", canUseSpells);

    if (!canUseSpells)
    {
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 无法使用技能，仅进行普通攻击");
        return;
    }

    // 更新战斗状态
    m_combatState.comboPoints = m_player->GetComboPoints();
    m_combatState.hasSliceAndDice = HasSliceAndDice();
    m_combatState.inStealth = m_player->HasAuraType(SPELL_AURA_MOD_STEALTH);

    LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 战斗状态 - 连击点:{}, 切割:{}, 潜行:{}", 
              m_combatState.comboPoints, m_combatState.hasSliceAndDice, m_combatState.inStealth);

    // 防止技能过于频繁使用
    uint32 currentTime = getMSTime();
    if (currentTime - m_combatState.lastSpellTime < 1500) // 1.5秒冷却
    {
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 技能冷却中，等待");
        return;
    }

    // 简化技能选择 - 直接尝试基础攻击技能
    uint32 spellToUse = 0;
    
    // 如果没有连击点，使用邪恶攻击
    if (m_combatState.comboPoints == 0 && RogueAttack_EvilAtt)
    {
        spellToUse = RogueAttack_EvilAtt;
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 选择邪恶攻击积累连击点");
    }
    // 如果有连击点但没有切割，使用切割
    else if (m_combatState.comboPoints >= 1 && !m_combatState.hasSliceAndDice && RogueAttack_Separate)
    {
        spellToUse = RogueAttack_Separate;
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 选择切割维持攻击速度");
    }
    // 如果有5个连击点，使用剔骨
    else if (m_combatState.comboPoints >= 5 && RogueAttack_Damage)
    {
        spellToUse = RogueAttack_Damage;
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 选择剔骨消耗连击点");
    }

    if (spellToUse != 0)
    {
        SpellCastResult result = TryCastSpell(spellToUse, pTarget);
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 尝试释放技能 {} 结果: {} ({})", 
                 spellToUse, static_cast<int>(result), GetSpellFailureReason(result));
        
        if (result == SPELL_CAST_OK)
        {
            m_combatState.lastSpellCast = spellToUse;
            m_combatState.lastSpellTime = currentTime;
            LOG_INFO("server", "FieldRogueAI: {} 成功使用技能 {}", m_player->GetName(), spellToUse);
        }
        else
        {
            LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 技能释放失败，原因: {}", GetSpellFailureReason(result));
            // 技能失败时也要更新时间，避免无限重试
            m_combatState.lastSpellTime = currentTime;
        }
    }
    else
    {
        LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpell: 没有合适的技能，继续普通攻击");
    }
}

void FieldRogueAI::ProcessMeleeSpellFallback(Unit* pTarget)
{
    if (!pTarget || !m_player)
        return;

    LOG_DEBUG("server", "FieldRogueAI::ProcessMeleeSpellFallback: 开始天赋专精战斗逻辑，天赋类型: {}", m_BotTalentType);

    // 确保普通攻击继续
    if (!m_player->GetVictim() || m_player->GetVictim() != pTarget)
    {
        m_player->Attack(pTarget, true);
    }

    // 首先检查毒药 - 完全按照mod-playerbots-liyunfan的持续检查逻辑
    static uint32 lastPoisonCheck = 0;
    uint32 currentTime = getMSTime();

    // 每5秒检查一次毒药状态（模拟mod-playerbots-liyunfan的持续触发器检查）
    if (lastPoisonCheck == 0 || (currentTime - lastPoisonCheck) > 5000)
    {
        // 检查主手武器是否需要毒药
        if (IsMainHandWeaponNoEnchant())
        {
            LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpellFallback: 主手武器需要毒药，执行上毒");
            if (UseInstantPoisonOnMainHand())
            {
                LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpellFallback: 主手武器上毒成功");
            }
        }

        // 检查副手武器是否需要毒药
        if (IsOffHandWeaponNoEnchant())
        {
            LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpellFallback: 副手武器需要毒药，执行上毒");
            if (UseDeadlyPoisonOnOffHand())
            {
                LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpellFallback: 副手武器上毒成功");
            }
        }

        lastPoisonCheck = currentTime;
    }

    // 潜行检查 - 完全按照mod-playerbots-liyunfan的触发器逻辑
    if (CanStealth() && ShouldStealth())
    {
        if (TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "FieldRogueAI::ProcessMeleeSpellFallback: 成功进入潜行状态");
            OnCastSneak();
            return;
        }
    }

    // 潜行状态下的技能
    if (ProcessSneakSpell(pTarget))
        return;

    // 根据天赋类型执行不同的战斗逻辑
    switch (m_BotTalentType)
    {
        case 0: // 刺杀天赋
            ProcessAssassinationRotation(pTarget);
            break;
        case 1: // 战斗天赋
            ProcessCombatRotation(pTarget);
            break;
        case 2: // 敏锐天赋
            ProcessSubtletyRotation(pTarget);
            break;
        default:
            ProcessGenericRotation(pTarget);
            break;
    }
}

// 刺杀天赋轮换逻辑
void FieldRogueAI::ProcessAssassinationRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查多目标AOE
    auto nearbyEnemies = RangeEnemyListByNonAura(0, BotAITool::NEEDFLEE_CHECKRANGE);
    if (nearbyEnemies.size() > 3)
    {
        // AOE输出：刀扇
        if (RogueAOE_Knife && energy >= 60 && TryCastSpell(RogueAOE_Knife, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用刀扇进行AOE输出");
            return;
        }
    }

    // 检查重要BUFF和DEBUFF
    bool hasSliceAndDice = m_player->HasAura(5171);  // 切割
    bool hasHungerForBlood = pTarget->HasAura(51662); // 血之饥渴
    bool hasRupture = pTarget->HasAura(1943);         // 割裂
    bool hasDeadlyPoison = pTarget->HasAuraState(AURA_STATE_DEADLY_POISON, nullptr, m_player);

    // 优先级1: 保持切割BUFF
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用切割提升攻击速度");
            return;
        }
    }

    // 优先级2: 血之饥渴DEBUFF
    if (!hasHungerForBlood && RogueAssist_Blood && energy >= 15)
    {
        if (TryCastSpell(RogueAssist_Blood, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用血之饥渴增加伤害");
            return;
        }
    }

    // 优先级3: 连击点消耗技能
    if (combo >= 3)
    {
        // 割裂即将结束或能量即将溢出时优先使用割裂
        if ((!hasRupture || GetAuraRemainingTime(pTarget, 1943) < 6000) && energy >= 25)
        {
            if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "刺杀天赋: 刷新割裂DOT");
                return;
            }
        }

        // 毒伤：需要高位能量(80+)且有致命毒药
        if (hasDeadlyPoison && energy >= 80 && combo >= 3)
        {
            if (RogueAttack_PoisonDmg && TryCastSpell(RogueAttack_PoisonDmg, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "刺杀天赋: 使用毒伤，连击点: {}, 能量: {}", combo, energy);
                return;
            }
        }
    }

    // 优先级4: 连击点建立
    if (combo < 5)
    {
        // 毁伤 - 刺杀天赋主要攒星技能
        if (RogueAttack_Injure && energy >= 40 && TryCastSpell(RogueAttack_Injure, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用毁伤攒连击点");
            return;
        }

        // 邪恶攻击作为备选
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用邪恶攻击攒连击点");
            return;
        }
    }

    // 冷血爆发
    if (combo >= 4 && RogueAssist_NextCrit && !m_player->HasSpellCooldown(14177))
    {
        if (TryCastSpell(RogueAssist_NextCrit, m_player) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用冷血提升爆发");
            return;
        }
    }
}

// 战斗天赋轮换逻辑
void FieldRogueAI::ProcessCombatRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查重要BUFF
    bool hasSliceAndDice = m_player->HasAura(5171);  // 切割
    bool hasRupture = pTarget->HasAura(1943);         // 割裂

    // 优先级1: 全程保持切割效果
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 开启切割提升攻击速度");
            return;
        }
    }

    // 优先级2: 连击点消耗逻辑
    if (combo >= 3)
    {
        // 切割时间较短时，优先使用割裂延长切割时间
        if (hasSliceAndDice && GetAuraRemainingTime(m_player, 5171) < 8000)
        {
            if (RogueAttack_Separate && energy >= 25 && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 刷新切割时间");
                return;
            }
        }

        // 切割时间充足时，攒到5星使用剔骨
        if (combo >= 5 && hasSliceAndDice && GetAuraRemainingTime(m_player, 5171) > 8000)
        {
            if (RogueAttack_Damage && energy >= 35 && TryCastSpell(RogueAttack_Damage, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 5星剔骨高额伤害");
                return;
            }
        }

        // 如果没有割裂DOT，可以上一个
        if (!hasRupture && combo >= 3 && energy >= 25)
        {
            if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 上割裂DOT");
                return;
            }
        }
    }

    // 优先级3: 连击点建立 - 影袭(邪恶攻击)
    if (combo < 5)
    {
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 使用影袭攒连击点");
            return;
        }

        // 背刺作为备选
        if (RogueAttack_BackAtt && energy >= 40 && TryCastSpell(RogueAttack_BackAtt, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 使用背刺攒连击点");
            return;
        }
    }

    // 爆发技能：剑刃乱舞、杀戮盛宴、冲动
    if (combo >= 3 && energy >= 60)
    {
        // 剑刃乱舞
        if (RogueAOE_AllDance && !m_player->HasSpellCooldown(13877))
        {
            if (TryCastSpell(RogueAOE_AllDance, m_player) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 开启剑刃乱舞");
                return;
            }
        }

        // 冲动
        if (RogueAssist_FastEnergy && !m_player->HasSpellCooldown(13750))
        {
            if (TryCastSpell(RogueAssist_FastEnergy, m_player) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 使用冲动提升能量恢复");
                return;
            }
        }
    }
}

// 敏锐天赋轮换逻辑
void FieldRogueAI::ProcessSubtletyRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查重要BUFF和DEBUFF
    bool hasSliceAndDice = m_player->HasAura(5171);   // 切割
    bool hasRupture = pTarget->HasAura(1943);          // 割裂
    bool hasRecuperate = m_player->HasAura(73651);     // 养精蓄锐
    bool hasShadowDance = m_player->HasAura(51713);    // 暗影之舞

    // 能量管理：尽量保持在60以下避免溢出
    bool energyNearCap = energy >= 80;

    // 优先级1: 切割 - 最高优先级
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 开启切割");
            return;
        }
    }

    // 优先级2: 5星割裂
    if (!hasRupture && combo >= 5 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 5星割裂");
            return;
        }
    }

    // 优先级3: 养精蓄锐
    if (!hasRecuperate && combo >= 1 && energy >= 25)
    {
        // 这里需要养精蓄锐的技能ID，暂时用切割代替
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, m_player) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 开启养精蓄锐");
            return;
        }
    }

    // 优先级4: 5星剔骨（在切割和养精蓄锐快断前4秒内使用）
    if (combo >= 5 && hasSliceAndDice && hasRecuperate)
    {
        uint32 sliceTime = GetAuraRemainingTime(m_player, 5171);
        uint32 recupTime = GetAuraRemainingTime(m_player, 73651);

        if ((sliceTime < 4000 || recupTime < 4000) && energy >= 35)
        {
            if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SPELL_CAST_OK)
            {
                LOG_INFO("server", "敏锐天赋: 5星剔骨刷新BUFF");
                return;
            }
        }
    }

    // 暗影之舞爆发
    if (!hasShadowDance && combo >= 3 && RogueAssist_ShadowDance && !m_player->HasSpellCooldown(51713))
    {
        if (TryCastSpell(RogueAssist_ShadowDance, m_player) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 开启暗影之舞");
            return;
        }
    }

    // 暗影步接近目标
    if (m_player->GetDistance(pTarget) > 8.0f && RogueAssist_ShadowFlash && !m_player->HasSpellCooldown(36554))
    {
        if (TryCastSpell(RogueAssist_ShadowFlash, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 暗影步接近目标");
            return;
        }
    }

    // 连击点建立
    if (combo < 5 && energy >= 40)
    {
        // 在目标背后使用背刺
        if (RogueAttack_BackAtt && TryCastSpell(RogueAttack_BackAtt, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 背刺攒星");
            return;
        }

        // 在目标正面使用出血
        if (RogueAttack_Blood && TryCastSpell(RogueAttack_Blood, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 出血攒星");
            return;
        }

        // 邪恶攻击作为备选
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 邪恶攻击攒星");
            return;
        }
    }

    // 能量即将溢出时的处理
    if (energyNearCap && combo >= 1)
    {
        if (RogueAttack_Damage && energy >= 35 && TryCastSpell(RogueAttack_Damage, pTarget) == SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 能量溢出，使用剔骨");
            return;
        }
    }
}

// 通用轮换逻辑（备用）
void FieldRogueAI::ProcessGenericRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 简单的通用轮换
    if (combo >= 3 && energy >= 25)
    {
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SPELL_CAST_OK)
            return;
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SPELL_CAST_OK)
            return;
    }

    if (combo < 5 && energy >= 40)
    {
        if (RogueAttack_EvilAtt && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SPELL_CAST_OK)
            return;
        if (RogueAttack_BackAtt && TryCastSpell(RogueAttack_BackAtt, pTarget) == SPELL_CAST_OK)
            return;
    }
}

void FieldRogueAI::ProcessRangeSpell(Unit* pTarget)
{
    if (!pTarget || m_player->GetSelectedUnit() != pTarget)
        return;

    if (m_player->GetDistance(pTarget) < 20)
    {
        if (RogueRange_Throw && TryCastSpell(RogueRange_Throw, pTarget) == SPELL_CAST_OK)
            return;
    }
}

bool FieldRogueAI::ProcessNormalSpell()
{
    if (m_player->IsMounted())
        return false;

    LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 被调用，开始非战斗处理");

    // 非战斗状态下持续检查毒药 - 作为UpdateBotAI的备用机制
    bool appliedPoison = false;

    // 检查主手武器是否需要毒药
    if (IsMainHandWeaponNoEnchant())
    {
        LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 主手武器需要毒药");
        if (UseInstantPoisonOnMainHand())
        {
            LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 主手武器上毒成功");
            appliedPoison = true;
        }
        else
        {
            LOG_WARN("server", "FieldRogueAI::ProcessNormalSpell: 主手武器上毒失败");
        }
    }
    else
    {
        LOG_DEBUG("server", "FieldRogueAI::ProcessNormalSpell: 主手武器已有毒药");
    }

    // 检查副手武器是否需要毒药
    if (IsOffHandWeaponNoEnchant())
    {
        LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 副手武器需要毒药");
        if (UseDeadlyPoisonOnOffHand())
        {
            LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 副手武器上毒成功");
            appliedPoison = true;
        }
        else
        {
            LOG_WARN("server", "FieldRogueAI::ProcessNormalSpell: 副手武器上毒失败");
        }
    }
    else
    {
        LOG_DEBUG("server", "FieldRogueAI::ProcessNormalSpell: 副手武器已有毒药");
    }

    LOG_INFO("server", "FieldRogueAI::ProcessNormalSpell: 处理完成，应用了毒药: {}", appliedPoison);
    return appliedPoison;
}

void FieldRogueAI::UpEnergy()
{
    // 能量管理逻辑
    if (GetEnergyPowerPer() < 30)
    {
        if (RogueAssist_FastEnergy && TryCastSpell(RogueAssist_FastEnergy, m_player) == SPELL_CAST_OK)
            return;
    }
}

bool FieldRogueAI::NeedFlee()
{
    if (m_player->GetHealthPct() < 30.0f)
        return true;
    
    auto enemies = RangeEnemyListByTargetIsMe(BotAITool::NEEDFLEE_CHECKRANGE);
    return enemies.size() > 2;
}

uint32 FieldRogueAI::GetEnergyPowerPer()
{
    float per = (float)m_player->GetPower(POWER_ENERGY) / (float)m_player->GetMaxPower(POWER_ENERGY);
    return (uint32)(per * 100);
}

uint32 FieldRogueAI::GetPoisonEntryByWeaponType(EquipmentSlots equipSlot)
{
    // TODO: 根据武器类型返回合适的毒药
    return 0;
}

bool FieldRogueAI::ProcessUpPoison()
{
    // TODO: 实现毒药涂抹逻辑
    return false;
}

bool FieldRogueAI::ProcessSneakSpell(Unit* pTarget)
{
    if (!m_player->HasAura(RogueGuard_Sneak))
        return false;

    // 潜行状态下的技能选择
    if (RogueSneak_Ambush && TryCastSpell(RogueSneak_Ambush, pTarget) == SPELL_CAST_OK)
        return true;
    
    if (RogueSneak_Surprise && TryCastSpell(RogueSneak_Surprise, pTarget) == SPELL_CAST_OK)
        return true;

    return false;
}

bool FieldRogueAI::ProcessMeleeBlind(Unit* pTarget)
{
    // TODO: 实现致盲逻辑
    return false;
}

bool FieldRogueAI::CanConsumeCombo(Unit* pTarget)
{
    return m_player->GetComboPoints() >= 3;
}

bool FieldRogueAI::CastCloakByNeed()
{
    // TODO: 实现斗篷逻辑
    return false;
}

void FieldRogueAI::OnCastSneak()
{
    LOG_DEBUG("server", "FieldRogueAI: {} 进入潜行状态", m_player->GetName());
}

// 智能战斗逻辑实现 - 整合自RogueCombatAI
uint32 FieldRogueAI::SelectBestSpell(Unit* pTarget)
{
    if (!pTarget)
    {
        LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 目标为空");
        return 0;
    }

    LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 开始选择技能，潜行:{}, 连击点:{}, 切割:{}", 
              m_combatState.inStealth, m_combatState.comboPoints, m_combatState.hasSliceAndDice);

    // 1. 优先使用开场技能（如果在潜行状态）
    if (m_combatState.inStealth)
    {
        uint32 opener = GetOpenerSpell(pTarget);
        if (opener != 0)
        {
            LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 选择潜行开场技能 {}", opener);
            return opener;
        }
    }

    // 2. 维持切割状态
    if (!m_combatState.hasSliceAndDice && m_combatState.comboPoints >= 1)
    {
        uint32 finisher = GetFinisherSpell(pTarget);
        if (finisher != 0)
        {
            LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 选择维持切割技能 {}", finisher);
            return finisher;
        }
    }

    // 3. 如果有5个连击点，使用终结技
    if (m_combatState.comboPoints >= 5)
    {
        uint32 finisher = GetFinisherSpell(pTarget);
        if (finisher != 0)
        {
            LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 选择终结技能 {}", finisher);
            return finisher;
        }
    }

    // 4. 积累连击点
    uint32 comboBuilder = GetComboBuilderSpell(pTarget);
    if (comboBuilder != 0)
    {
        LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 选择连击点积累技能 {}", comboBuilder);
        return comboBuilder;
    }

    // 5. 使用辅助技能
    uint32 utility = GetUtilitySpell(pTarget);
    if (utility != 0)
    {
        LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 选择辅助技能 {}", utility);
        return utility;
    }

    LOG_DEBUG("server", "FieldRogueAI::SelectBestSpell: 没有找到合适的技能");
    return 0;
}

uint32 FieldRogueAI::GetOpenerSpell(Unit* pTarget)
{
    // 潜行开场技能优先级：伏击 > 偷袭
    if (RogueSneak_Ambush && CanUseSpellSmart(RogueSneak_Ambush, pTarget))
        return RogueSneak_Ambush;
    
    if (RogueSneak_Surprise && CanUseSpellSmart(RogueSneak_Surprise, pTarget))
        return RogueSneak_Surprise;
    
    return 0;
}

uint32 FieldRogueAI::GetComboBuilderSpell(Unit* pTarget)
{
    LOG_DEBUG("server", "FieldRogueAI::GetComboBuilderSpell: 邪恶攻击ID:{}, 背刺ID:{}", RogueAttack_EvilAtt, RogueAttack_BackAtt);
    
    // 连击点积累技能优先级：邪恶攻击 > 背刺
    if (RogueAttack_EvilAtt && CanUseSpellSmart(RogueAttack_EvilAtt, pTarget))
    {
        LOG_DEBUG("server", "FieldRogueAI::GetComboBuilderSpell: 选择邪恶攻击 {}", RogueAttack_EvilAtt);
        return RogueAttack_EvilAtt;
    }
    
    if (RogueAttack_BackAtt && CanUseSpellSmart(RogueAttack_BackAtt, pTarget))
    {
        LOG_DEBUG("server", "FieldRogueAI::GetComboBuilderSpell: 选择背刺 {}", RogueAttack_BackAtt);
        return RogueAttack_BackAtt;
    }
    
    LOG_DEBUG("server", "FieldRogueAI::GetComboBuilderSpell: 没有可用的连击点积累技能");
    return 0;
}

uint32 FieldRogueAI::GetFinisherSpell(Unit* pTarget)
{
    // 如果没有切割效果，优先使用切割
    if (!m_combatState.hasSliceAndDice && RogueAttack_Separate && 
        CanUseSpellSmart(RogueAttack_Separate, pTarget))
    {
        return RogueAttack_Separate; // 切割
    }
    
    // 终结技能优先级：剔骨 > 切割
    if (RogueAttack_Damage && CanUseSpellSmart(RogueAttack_Damage, pTarget))
        return RogueAttack_Damage; // 剔骨
    
    if (RogueAttack_Separate && CanUseSpellSmart(RogueAttack_Separate, pTarget))
        return RogueAttack_Separate; // 切割
    
    return 0;
}

uint32 FieldRogueAI::GetUtilitySpell(Unit* pTarget)
{
    // 辅助技能：脚踢（打断施法）
    if (pTarget->IsNonMeleeSpellCast(false) && RogueAssist_BlockCast && 
        CanUseSpellSmart(RogueAssist_BlockCast, pTarget))
    {
        return RogueAssist_BlockCast; // 脚踢
    }
    
    return 0;
}

bool FieldRogueAI::CanUseSpellSmart(uint32 spellId, Unit* pTarget)
{
    if (!spellId || !pTarget || !m_player->HasSpell(spellId))
        return false;

    // 检查能量
    if (GetEnergyPowerPer() < 30) // 至少需要30%能量
        return false;

    // 检查冷却时间
    if (m_player->HasSpellCooldown(spellId))
        return false;

    // 检查距离
    if (!IsInMeleeRange(pTarget))
        return false;

    return true;
}

bool FieldRogueAI::IsInMeleeRange(Unit* pTarget)
{
    return pTarget && m_player->IsWithinMeleeRange(pTarget);
}

bool FieldRogueAI::IsBehindTarget(Unit* pTarget)
{
    return pTarget && m_player->isInBack(pTarget);
}

bool FieldRogueAI::HasSliceAndDice()
{
    return m_player->HasAura(RogueAttack_Separate); // 切割光环
}

void FieldRogueAI::ForceBasicAttack(Unit* pTarget)
{
    if (!pTarget || !m_player)
    {
        LOG_INFO("server", "ForceBasicAttack: 机器人或目标为空");
        return;
    }

    // 检查距离
    if (!m_player->IsWithinMeleeRange(pTarget))
    {
        LOG_INFO("server", "ForceBasicAttack: 机器人 {} 不在近战范围内，距离: {:.2f}", 
                 m_player->GetName(), m_player->GetDistance(pTarget));
        return;
    }

    // 方法1: 无条件强制执行攻击
    LOG_INFO("server", "ForceBasicAttack: 无条件强制执行AttackerStateUpdate");
    try 
    {
        // 强制重置攻击计时器
        m_player->resetAttackTimer(BASE_ATTACK);
        m_player->setAttackTimer(BASE_ATTACK, 0);
        
        // 直接执行攻击，不检查任何条件
        m_player->AttackerStateUpdate(pTarget);
        
        // 重置计时器为正常攻击间隔
        m_player->resetAttackTimer(BASE_ATTACK);
        
        LOG_INFO("server", "ForceBasicAttack: 强制攻击执行成功");
        return; // 成功执行，直接返回
    }
    catch (...)
    {
        LOG_ERROR("server", "ForceBasicAttack: 强制攻击执行异常");
    }

    // 方法2: 如果方法1失败，尝试通过Player::Attack重新建立攻击状态
    LOG_INFO("server", "ForceBasicAttack: 尝试重新建立攻击状态");
    if (Player* player = dynamic_cast<Player*>(m_player))
    {
        // 强制开始攻击
        bool attackResult = player->Attack(pTarget, true);
        LOG_INFO("server", "ForceBasicAttack: Attack()调用结果: {}", attackResult);
        
        // 如果Attack成功，再次尝试AttackerStateUpdate
        if (attackResult)
        {
            try 
            {
                player->AttackerStateUpdate(pTarget);
                LOG_INFO("server", "ForceBasicAttack: 玩家攻击模拟成功");
            }
            catch (...)
            {
                LOG_ERROR("server", "ForceBasicAttack: 玩家攻击模拟异常");
            }
        }
    }

    // 方法3: 最后的尝试 - 最简化的强制攻击
    LOG_INFO("server", "ForceBasicAttack: 最后尝试 - 最简化强制攻击");
    try 
    {
        // 最简化方法：只调用AttackerStateUpdate
        if (m_player->IsWithinMeleeRange(pTarget))
        {
            LOG_INFO("server", "ForceBasicAttack: 直接执行AttackerStateUpdate");
            m_player->AttackerStateUpdate(pTarget);
            LOG_INFO("server", "ForceBasicAttack: 最简化攻击执行成功");
        }
        else
        {
            LOG_INFO("server", "ForceBasicAttack: 机器人不在近战范围内，距离: {:.2f}", 
                     m_player->GetDistance(pTarget));
        }
    }
    catch (...)
    {
        LOG_ERROR("server", "ForceBasicAttack: 最简化攻击执行异常");
    }
}

std::string FieldRogueAI::GetSpellFailureReason(SpellCastResult result)
{
    switch (result)
    {
        case SPELL_CAST_OK: return "成功";
        case SPELL_FAILED_DONT_REPORT: return "不报告错误";
        case SPELL_FAILED_SPELL_LEARNED: return "技能未学会";
        case SPELL_FAILED_NOT_READY: return "技能未准备好";
        case SPELL_FAILED_NO_POWER: return "能量不足";
        case SPELL_FAILED_BAD_TARGETS: return "无效目标";
        case SPELL_FAILED_SPELL_UNAVAILABLE: return "技能不可用";
        case SPELL_FAILED_OUT_OF_RANGE: return "超出范围";
        case SPELL_FAILED_MOVING: return "正在移动";
        case SPELL_FAILED_NOT_INFRONT: return "未面向目标";
        default: return "未知错误(" + std::to_string(static_cast<int>(result)) + ")";
    }
}

uint32 FieldRogueAI::GetAuraRemainingTime(Unit* unit, uint32 spellId)
{
    if (!unit)
        return 0;

    if (Aura* aura = unit->GetAura(spellId))
    {
        return aura->GetDuration();
    }

    return 0;
}

bool FieldRogueAI::IsPoisonBuffExpiringSoon()
{
    if (!m_player)
        return false;

    // 检查主手武器毒药buff剩余时间
    Item* mainHand = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_MAINHAND);
    if (mainHand)
    {
        uint32 enchantId = mainHand->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
        if (enchantId > 0)
        {
            uint32 duration = mainHand->GetEnchantmentDuration(TEMP_ENCHANTMENT_SLOT);
            uint32 remainingMinutes = duration / 60000; // 转换为分钟

            LOG_DEBUG("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 主手武器毒药剩余时间: {}分钟", remainingMinutes);

            if (remainingMinutes < 5)
            {
                LOG_INFO("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 主手武器毒药即将过期");
                return true;
            }
        }
        else
        {
            LOG_DEBUG("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 主手武器没有毒药buff");
            return true; // 没有毒药buff也需要上毒
        }
    }

    // 检查副手武器毒药buff剩余时间
    Item* offHand = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_OFFHAND);
    if (offHand)
    {
        uint32 enchantId = offHand->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
        if (enchantId > 0)
        {
            uint32 duration = offHand->GetEnchantmentDuration(TEMP_ENCHANTMENT_SLOT);
            uint32 remainingMinutes = duration / 60000; // 转换为分钟

            LOG_DEBUG("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 副手武器毒药剩余时间: {}分钟", remainingMinutes);

            if (remainingMinutes < 5)
            {
                LOG_INFO("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 副手武器毒药即将过期");
                return true;
            }
        }
        else
        {
            LOG_DEBUG("server", "FieldRogueAI::IsPoisonBuffExpiringSoon: 副手武器没有毒药buff");
            return true; // 没有毒药buff也需要上毒
        }
    }

    return false;
}

// HasAuraMechanic方法已在BotRogueSpells基类中实现，这里删除重复定义