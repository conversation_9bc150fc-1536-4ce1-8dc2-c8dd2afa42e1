﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{C1B3B1F1-588C-3E0B-8C28-A2834C66B5BA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>common</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\common\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">common.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">common</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\common\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">common.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">common</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\common\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">common.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">common</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.lib</TargetExt>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\common\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">common.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">common</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.lib</TargetExt>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /bigobj /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/common/common.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/common/common.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/common/common.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/common/common.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;D:\keji\KejiBuild\src\common;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Lib>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\common\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/common/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/common/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/common/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/common/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/common/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/common/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/common/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/common/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\cmake_pch.cxx">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/common.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/common.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/common.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/common.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Banner.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Banner.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Common.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Common.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\CompilerDefs.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Define.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\GitRevision.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\GitRevision.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\AsioHacksFwd.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IoContext.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IpAddress.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IpNetwork.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\Resolver.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\SteadyTimer.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\Strand.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchy.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchy.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchyWrapper.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\DynamicTree.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\DynamicTree.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\RegularGrid.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\VMapDefinitions.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\VMapTools.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\IMMAPMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\IVMapMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapFactory.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapFactory.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapMgr.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapMgr.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapFactory.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapFactory.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapMgr2.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapMgr2.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapTree.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapTree.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\TileAssembler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\TileAssembler.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\GameObjectModel.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\GameObjectModel.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelIgnoreFlags.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelInstance.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelInstance.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\WorldModel.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\WorldModel.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Configuration\BuiltInConfig.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\BuiltInConfig.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Configuration\Config.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\Config.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\ConfigValueCache.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\AES.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\AES.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\ARC4.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\ARC4.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Argon2.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Argon2.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\BigNumber.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\BigNumber.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoConstants.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoGenerics.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoHash.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoRandom.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoRandom.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\HMAC.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\OpenSSLCrypto.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\OpenSSLCrypto.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\SessionKeyGenerator.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\TOTP.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\TOTP.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthCrypt.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthCrypt.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthDefines.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\SRP6.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\SRP6.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\DataStores\DBCFileLoader.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\DataStores\DBCFileLoader.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\FactoryHolder.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedList.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\ObjectRegistry.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainer.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerFunctions.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerFunctionsPtr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerVisitor.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeList.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference\RefMgr.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference\Reference.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base32.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base32.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base64.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base64.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\BaseEncoding.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\IPLocation\IPLocation.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\IPLocation\IPLocation.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Appender.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Appender.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderConsole.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderConsole.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderFile.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderFile.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Log.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Log.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogCommon.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\LogMessage.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogMessage.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\LogOperation.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogOperation.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Logger.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Logger.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\enuminfo_AppenderConsole.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\enuminfo_LogCommon.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Metric\Metric.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Metric\Metric.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Navigation\DetourExtended.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Navigation\DetourExtended.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\LockedQueue.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\MPSCQueue.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\PCQueue.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\PolicyLock.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Threading\ProcessPriority.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\ProcessPriority.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Threading\Threading.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\Threading.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\ThreadingModel.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\AsyncCallbackProcessor.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\ByteConverter.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\CircularBuffer.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Containers.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\DataMap.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Duration.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EnumFlag.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventEmitter.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventMap.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventMap.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventProcessor.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventProcessor.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Geometry.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\IteratorPair.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\MathUtil.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\MessageBuffer.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Optional.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Physics.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Random.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Random.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\SFMTRand.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SFMTRand.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SignalHandler.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SmartEnum.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\StartProcess.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StartProcess.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringConvert.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringFormat.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringFormat.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\TaskScheduler.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\TaskScheduler.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Timer.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Timer.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tokenize.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tokenize.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tuples.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Types.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Util.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Util.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\advstd.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Debugging\Errors.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/common/CMakeFiles/common.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Debugging\Errors.h" />
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\Debug\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\Release\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\MinSizeRel\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\RelWithDebInfo\cmake_pch.hxx" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\KejiBuild\ZERO_CHECK.vcxproj">
      <Project>{2B34230A-489D-329E-A203-4300066A20FA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{7762352D-F6A8-3C78-B40C-F6D9EC6696BB}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\argon2\argon2.vcxproj">
      <Project>{0193C8EB-A537-3520-85BF-0E144631F6EE}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\fmt\fmt.vcxproj">
      <Project>{7360C5EE-63A8-3467-9347-9D01A58DD42F}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\g3dlite\g3dlib.vcxproj">
      <Project>{81F9C921-10D9-36E3-88FE-F780EF060AE7}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\SFMT\sfmt.vcxproj">
      <Project>{125ADA47-4D19-3387-AB3D-C8276D002DDC}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\zlib\zlib.vcxproj">
      <Project>{792C559D-6BD0-3B63-9273-7B424A678DC9}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>