# YGbot天赋系统 - NFU天赋链接使用指南

## 🎯 **NFU天赋链接支持**

YGbot天赋系统**完全支持**NFU (nfuwow.com) 天赋链接，可以直接使用NFU网站生成的天赋链接为机器人配置天赋。

### ✅ **支持的功能**

1. **NFU链接自动识别** - 自动检测`nfuwow.com`链接格式
2. **16进制解析** - 支持NFU的16进制天赋编码
3. **多等级支持** - 支持不同等级的天赋配置
4. **自动补全** - 链接长度不足时自动补0
5. **完整验证** - 验证天赋点分配的合法性

## 🔧 **使用方法**

### 1. **获取NFU天赋链接**

1. 访问 [nfuwow.com](http://nfuwow.com) 天赋计算器
2. 选择职业和专精
3. 分配天赋点
4. 复制生成的链接

### 2. **应用天赋链接**

#### **命令行方式**
```bash
# 为目标机器人应用NFU天赋链接
.ygbot talent apply "http://nfuwow.com/talent?c=warrior&t=0505032000000000000000000000000000000000000000000000000000000000000000000"

# 为指定机器人应用天赋
.ygbot talent apply "nfuwow链接" 机器人名称
```

#### **配置文件方式**
在`mod_ygbot.conf.dist`中配置默认天赋：
```ini
# 战士防护专精NFU链接示例
YGbot.Talent.Warrior.Protection = "http://nfuwow.com/talent?c=warrior&t=0505032000000000000000000000000000000000000000000000000000000000000000000"

# 牧师神圣专精NFU链接示例
YGbot.Talent.Priest.Holy = "http://nfuwow.com/talent?c=priest&t=0500000000000000000000000000000000000000000000000000000000000000000000000"
```

### 3. **NFU链接格式说明**

#### **标准NFU链接格式**
```
http://nfuwow.com/talent?c=职业&t=天赋字符串
```

#### **天赋字符串解析**
- **长度**: 72个字符 (每个天赋树24个字符)
- **编码**: 16进制 (0-9, a-f)
- **顺序**: 天赋树1 + 天赋树2 + 天赋树3
- **每个字符**: 代表对应天赋的等级 (0-5)

#### **示例解析**
```
天赋字符串: 0505032000000000000000000000000000000000000000000000000000000000000000000
           |-------- 天赋树1 --------|-------- 天赋树2 --------|-------- 天赋树3 --------|

天赋树1: 050503200000000000000000
- 第1个天赋: 0级
- 第2个天赋: 5级
- 第3个天赋: 0级
- 第4个天赋: 5级
- 第5个天赋: 0级
- 第6个天赋: 3级
- 第7个天赋: 2级
- ...以此类推
```

## 📋 **新格式预设天赋配置示例**

### **配置文件格式 (推荐)**
```ini
# 战士预设天赋配置
YGbot.PremadeSpecName.1.0 = 武器
YGbot.PremadeSpecLink.1.0.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3020322023335100002012213231251305043000020000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.1.0 = 43418,43395,43423,43399,49084,43421

YGbot.PremadeSpecName.1.1 = 狂暴
YGbot.PremadeSpecLink.1.1.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3200320123300000000000000000000305053000500310053120511351000000000000000000000000000
YGbot.PremadeSpecGlyph.1.1 = 43418,43395,43414,43399,49084,43432

YGbot.PremadeSpecName.1.2 = 防护
YGbot.PremadeSpecLink.1.2.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3502000023000000000000000000000300000000000000000000000000053351225000212521030113321
YGbot.PremadeSpecGlyph.1.2 = 43424,43395,43425,43399,49084,45793
```

### **使用新格式的命令**
```bash
# 应用预设天赋 (会自动应用天赋和雕文)
.ygbot talent apply 武器
.ygbot talent apply 狂暴
.ygbot talent apply 防护

# 列出可用的预设天赋
.ygbot talent list
```

### **配置格式说明**
- `YGbot.PremadeSpecName.职业ID.专精ID = 专精名称`
- `YGbot.PremadeSpecLink.职业ID.专精ID.等级 = NFU天赋链接`
- `YGbot.PremadeSpecGlyph.职业ID.专精ID = 雕文ID列表(逗号分隔)`

### **职业ID对照表**
| 职业 | ID | 职业 | ID |
|------|----|----- |----|
| 战士 | 1 | 死亡骑士 | 6 |
| 圣骑士 | 2 | 萨满 | 7 |
| 猎人 | 3 | 法师 | 8 |
| 盗贼 | 4 | 术士 | 9 |
| 牧师 | 5 | 德鲁伊 | 11 |

### **法师 (Mage)**
```bash
# 奥术专精 (Arcane)
.ygbot talent apply "http://nfuwow.com/talent?c=mage&t=2300230100000000000000000000000000000000000000000000000000000000000000000"

# 火焰专精 (Fire)
.ygbot talent apply "http://nfuwow.com/talent?c=mage&t=0000000000000000000000002300230100000000000000000000000000000000000000000"

# 冰霜专精 (Frost)
.ygbot talent apply "http://nfuwow.com/talent?c=mage&t=0000000000000000000000000000000000000000000000000000002300230100000000000"
```

### **牧师 (Priest)**
```bash
# 戒律专精 (Discipline)
.ygbot talent apply "http://nfuwow.com/talent?c=priest&t=0500320100000000000000000000000000000000000000000000000000000000000000000"

# 神圣专精 (Holy)
.ygbot talent apply "http://nfuwow.com/talent?c=priest&t=0000000000000000000000000500320100000000000000000000000000000000000000000"

# 暗影专精 (Shadow)
.ygbot talent apply "http://nfuwow.com/talent?c=priest&t=0000000000000000000000000000000000000000000000000000000500320100000000000"
```

## ⚙️ **配置选项**

### **mod_ygbot.conf.dist 配置**
```ini
# 启用天赋系统
YGbot.Talent.Enable = 1

# 自动分配天赋
YGbot.Talent.AutoAssign = 1

# 自动重置天赋
YGbot.Talent.AutoReset = 0

# 队伍天赋优化
YGbot.Talent.GroupOptimize = 1

# 日志级别
YGbot.Talent.LogLevel = 1

# 自动学习技能
YGbot.Talent.AutoLearnSpells = 1
```

## 🔍 **故障排除**

### **常见问题**

#### **1. 天赋链接无效**
- 检查链接格式是否正确
- 确保包含完整的NFU域名
- 验证天赋字符串长度 (应为72字符)

#### **2. 天赋点不足**
- 检查机器人等级是否足够
- 确认天赋点数量是否充足
- 验证天赋前置条件

#### **3. 天赋应用失败**
- 检查机器人是否在线
- 确认机器人职业是否匹配
- 查看日志获取详细错误信息

### **调试命令**
```bash
# 查看机器人当前天赋
.ygbot talent show

# 重置机器人天赋
.ygbot talent reset

# 列出可用专精
.ygbot talent list

# 查看天赋点数量
.ygbot talent points
```

## 🎉 **总结**

YGbot天赋系统提供了完整的NFU天赋链接支持，让您可以：

1. **直接使用NFU链接** - 无需手动配置天赋
2. **支持所有职业** - 覆盖全部10个职业的所有专精
3. **自动化管理** - 支持自动分配和队伍优化
4. **灵活配置** - 支持命令行和配置文件两种方式

通过NFU天赋链接，您可以轻松为机器人配置专业的天赋加点，大大提升机器人的战斗效率！