﻿  ModulesLoader.cpp
  ElunaLuaEngine_SC.cpp
  eluna_loader.cpp
  ElunaCompat.cpp
  ElunaDBCRegistry.cpp
  ElunaEventMgr.cpp
  ElunaInstanceAI.cpp
  ElunaUtility.cpp
  HttpManager.cpp
  LuaEngine.cpp
  LuaFunctions.cpp
  lmarshal.cpp
  BattleGroundHooks.cpp
  CreatureHooks.cpp
  GameObjectHooks.cpp
  GossipHooks.cpp
  GroupHooks.cpp
  GuildHooks.cpp
  InstanceHooks.cpp
  ItemHooks.cpp
  PacketHooks.cpp
  PlayerHooks.cpp
  ServerHooks.cpp
  SpellHooks.cpp
  TicketHooks.cpp
  VehicleHooks.cpp
  BotAITool.cpp
  BotClassSpells.cpp
  BotCombatMovement.cpp
  BotSpellManager.cpp
  CombatSystemScript.cpp
  PlayerBotAI.cpp
  BotArenaAI.cpp
  BotBGAI.cpp
  BotDuelAI.cpp
  BotFieldAI.cpp
  FieldMageAI.cpp
  FieldPaladinAI.cpp
  FieldRogueAI.cpp
  FieldWarriorAI.cpp
  BotGroupAI.cpp
  GroupRogueAI.cpp
  BotDeathHandler.cpp
  BotAutoLearnSpells.cpp
  BotControlCommands.cpp
  BotGroupSyncScript.cpp
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(82,37): error C2065: “RogueAttack_Incision”: 未声明的标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(82,80): error C2065: “RogueAttack_Incision”: 未声明的标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(84,30): error C2065: “RogueAttack_Incision”: 未声明的标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1207,20): error C2509: “ProcessFlee”: 成员函数没有在“GroupRogueAI”中声明
      D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.h(7,7):
      参见“GroupRogueAI”的声明
  
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1263,20): error C2084: 函数“bool GroupRogueAI::CanStartSpell(void)”已有主体
      D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(667,20):
      参见“GroupRogueAI::CanStartSpell”的前一个定义
  
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1268,20): error C2084: 函数“void GroupRogueAI::UpEnergy(void)”已有主体
      D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(207,20):
      参见“GroupRogueAI::UpEnergy”的前一个定义
  
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1275,20): error C2039: "TryBlockCastingByTarget": 不是 "GroupRogueAI" 的成员
      D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.h(7,7):
      参见“GroupRogueAI”的声明
  
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,9): error C2065: “RogueAssist_BlockCast”: 未声明的标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,47): error C2065: “RogueAssist_BlockCast”: 未声明的标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,34): error C3861: “TryCastSpell”: 找不到标识符
D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,79): error C2677: 二进制“==”: 没有找到接受“SpellCastResult”类型的全局运算符(或没有可接受的转换)
      D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\GridDefines.h(156,6):
      可能是“bool operator ==(const CoordPair<LIMIT> &,const CoordPair<LIMIT> &)”
          D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,46):
          “bool operator ==(const CoordPair<LIMIT> &,const CoordPair<LIMIT> &)”: 无法从“<error type>”推导出“const CoordPair<LIMIT> &”的 模板 参数
      D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\GridDefines.h(156,6):
      或 "bool operator ==(const CoordPair<LIMIT> &,const CoordPair<LIMIT> &)" [综合表达式 "y == x"]
          D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI\GroupRogueAI.cpp(1279,46):
          “bool operator ==(const CoordPair<LIMIT> &,const CoordPair<LIMIT> &)”: 无法从“SpellCastResult”推导出“const CoordPair<LIMIT> &”的 模板 参数
  
  FakePlayers.cpp
  BotBehaviorEngine.cpp
  BotDuelPlayerScript.cpp
  BotEventHandlers.cpp
  BotEventSystem.cpp
  BotInteractionOpcodeHandler.cpp
  BotOpcodeHandler.cpp
  Faker.cpp
  InteractionResponseHandlerImpl.cpp
  BotMovementManager.cpp
  BotMovementTasks.cpp
  BotTerrainValidator.cpp
  MovementSystemScript.cpp
  BotAutoTalentGlyph.cpp
  YGbotGlyphCommands.cpp
  YGbotGlyphManager.cpp
  YGbotTalentCommands.cpp
  YGbotTalentDetector.cpp
  YGbotTalentManager.cpp
