﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\AABox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Any.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\AnyTableReader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\BinaryFormat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\BinaryInput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\BinaryOutput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Box.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Capsule.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\CollisionDetection.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\CoordinateFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Crypto.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Cylinder.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\debugAssert.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\FileSystem.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\fileutils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\format.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\g3dfnmatch.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\g3dmath.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\GThread.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Line.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\LineSegment.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Log.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Matrix3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Matrix4.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\MemoryManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\PhysicsFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Plane.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\prompt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Quat.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Random.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Ray.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\RegistryUtil.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Sphere.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\stringutils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\System.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\TextInput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\TextOutput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Triangle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\uint128.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\UprightFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Vector2.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Vector3.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\deps\g3dlite\source\Vector4.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\deps\g3dlite\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{13BB9762-CF46-3603-980C-A0F8777D13B0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
