#include "PlayerBotAI.h"
#include "Log.h"
#include "ObjectAccessor.h"

PlayerBotAI::PlayerBotAI(Player* player)
    : m_player(player)
    , m_isInCombat(false)
    , m_isFollowing(false)
    , m_workType(AIWT_ALL)
    , m_scenarioType(SCENARIO_FIELD)
    , m_lastUpdateTime(0)
    , m_lastCombatTime(0)
    , m_lastMoveTime(0)
    , m_lastSpellTime(0)
{
    if (!m_player)
    {
        LOG_ERROR("server", "PlayerBotAI: 创建AI时玩家对象为空");
        return;
    }

    // 初始化子系统
    m_spellManager = std::make_shared<BotSpellManager>(m_player);
    m_movementManager = std::make_shared<BotCombatMovement>(m_player);
    
    // 简化版本，不使用决策引擎

    LOG_INFO("server", "PlayerBotAI: 为玩家 {} 创建AI成功", m_player->GetName());
}

PlayerBotAI::~PlayerBotAI()
{
    LOG_INFO("server", "PlayerBotAI: 销毁玩家 {} 的AI", m_player ? m_player->GetName() : "Unknown");
}

void PlayerBotAI::UpdateAI(uint32 diff)
{
    if (!m_player || !m_player->IsInWorld())
        return;

    uint32 currentTime = getMSTime();
    
    // 检查更新间隔
    if (currentTime - m_lastUpdateTime < AI_UPDATE_INTERVAL)
        return;
    
    m_lastUpdateTime = currentTime;

    // 更新战斗状态
    bool wasInCombat = m_isInCombat;
    m_isInCombat = m_player->IsInCombat();
    
    if (!wasInCombat && m_isInCombat)
    {
        OnEnterCombat(m_player->GetVictim());
    }
    else if (wasInCombat && !m_isInCombat)
    {
        OnLeaveCombat();
    }

    // 更新子系统
    if (m_spellManager)
        m_spellManager->Update(diff);

    if (m_movementManager)
        m_movementManager->Update(diff);

    // 更新战斗逻辑
    if (m_isInCombat)
    {
        UpdateCombat(diff);
    }
    else
    {
        UpdateMovement(diff);

        // 非战斗上毒移至Faker系统统一管理，这里不再处理
    }

    // 简化版本，由子类实现UpdateBotAI
    // 🔧 确保调用子类的场景AI更新（如GroupRogueAI::UpdateBotAI），以驱动定时器等逻辑
    UpdateBotAI(diff);
}

void PlayerBotAI::UpdateCombat(uint32 diff)
{
    uint32 currentTime = getMSTime();
    
    if (currentTime - m_lastCombatTime < COMBAT_UPDATE_INTERVAL)
        return;
    
    m_lastCombatTime = currentTime;

    // 选择目标
    Unit* target = SelectTarget();
    if (!target)
        return;

    SetTarget(target);

    // 选择并释放技能
    uint32 spellId = SelectSpell(target);
    if (spellId)
    {
        CastSpell(spellId, target);
    }
}

void PlayerBotAI::OnEnterCombat(Unit* target)
{
    LOG_INFO("server", "PlayerBotAI: 玩家 {} 进入战斗", m_player->GetName());
    m_lastCombatTime = getMSTime();
}

void PlayerBotAI::OnLeaveCombat()
{
    LOG_INFO("server", "PlayerBotAI: 玩家 {} 离开战斗", m_player->GetName());
    m_targetGuid.Clear();
}

void PlayerBotAI::UpdateMovement(uint32 diff)
{
    uint32 currentTime = getMSTime();
    
    if (currentTime - m_lastMoveTime < MOVEMENT_UPDATE_INTERVAL)
        return;
    
    m_lastMoveTime = currentTime;

    // 如果在跟随状态
    if (m_isFollowing)
    {
        Player* followTarget = ObjectAccessor::GetPlayer(*m_player, m_followTargetGuid);
        if (followTarget)
        {
            Follow(followTarget);
        }
    }
}

void PlayerBotAI::MoveTo(const Position& pos)
{
    if (m_movementManager)
    {
        m_movementManager->MoveToPosition(pos.GetPositionX(), pos.GetPositionY(), pos.GetPositionZ());
    }
}

void PlayerBotAI::Follow(Player* target)
{
    if (!target)
        return;

    float distance = m_player->GetDistance(target);
    if (distance > FOLLOW_DISTANCE)
    {
        Position followPos = GetFormationPosition();
        MoveTo(followPos);
    }
}

bool PlayerBotAI::CastSpell(uint32 spellId, Unit* target)
{
    if (!CanCast(spellId, target))
        return false;

    if (m_spellManager)
    {
        return m_spellManager->TryCastSpell(spellId, target);
    }

    return false;
}

uint32 PlayerBotAI::SelectSpell(Unit* target)
{
    // 基础实现，子类应该重写
    if (m_spellManager)
    {
        return m_spellManager->GetNextSpell();
    }
    return 0;
}

Unit* PlayerBotAI::SelectTarget()
{
    // 优先攻击当前目标
    if (!m_targetGuid.IsEmpty())
    {
        Unit* currentTarget = ObjectAccessor::GetUnit(*m_player, m_targetGuid);
        if (currentTarget && currentTarget->IsAlive() && m_player->IsValidAttackTarget(currentTarget))
        {
            return currentTarget;
        }
    }

    // 寻找新目标
    return BotAITool::FindNearestEnemy(m_player, COMBAT_RANGE);
}

void PlayerBotAI::SetTarget(Unit* target)
{
    if (target)
    {
        m_targetGuid = target->GetGUID();
        m_player->SetTarget(target->GetGUID());
    }
    else
    {
        m_targetGuid.Clear();
        m_player->SetTarget(ObjectGuid::Empty);
    }
}

bool PlayerBotAI::CanCast(uint32 spellId, Unit* target) const
{
    if (!m_spellManager)
        return false;

    return m_spellManager->CanCastSpell(spellId, target);
}

bool PlayerBotAI::IsInRange(Unit* target, float range) const
{
    if (!target)
        return false;

    return m_player->GetDistance(target) <= range;
}

bool PlayerBotAI::NeedHeal(float healthPct) const
{
    return m_player->GetHealthPct() < healthPct;
}

bool PlayerBotAI::NeedMana(float manaPct) const
{
    if (m_player->GetMaxPower(POWER_MANA) == 0)
        return false;

    return m_player->GetPowerPct(POWER_MANA) < manaPct;
}

Group* PlayerBotAI::GetGroup() const
{
    return m_player->GetGroup();
}

Player* PlayerBotAI::GetGroupLeader() const
{
    Group* group = GetGroup();
    if (!group)
        return nullptr;

    return ObjectAccessor::GetPlayer(*m_player, group->GetLeaderGUID());
}

Position PlayerBotAI::GetFormationPosition() const
{
    Group* group = GetGroup();
    Player* leader = GetGroupLeader();
    
    if (!group || !leader)
        return m_player->GetPosition();

    ObjectGuid playerGuid = m_player->GetGUID();
    return BotAITool::GetPositionFromGroup(leader, playerGuid, group);
}

// 职责判定方法实现 - 使用TalentSystem
BotAIWorkType PlayerBotAI::DetermineWorkType() const
{
    if (!m_player)
        return AIWT_ALL;

    uint32 primarySpec = YGbotTalentDetector::DetectPrimaryTalentSpec(m_player);
    uint8 playerClass = m_player->getClass();

    switch (playerClass)
    {
        case CLASS_WARRIOR:
        {
            // 0=武器, 1=狂暴, 2=防护
            if (primarySpec == 2)
                return AIWT_TANK;
            return AIWT_MELEE;
        }
        case CLASS_PALADIN:
        {
            // 0=神圣, 1=防护, 2=惩戒
            if (primarySpec == 0)
                return AIWT_HEAL;
            if (primarySpec == 1)
                return AIWT_TANK;
            return AIWT_MELEE;
        }
        case CLASS_HUNTER:
            return AIWT_RANGE;
        case CLASS_ROGUE:
            return AIWT_MELEE;
        case CLASS_PRIEST:
        {
            // 0=戒律, 1=神圣, 2=暗影
            if (primarySpec == 2)
                return AIWT_RANGE;
            return AIWT_HEAL;
        }
        case CLASS_SHAMAN:
        {
            // 0=元素, 1=增强, 2=恢复
            if (primarySpec == 2)
                return AIWT_HEAL;
            if (primarySpec == 1)
                return AIWT_MELEE;
            return AIWT_RANGE;
        }
        case CLASS_MAGE:
        case CLASS_WARLOCK:
            return AIWT_RANGE;
        case CLASS_DRUID:
        {
            // 0=平衡, 1=野性, 2=恢复
            if (primarySpec == 2)
                return AIWT_HEAL;
            if (primarySpec == 1)
                return AIWT_MELEE;
            return AIWT_RANGE;
        }
        case CLASS_DEATH_KNIGHT:
        {
            // 0=血魄, 1=冰霜, 2=邪恶
            if (primarySpec == 0)
                return AIWT_TANK;
            return AIWT_MELEE;
        }
        default:
            return AIWT_ALL;
    }
}

bool PlayerBotAI::IsTankRole() const
{
    return DetermineWorkType() == AIWT_TANK;
}

bool PlayerBotAI::IsHealerRole() const
{
    return DetermineWorkType() == AIWT_HEAL;
}

bool PlayerBotAI::IsMeleeRole() const
{
    return DetermineWorkType() == AIWT_MELEE;
}

bool PlayerBotAI::IsRangeRole() const
{
    return DetermineWorkType() == AIWT_RANGE;
}

// 战斗控制方法实现
void PlayerBotAI::StartCombat(Unit* target)
{
    if (!target || !m_player)
        return;

    // 设置目标并开始攻击
    SetTarget(target);
    m_player->SetTarget(target->GetGUID());
    m_player->Attack(target, true);
    m_isInCombat = true;

    LOG_DEBUG("server", "PlayerBotAI::StartCombat: 机器人 {} 开始攻击目标 {}",
              m_player->GetName(), target->GetName());
}

void PlayerBotAI::StopCombat()
{
    if (!m_player)
        return;

    // 停止攻击并清除目标
    m_player->AttackStop();
    m_player->InterruptNonMeleeSpells(false);
    ClearTarget();
    m_isInCombat = false;

    LOG_DEBUG("server", "PlayerBotAI::StopCombat: 机器人 {} 停止战斗", m_player->GetName());
}

void PlayerBotAI::ClearTarget()
{
    if (!m_player)
        return;

    // 清除目标
    SetTarget(nullptr);
    m_player->SetTarget(ObjectGuid::Empty);

    LOG_DEBUG("server", "PlayerBotAI::ClearTarget: 机器人 {} 清除目标", m_player->GetName());
}

// PlayerBotAI工厂实现
#include "BotFieldAI/BotFieldAI.h"
#include "BotGroupAI/BotGroupAI.h"
#include "BotDuelAI/BotDuelAI.h"
#include "BotBGAI/BotBGAI.h"
#include "BotArenaAI/BotArenaAI.h"

std::unique_ptr<PlayerBotAI> PlayerBotAIFactory::CreateAI(Player* player)
{
    if (!player)
        return nullptr;

    // 根据场景选择AI类型
    BotAIScenarioType scenario = DetermineScenario(player);

    PlayerBotAI* pAI = nullptr;

    switch (scenario)
    {
        case SCENARIO_FIELD:
            pAI = BotFieldAI::CreateBotFieldAIByPlayerClass(player);
            break;
        case SCENARIO_GROUP:
            pAI = BotGroupAI::CreateBotGroupAIByPlayerClass(player);
            break;
        case SCENARIO_DUEL:
            pAI = BotDuelAI::CreateBotDuelAIByPlayerClass(player);
            break;
        case SCENARIO_BG:
            pAI = BotBGAI::CreateBotBGAIByPlayerClass(player);
            break;
        case SCENARIO_ARENA:
            pAI = BotArenaAI::CreateBotArenaAIByPlayerClass(player);
            break;
        default:
            LOG_ERROR("server", "PlayerBotAIFactory: 未知场景类型 {}", static_cast<int>(scenario));
            return nullptr;
    }

    return std::unique_ptr<PlayerBotAI>(pAI);
}

BotAIScenarioType PlayerBotAIFactory::DetermineScenario(Player* player)
{
    if (!player)
        return SCENARIO_FIELD;

    // 检查是否在战场
    if (player->GetBattleground())
    {
        if (player->GetBattleground()->isArena())
            return SCENARIO_ARENA;
        else
            return SCENARIO_BG;
    }

    // 检查是否在决斗
    if (player->duel && player->duel->State != DUEL_STATE_COMPLETED && player->duel->Opponent)
        return SCENARIO_DUEL;

    // 检查是否在组队
    if (player->GetGroup())
        return SCENARIO_GROUP;

    // 默认野外场景
    return SCENARIO_FIELD;
}

// ==================== 通用攻击系统实现 ====================
// 所有场景和职业都可以使用的攻击方法

void PlayerBotAI::DoUniversalAttack(Unit* pTarget)
{
    if (!pTarget || !m_player)
    {
        LOG_ERROR("server", "PlayerBotAI::DoUniversalAttack: 目标或玩家为空");
        return;
    }

    LOG_INFO("server", "PlayerBotAI::DoUniversalAttack: 机器人 {} (职业: {}) 开始通用攻击",
             m_player->GetName(), m_player->getClass());

    // 设置战斗状态
    if (!SetupCombatState(pTarget))
    {
        LOG_ERROR("server", "PlayerBotAI::DoUniversalAttack: 战斗状态设置失败");
        return;
    }

    // 根据职业和装备智能选择攻击方式
    uint8 playerClass = m_player->getClass();

    // 法系职业优先使用法术攻击
    if (playerClass == CLASS_MAGE || playerClass == CLASS_WARLOCK || playerClass == CLASS_PRIEST)
    {
        LOG_INFO("server", "PlayerBotAI::DoUniversalAttack: 法系职业，使用法术攻击");
        DoSpellAttackIfReady(pTarget);
        return;
    }

    // 猎人优先使用远程攻击
    if (playerClass == CLASS_HUNTER)
    {
        LOG_INFO("server", "PlayerBotAI::DoUniversalAttack: 猎人职业，使用远程攻击");
        DoRangedAttackIfReady(pTarget);
        return;
    }

    // 检查是否装备远程武器
    Item* rangedWeapon = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_RANGED);
    if (rangedWeapon && rangedWeapon->GetTemplate()->SubClass == ITEM_SUBCLASS_WEAPON_BOW)
    {
        float distance = m_player->GetDistance(pTarget);
        if (distance > 8.0f && distance < 30.0f) // 远程攻击距离
        {
            LOG_INFO("server", "PlayerBotAI::DoUniversalAttack: 装备远程武器，使用远程攻击");
            DoRangedAttackIfReady(pTarget);
            return;
        }
    }

    // 默认使用近战攻击
    LOG_INFO("server", "PlayerBotAI::DoUniversalAttack: 使用近战攻击");
    DoMeleeAttackIfReady(pTarget);
}

bool PlayerBotAI::SetupCombatState(Unit* pTarget)
{
    if (!pTarget || !m_player)
        return false;

    // 检查是否已经在正确的战斗状态中
    if (m_player->IsInCombat() && m_player->GetVictim() == pTarget)
    {
        LOG_DEBUG("server", "PlayerBotAI::SetupCombatState: 机器人 {} 已在正确战斗状态", m_player->GetName());
        return true; // 已经在正确的战斗状态，无需重复设置
    }

    LOG_INFO("server", "PlayerBotAI::SetupCombatState: 机器人 {} 设置战斗状态", m_player->GetName());

    // 确保机器人正在攻击目标
    if (!m_player->GetVictim() || m_player->GetVictim() != pTarget)
    {
        bool attackResult = m_player->Attack(pTarget, true);
        LOG_INFO("server", "PlayerBotAI::SetupCombatState: Attack()调用结果: {}", attackResult);

        if (!attackResult)
        {
            LOG_ERROR("server", "PlayerBotAI::SetupCombatState: Attack()调用失败");
            return false;
        }
    }

    // 设置完整的战斗状态（只在需要时设置）
    if (!m_player->IsInCombat())
    {
        LOG_INFO("server", "PlayerBotAI::SetupCombatState: 初始化战斗状态");
        m_player->SetInCombatWith(pTarget);
        pTarget->SetInCombatWith(m_player);
        m_player->AddThreat(pTarget, 1.0f);
        m_player->SetFacingToObject(pTarget);

        // 设置战斗标志
        m_player->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_IN_COMBAT);
        pTarget->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_IN_COMBAT);

        // 设置选择目标
        m_player->SetSelection(pTarget->GetGUID());

        LOG_INFO("server", "PlayerBotAI::SetupCombatState: 战斗状态初始化完成");
    }

    return true;
}

float PlayerBotAI::GetOptimalAttackRange()
{
    if (!m_player)
        return 5.0f;

    uint8 playerClass = m_player->getClass();

    // 法系职业
    if (playerClass == CLASS_MAGE || playerClass == CLASS_WARLOCK || playerClass == CLASS_PRIEST)
        return 25.0f; // 法术攻击距离

    // 猎人
    if (playerClass == CLASS_HUNTER)
        return 20.0f; // 远程攻击距离

    // 检查远程武器
    Item* rangedWeapon = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_RANGED);
    if (rangedWeapon && rangedWeapon->GetTemplate()->SubClass == ITEM_SUBCLASS_WEAPON_BOW)
        return 15.0f; // 弓箭攻击距离

    // 默认近战距离
    return 5.0f;
}

bool PlayerBotAI::IsInOptimalAttackRange(Unit* pTarget)
{
    if (!pTarget || !m_player)
        return false;

    float distance = m_player->GetDistance(pTarget);
    float optimalRange = GetOptimalAttackRange();

    LOG_INFO("server", "PlayerBotAI::IsInOptimalAttackRange: 距离 {:.2f}, 最佳距离 {:.2f}", distance, optimalRange);

    return distance <= optimalRange;
}

// 近战攻击实现
void PlayerBotAI::DoMeleeAttackIfReady(Unit* pTarget)
{
    if (!pTarget || !m_player)
    {
        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人或目标为空");
        return;
    }

    LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 开始检查近战攻击", m_player->GetName());

    // 检查是否正在施法
    if (m_player->HasUnitState(UNIT_STATE_CASTING))
    {
        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 正在施法，跳过攻击", m_player->GetName());
        return;
    }

    // 检查是否在近战范围内
    float distance = m_player->GetDistance(pTarget);
    bool inAttackRange = m_player->IsWithinMeleeRange(pTarget);

    LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 距离目标 {:.2f}码，在攻击范围内: {}",
             m_player->GetName(), distance, inAttackRange);

    if (!inAttackRange)
    {
        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 不在攻击范围内，距离: {:.2f}",
                 m_player->GetName(), distance);
        return;
    }

    // 检查攻击准备状态
    bool attackReady = m_player->isAttackReady();
    uint32 attackTimer = m_player->getAttackTimer(BASE_ATTACK);
    LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 攻击准备状态: {}, 攻击计时器: {}",
             m_player->GetName(), attackReady, attackTimer);

    // 如果攻击未准备好，强制重置计时器
    if (!attackReady)
    {
        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 攻击未准备好，强制重置计时器", m_player->GetName());
        m_player->resetAttackTimer(BASE_ATTACK);
        m_player->setAttackTimer(BASE_ATTACK, 0);
        attackReady = true; // 强制设置为准备好
    }

    // 执行主手攻击
    if (attackReady)
    {
        // 防止主手和副手同时攻击
        if (m_player->HasOffhandWeaponForAttack())
            if (m_player->getAttackTimer(OFF_ATTACK) < 200)
                m_player->setAttackTimer(OFF_ATTACK, 200);

        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 执行主手攻击", m_player->GetName());

        try
        {
            // 执行攻击
            m_player->AttackerStateUpdate(pTarget);
            LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 主手攻击执行成功", m_player->GetName());

            // 重置攻击计时器（参考mod-jbbot的正确做法）
            m_player->resetAttackTimer(BASE_ATTACK);

            // 设置下次攻击时间
            uint32 attackTime = m_player->GetAttackTime(BASE_ATTACK);
            m_player->setAttackTimer(BASE_ATTACK, attackTime);

            LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 设置下次攻击时间: {}ms", attackTime);

            // 确保机器人保持攻击状态（关键！）
            if (!m_player->HasUnitState(UNIT_STATE_MELEE_ATTACKING))
            {
                m_player->AddUnitState(UNIT_STATE_MELEE_ATTACKING);
                LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 重新添加近战攻击状态");
            }
        }
        catch (...)
        {
            LOG_ERROR("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 主手攻击执行异常", m_player->GetName());
        }
    }

    // 处理副手攻击
    if (m_player->HasOffhandWeaponForAttack() && m_player->isAttackReady(OFF_ATTACK))
    {
        // 延迟主手攻击如果两者会同时命中
        if (m_player->getAttackTimer(BASE_ATTACK) < 200)
            m_player->setAttackTimer(BASE_ATTACK, 200);

        LOG_INFO("server", "PlayerBotAI::DoMeleeAttackIfReady: 机器人 {} 执行副手攻击", m_player->GetName());
        m_player->AttackerStateUpdate(pTarget, OFF_ATTACK);
        m_player->resetAttackTimer(OFF_ATTACK);
    }
}

// 远程攻击实现
void PlayerBotAI::DoRangedAttackIfReady(Unit* pTarget)
{
    if (!pTarget || !m_player)
    {
        LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人或目标为空");
        return;
    }

    LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人 {} 开始检查远程攻击", m_player->GetName());

    // 检查是否装备远程武器
    Item* rangedWeapon = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_RANGED);
    if (!rangedWeapon)
    {
        LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人 {} 没有装备远程武器，回退到近战", m_player->GetName());
        DoMeleeAttackIfReady(pTarget);
        return;
    }

    // 检查距离
    float distance = m_player->GetDistance(pTarget);
    if (distance < 8.0f || distance > 30.0f)
    {
        LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人 {} 距离 {:.2f} 不适合远程攻击", m_player->GetName(), distance);
        if (distance < 8.0f)
            DoMeleeAttackIfReady(pTarget); // 距离太近，使用近战
        return;
    }

    // 检查远程攻击准备状态
    if (m_player->isAttackReady(RANGED_ATTACK))
    {
        LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人 {} 执行远程攻击", m_player->GetName());
        m_player->AttackerStateUpdate(pTarget, RANGED_ATTACK);
        m_player->resetAttackTimer(RANGED_ATTACK);
    }
    else
    {
        LOG_INFO("server", "PlayerBotAI::DoRangedAttackIfReady: 机器人 {} 远程攻击未准备好", m_player->GetName());
    }
}

// 法术攻击实现
void PlayerBotAI::DoSpellAttackIfReady(Unit* pTarget)
{
    if (!pTarget || !m_player)
    {
        LOG_INFO("server", "PlayerBotAI::DoSpellAttackIfReady: 机器人或目标为空");
        return;
    }

    LOG_INFO("server", "PlayerBotAI::DoSpellAttackIfReady: 机器人 {} 开始检查法术攻击", m_player->GetName());

    // 检查法力值
    uint32 manaPct = m_player->GetPowerPct(POWER_MANA);
    if (manaPct < 20)
    {
        LOG_INFO("server", "PlayerBotAI::DoSpellAttackIfReady: 机器人 {} 法力值不足 {}%，回退到近战", m_player->GetName(), manaPct);
        DoMeleeAttackIfReady(pTarget);
        return;
    }

    // 检查距离
    float distance = m_player->GetDistance(pTarget);
    if (distance > 30.0f)
    {
        LOG_INFO("server", "PlayerBotAI::DoSpellAttackIfReady: 机器人 {} 距离 {:.2f} 超出法术范围", m_player->GetName(), distance);
        return;
    }

    // 这里应该由具体的职业AI实现具体的法术攻击逻辑
    // 基类只提供框架，具体法术由子类实现
    LOG_INFO("server", "PlayerBotAI::DoSpellAttackIfReady: 机器人 {} 法术攻击需要子类实现", m_player->GetName());

    // 如果没有法术可用，回退到近战
    DoMeleeAttackIfReady(pTarget);
}
