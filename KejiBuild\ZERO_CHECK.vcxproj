﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{2B34230A-489D-329E-A203-4300066A20FA}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\KejiBuild\CMakeFiles\2f9373463ec853646d4c9d8e2e77fe21\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/keji/KejiBuild/AzerothCore.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\CMakeLists.txt;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\SFMT\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\deps\argon2\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\boost\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\fmt\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\g3dlite\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gperftools\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gsoap\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\jemalloc\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\mysql\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\openssl\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\readline\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Recast\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\stdfs\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\threads\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\utf8cpp\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\zlib\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\ModulesLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\CMakeLists.txt;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindFilesystem.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindOpenSSL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;D:\keji\azerothcore-pbot\src\common\CMakeLists.txt;D:\keji\azerothcore-pbot\src\genrev\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\ScriptLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\server\shared\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\SFMT\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\argon2\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\boost\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\fmt\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\jemalloc\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\openssl\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\stdfs\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\threads\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\utf8cpp\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\mysql\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\zlib\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\g3dlite\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Detour\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Recast\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\readline\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gsoap\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gperftools\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\genrev\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\shared\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\scripts\CMakeFiles\generate.stamp;D:\keji\KejiBuild\modules\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/keji/KejiBuild/AzerothCore.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\CMakeLists.txt;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\SFMT\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\deps\argon2\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\boost\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\fmt\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\g3dlite\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gperftools\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gsoap\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\jemalloc\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\mysql\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\openssl\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\readline\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Recast\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\stdfs\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\threads\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\utf8cpp\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\zlib\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\ModulesLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\CMakeLists.txt;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindFilesystem.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindOpenSSL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;D:\keji\azerothcore-pbot\src\common\CMakeLists.txt;D:\keji\azerothcore-pbot\src\genrev\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\ScriptLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\server\shared\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\SFMT\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\argon2\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\boost\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\fmt\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\jemalloc\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\openssl\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\stdfs\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\threads\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\utf8cpp\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\mysql\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\zlib\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\g3dlite\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Detour\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Recast\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\readline\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gsoap\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gperftools\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\genrev\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\shared\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\scripts\CMakeFiles\generate.stamp;D:\keji\KejiBuild\modules\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/keji/KejiBuild/AzerothCore.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\CMakeLists.txt;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\SFMT\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\deps\argon2\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\boost\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\fmt\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\g3dlite\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gperftools\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gsoap\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\jemalloc\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\mysql\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\openssl\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\readline\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Recast\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\stdfs\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\threads\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\utf8cpp\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\zlib\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\ModulesLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\CMakeLists.txt;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindFilesystem.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindOpenSSL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;D:\keji\azerothcore-pbot\src\common\CMakeLists.txt;D:\keji\azerothcore-pbot\src\genrev\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\ScriptLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\server\shared\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\SFMT\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\argon2\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\boost\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\fmt\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\jemalloc\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\openssl\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\stdfs\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\threads\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\utf8cpp\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\mysql\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\zlib\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\g3dlite\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Detour\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Recast\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\readline\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gsoap\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gperftools\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\genrev\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\shared\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\scripts\CMakeFiles\generate.stamp;D:\keji\KejiBuild\modules\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/keji/KejiBuild/AzerothCore.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakePushCheckState.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFile.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckSymbolExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindBoost.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\SelectLibraryConfigurations.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\CMakeLists.txt;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\SFMT\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\deps\argon2\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\boost\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\fmt\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\g3dlite\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gperftools\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\gsoap\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\jemalloc\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\mysql\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\openssl\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\readline\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\recastnavigation\Recast\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\stdfs\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\threads\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\utf8cpp\CMakeLists.txt;D:\keji\azerothcore-pbot\deps\zlib\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\CMakeLists.txt;D:\keji\azerothcore-pbot\modules\ModulesLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\CMakeLists.txt;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindFilesystem.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindOpenSSL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;D:\keji\azerothcore-pbot\src\common\CMakeLists.txt;D:\keji\azerothcore-pbot\src\genrev\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\database\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\CMakeLists.txt;D:\keji\azerothcore-pbot\src\server\scripts\ScriptLoader.cpp.in.cmake;D:\keji\azerothcore-pbot\src\server\shared\CMakeLists.txt;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\SFMT\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\argon2\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\boost\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\fmt\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\jemalloc\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\openssl\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\stdfs\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\threads\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\utf8cpp\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\mysql\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\zlib\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\g3dlite\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Detour\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\recastnavigation\Recast\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\readline\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gsoap\CMakeFiles\generate.stamp;D:\keji\KejiBuild\deps\gperftools\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\common\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\genrev\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\database\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\shared\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\game\CMakeFiles\generate.stamp;D:\keji\KejiBuild\src\server\scripts\CMakeFiles\generate.stamp;D:\keji\KejiBuild\modules\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>