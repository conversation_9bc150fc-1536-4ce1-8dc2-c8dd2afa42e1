# 分层AI系统扩展指南

## 概述

分层AI架构战斗系统现已完成核心实现，包含完整的战士AI和示例圣骑士AI。本指南说明如何扩展系统以支持更多职业和场景。

## 当前实现状态

### ✅ 已完成
- **核心架构** - 四层AI架构完整实现
- **战士AI** - 三个特化完整支持
- **野外PvE场景** - 四个AI层级完整实现
- **圣骑士AI** - 基础框架和示例实现
- **系统初始化** - 自动注册和管理

### 🚧 框架就绪
- 其他8个职业的AI框架
- 其他5个场景的AI框架
- 扩展接口和工具类

## 添加新职业AI

### 1. 创建职业AI文件

#### 文件结构
```
src/CombatSystem/AI/Classes/
├── NewClassAI.h        # 头文件
└── NewClassAI.cpp      # 实现文件
```

#### 头文件模板 (NewClassAI.h)
```cpp
#pragma once

#include "BotAICore.h"
#include "Player.h"

// 职业技能常量
namespace NewClassSpells
{
    const uint32 SPELL_1 = 12345;
    const uint32 SPELL_2 = 12346;
    // ... 更多技能
}

// 职业特化枚举
enum class NewClassSpecialization
{
    SPEC_1 = 0,
    SPEC_2 = 1,
    SPEC_3 = 2
};

// 职业AI基类
class NewClassAI : public IClassAI
{
public:
    // IClassAI 接口实现
    bool Initialize(Player* bot, BotCombatAI* combatAI) override;
    std::vector<std::shared_ptr<IAILayer>> CreateAILayers() override;
    ClassSpecialization DetectSpecialization() override;
    void Reset() override;
    
private:
    Player* m_bot = nullptr;
    BotCombatAI* m_combatAI = nullptr;
    NewClassSpecialization m_specialization;
};

// 操作层AI
class NewClassOperationalAI : public IAILayer
{
    // 实现IAILayer接口...
};

// 战术层AI
class NewClassTacticalAI : public IAILayer
{
    // 实现IAILayer接口...
};

// 反应层AI
class NewClassReactiveAI : public IAILayer
{
    // 实现IAILayer接口...
};

// 注册器
class NewClassAIRegistrar
{
public:
    static void RegisterAI();
};
```

### 2. 实现职业AI逻辑

#### 关键实现点
1. **特化检测** - 根据装备、天赋、技能检测特化
2. **技能轮换** - 每个特化的最佳技能使用顺序
3. **资源管理** - 法力、怒气、能量等资源的管理
4. **紧急反应** - 低血量、控制效果等的应对

#### 示例实现 (参考PaladinAI.cpp)
```cpp
ClassSpecialization NewClassAI::DetectSpecialization()
{
    // 检测逻辑
    if (HasSpecificSpell())
        return ClassSpecialization::NEWCLASS_SPEC1;
    
    return ClassSpecialization::NEWCLASS_SPEC2;
}

std::vector<AIDecision> NewClassOperationalAI::GetDecisions()
{
    std::vector<AIDecision> decisions;
    
    // 根据特化获取技能轮换
    switch (m_specialization)
    {
        case NewClassSpecialization::SPEC_1:
            decisions = GetSpec1Rotation();
            break;
        // ... 其他特化
    }
    
    return decisions;
}
```

### 3. 注册新职业AI

#### 在CMakeLists.txt中添加
```cmake
# 职业AI文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/AI/Classes/NewClassAI.cpp")
```

#### 在BotAIInitializer.cpp中注册
```cpp
void BotAIInitializer::RegisterClassAIs()
{
    // 现有注册...
    
    // 注册新职业AI
    NewClassAIRegistrar::RegisterAI();
}
```

#### 在BotAIInitializer.h中包含
```cpp
#include "Classes/NewClassAI.h"
```

## 添加新场景AI

### 1. 创建场景AI文件

#### 文件结构
```
src/CombatSystem/AI/Scenarios/
├── NewScenarioAI.h     # 头文件
└── NewScenarioAI.cpp   # 实现文件
```

#### 场景AI模板
```cpp
// 场景AI基类
class NewScenarioAI : public IScenarioAI
{
public:
    // IScenarioAI 接口实现
    bool IsApplicable(Player* bot) override;
    std::vector<std::shared_ptr<IAILayer>> CreateAILayers() override;
    CombatScenario GetScenario() const override;
};

// 场景特定的四个AI层级
class NewScenarioStrategicAI : public IAILayer { /* ... */ };
class NewScenarioTacticalAI : public IAILayer { /* ... */ };
class NewScenarioOperationalAI : public IAILayer { /* ... */ };
class NewScenarioReactiveAI : public IAILayer { /* ... */ };
```

### 2. 实现场景检测逻辑

```cpp
bool NewScenarioAI::IsApplicable(Player* bot)
{
    // 场景检测逻辑
    if (bot->InBattleground())
        return GetScenario() == CombatScenario::BATTLEGROUND;
    
    if (bot->IsInArena())
        return GetScenario() == CombatScenario::ARENA;
    
    return false;
}
```

### 3. 注册新场景AI

类似职业AI的注册过程，在相应文件中添加注册代码。

## 扩展现有AI

### 1. 完善战士AI

#### 添加更多技能
```cpp
namespace WarriorSpells
{
    // 现有技能...
    
    // 新增技能
    const uint32 BERSERKER_RAGE = 18499;
    const uint32 PUMMEL = 6552;
    const uint32 SPELL_REFLECTION = 23920;
}
```

#### 完善技能轮换
```cpp
std::vector<AIDecision> WarriorOperationalAI::GetArmsRotation()
{
    std::vector<AIDecision> decisions;
    
    // 现有轮换...
    
    // 新增技能逻辑
    if (ShouldUseBerserkerRage())
    {
        // 添加狂暴之怒决策
    }
    
    return decisions;
}
```

### 2. 完善圣骑士AI

圣骑士AI目前只有基础框架，可以：
- 完善技能轮换逻辑
- 添加更多特化特定技能
- 实现团队治疗逻辑
- 添加保护队友功能

## 性能优化建议

### 1. 更新频率优化
```cpp
// 根据AI层级调整更新频率
void AILayer::Update(uint32 diff)
{
    uint32 updateInterval = GetUpdateInterval();
    if (currentTime < m_lastUpdate + updateInterval)
        return;
    
    // 执行更新逻辑...
}

uint32 GetUpdateInterval()
{
    switch (GetLayer())
    {
        case AILayer::REACTIVE: return 100;    // 0.1秒
        case AILayer::OPERATIONAL: return 300; // 0.3秒
        case AILayer::TACTICAL: return 1000;   // 1秒
        case AILayer::STRATEGIC: return 2000;  // 2秒
    }
}
```

### 2. 决策缓存
```cpp
class AIDecisionCache
{
private:
    std::unordered_map<std::string, AIDecision> m_cache;
    uint32 m_cacheTimeout = 5000; // 5秒缓存
    
public:
    AIDecision GetCachedDecision(const std::string& key);
    void CacheDecision(const std::string& key, const AIDecision& decision);
};
```

### 3. 条件预检查
```cpp
std::vector<AIDecision> GetDecisions()
{
    // 快速预检查，避免不必要的计算
    if (!m_bot || !m_combatAI || !m_enabled)
        return {};
    
    if (!HasValidTarget())
        return {};
    
    // 执行决策逻辑...
}
```

## 调试和测试

### 1. 调试日志
```cpp
// 在关键位置添加调试日志
LOG_DEBUG("server", "NewClassAI: 检测到特化 {}, 生成 {} 个决策", 
          static_cast<int>(m_specialization), decisions.size());
```

### 2. 性能监控
```cpp
class AIPerformanceMonitor
{
public:
    void StartTimer(const std::string& operation);
    void EndTimer(const std::string& operation);
    void LogPerformanceStats();
};
```

### 3. 单元测试
```cpp
// 创建AI测试用例
TEST(NewClassAI, SpecializationDetection)
{
    // 测试特化检测逻辑
}

TEST(NewClassAI, SkillRotation)
{
    // 测试技能轮换逻辑
}
```

## 最佳实践

### 1. 代码组织
- 每个职业一个独立的文件夹
- 技能常量统一管理
- 接口和实现分离

### 2. 配置管理
```cpp
// 使用配置文件管理AI参数
class AIConfig
{
public:
    static float GetHealthThreshold(const std::string& ability);
    static uint32 GetCooldownOverride(uint32 spellId);
    static bool IsAbilityEnabled(const std::string& ability);
};
```

### 3. 错误处理
```cpp
// 完善的错误处理
try
{
    auto decisions = GetDecisions();
    return ExecuteBestDecision(decisions);
}
catch (const std::exception& e)
{
    LOG_ERROR("server", "AI决策异常: {}", e.what());
    return false;
}
```

## 总结

分层AI系统提供了强大的扩展能力：

1. **模块化设计** - 每个职业和场景独立实现
2. **标准化接口** - 统一的AI层级接口
3. **灵活配置** - 支持运行时调整和优化
4. **易于测试** - 清晰的职责分离便于单元测试

通过遵循本指南，可以轻松添加新的职业AI和场景AI，为机器人提供更智能的战斗行为。
