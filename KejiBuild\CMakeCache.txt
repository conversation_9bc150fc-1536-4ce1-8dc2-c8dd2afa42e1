# This is the CMakeCache file.
# For build in directory: d:/keji/KejiBuild
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build list for applications
APPS_BUILD:STRING=all

//Enable build the authserver application.
APP_AUTHSERVER:STRING=default

//Enable build the worldserver application.
APP_WORLDSERVER:STRING=default

//Value Computed by CMake
AzerothCore_BINARY_DIR:STATIC=D:/keji/KejiBuild

//Value Computed by CMake
AzerothCore_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
AzerothCore_SOURCE_DIR:STATIC=D:/keji/azerothcore-pbot

//Build unit tests
BUILD_TESTING:BOOL=OFF

//Boost atomic library (debug)
Boost_ATOMIC_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_atomic-vc143-mt-gd-x64-1_81.lib

//Boost atomic library (release)
Boost_ATOMIC_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_atomic-vc143-mt-x64-1_81.lib

//Boost chrono library (debug)
Boost_CHRONO_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_chrono-vc143-mt-gd-x64-1_81.lib

//Boost chrono library (release)
Boost_CHRONO_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_chrono-vc143-mt-x64-1_81.lib

//The directory containing a CMake configuration file for Boost.
Boost_DIR:PATH=Boost_DIR-NOTFOUND

//Boost filesystem library (debug)
Boost_FILESYSTEM_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_filesystem-vc143-mt-gd-x64-1_81.lib

//Boost filesystem library (release)
Boost_FILESYSTEM_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_filesystem-vc143-mt-x64-1_81.lib

//Path to a file.
Boost_INCLUDE_DIR:PATH=C:/local/boost_1_81_0

//Boost iostreams library (debug)
Boost_IOSTREAMS_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_iostreams-vc143-mt-gd-x64-1_81.lib

//Boost iostreams library (release)
Boost_IOSTREAMS_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_iostreams-vc143-mt-x64-1_81.lib

//Boost library directory DEBUG
Boost_LIBRARY_DIR_DEBUG:PATH=C:/local/boost_1_81_0/lib64-msvc-14.3

//Boost library directory RELEASE
Boost_LIBRARY_DIR_RELEASE:PATH=C:/local/boost_1_81_0/lib64-msvc-14.3

//Boost program_options library (debug)
Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_program_options-vc143-mt-gd-x64-1_81.lib

//Boost program_options library (release)
Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_program_options-vc143-mt-x64-1_81.lib

//Boost regex library (debug)
Boost_REGEX_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_regex-vc143-mt-gd-x64-1_81.lib

//Boost regex library (release)
Boost_REGEX_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_regex-vc143-mt-x64-1_81.lib

//Boost system library (debug)
Boost_SYSTEM_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_system-vc143-mt-gd-x64-1_81.lib

//Boost system library (release)
Boost_SYSTEM_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_system-vc143-mt-x64-1_81.lib

//Boost thread library (debug)
Boost_THREAD_LIBRARY_DEBUG:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_thread-vc143-mt-gd-x64-1_81.lib

//Boost thread library (release)
Boost_THREAD_LIBRARY_RELEASE:FILEPATH=C:/local/boost_1_81_0/lib64-msvc-14.3/libboost_thread-vc143-mt-x64-1_81.lib

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/lib.exe

//Semicolon separated list of supported configuration types, only
// supports Debug, Release, MinSizeRel, and RelWithDebInfo, anything
// else will be ignored.
CMAKE_CONFIGURATION_TYPES:STRING=Debug;Release;MinSizeRel;RelWithDebInfo

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=/DWIN32 /D_WINDOWS /EHsc

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=/DWIN32 /D_WINDOWS

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=/Zi /Ob0 /Od /RTC1

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=/O1 /Ob1 /DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=/O2 /Ob2 /DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=/Zi /O2 /Ob1 /DNDEBUG

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=D:/keji/KejiBuild/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/AzerothCore

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/link.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//Path to a program.
CMAKE_MT:FILEPATH=CMAKE_MT-NOTFOUND

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=AzerothCore

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=3.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=3

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=rc

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=-DWIN32

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=-D_DEBUG

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=/debug /INCREMENTAL

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=/INCREMENTAL:NO

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=/debug /INCREMENTAL

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=OFF

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=OFF

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=/machine:x64

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=OFF

//Enable abort if core found incorrect option in config files
CONFIG_ABORT_INCORRECT_OPTIONS:BOOL=OFF

//TRUE if we have the C++ filesystem headers
CXX_FILESYSTEM_HAVE_FS:BOOL=ON

//The header that should be included to obtain the filesystem APIs
CXX_FILESYSTEM_HEADER:STRING=filesystem

//The C++ namespace that contains the filesystem APIs
CXX_FILESYSTEM_NAMESPACE:STRING=std::filesystem

//Enable Checks relative to DisableMgr system on vmap
ENABLE_VMAP_CHECKS:BOOL=ON

//TRUE if we can compile and link a program using std::filesystem
Filesystem_FOUND:BOOL=TRUE

//Full path to git commandline client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//Path to a library.
LIB_EAY_DEBUG:FILEPATH=C:/Program Files/OpenSSL-Win64/lib/VC/x64/MDd/libcrypto.lib

//Path to a library.
LIB_EAY_RELEASE:FILEPATH=C:/Program Files/OpenSSL-Win64/lib/VC/x64/MD/libcrypto.lib

//Build core with modules
MODULES:STRING=static

//Build type of the mod-pbot module.
MODULE_MOD-PBOT:STRING=default

//path to your mysql binary.
MYSQL_EXECUTABLE:FILEPATH=C:/mysql/bin/mysql.exe

//Specify the directory containing mysql.h.
MYSQL_INCLUDE_DIR:PATH=C:/mysql/include

//Specify the location of the mysql library here.
MYSQL_LIBRARY:FILEPATH=C:/mysql/lib/libmysql.lib

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=C:/Program Files/OpenSSL-Win64/include

//Build core with scripts
SCRIPTS:STRING=static

//Build type of the Commands module.
SCRIPTS_COMMANDS:STRING=default

//Build type of the Custom module.
SCRIPTS_CUSTOM:STRING=default

//Build type of the EasternKingdoms module.
SCRIPTS_EASTERNKINGDOMS:STRING=default

//Build type of the Events module.
SCRIPTS_EVENTS:STRING=default

//Build type of the Kalimdor module.
SCRIPTS_KALIMDOR:STRING=default

//Build type of the Northrend module.
SCRIPTS_NORTHREND:STRING=default

//Build type of the OutdoorPvP module.
SCRIPTS_OUTDOORPVP:STRING=default

//Build type of the Outland module.
SCRIPTS_OUTLAND:STRING=default

//Build type of the Pet module.
SCRIPTS_PET:STRING=default

//Build type of the Spells module.
SCRIPTS_SPELLS:STRING=default

//Build type of the World module.
SCRIPTS_WORLD:STRING=default

//Path to a library.
SSL_EAY_DEBUG:FILEPATH=C:/Program Files/OpenSSL-Win64/lib/VC/x64/MDd/libssl.lib

//Path to a library.
SSL_EAY_RELEASE:FILEPATH=C:/Program Files/OpenSSL-Win64/lib/VC/x64/MD/libssl.lib

//Build list for tools
TOOLS_BUILD:STRING=none

//Enable build the dbimport tool.
TOOL_DBIMPORT:STRING=default

//Enable build the map_extractor tool.
TOOL_MAP_EXTRACTOR:STRING=default

//Enable build the mmaps_generator tool.
TOOL_MMAPS_GENERATOR:STRING=default

//Enable build the vmap4_assembler tool.
TOOL_VMAP4_ASSEMBLER:STRING=default

//Enable build the vmap4_extractor tool.
TOOL_VMAP4_EXTRACTOR:STRING=default

//Use precompiled headers when compiling servers
USE_COREPCH:BOOL=ON

//Use included MySQL-sources to build libraries
USE_MYSQL_SOURCES:BOOL=OFF

//Use precompiled headers when compiling scripts
USE_SCRIPTPCH:BOOL=ON

//Disable the GIT testing routines
WITHOUT_GIT:BOOL=OFF

//Disable metrics reporting (i.e. InfluxDB and Grafana)
WITHOUT_METRICS:BOOL=OFF

//Include additional debug-code in core
WITH_COREDEBUG:BOOL=OFF

//Enable detailed metrics reporting (i.e. time each session takes
// to update)
WITH_DETAILED_METRICS:BOOL=OFF

//Enable dynamic library linking.
WITH_DYNAMIC_LINKING:BOOL=OFF

//Enable compilation with gperftools libraries included
WITH_PERFTOOLS:BOOL=OFF

//Build the source tree for IDE's.
WITH_SOURCE_TREE:STRING=hierarchical

//Enable strict checking of database field value accessors
WITH_STRICT_DATABASE_TYPE_CHECKS:BOOL=OFF

//Show all warnings during compile
WITH_WARNINGS:BOOL=OFF


########################
# INTERNAL cache entries
########################

//STRINGS property for variable: APPS_BUILD
APPS_BUILD-STRINGS:INTERNAL=none;all;auth-only;world-only
//STRINGS property for variable: APP_AUTHSERVER
APP_AUTHSERVER-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: APP_WORLDSERVER
APP_WORLDSERVER-STRINGS:INTERNAL=default;enabled;disabled
//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_DEBUG
Boost_ATOMIC_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_ATOMIC_LIBRARY_RELEASE
Boost_ATOMIC_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_DEBUG
Boost_CHRONO_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_CHRONO_LIBRARY_RELEASE
Boost_CHRONO_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_DIR
Boost_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_DEBUG
Boost_FILESYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_FILESYSTEM_LIBRARY_RELEASE
Boost_FILESYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_INCLUDE_DIR
Boost_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_DEBUG
Boost_IOSTREAMS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_IOSTREAMS_LIBRARY_RELEASE
Boost_IOSTREAMS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_DEBUG
Boost_LIBRARY_DIR_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_LIBRARY_DIR_RELEASE
Boost_LIBRARY_DIR_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG
Boost_PROGRAM_OPTIONS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE
Boost_PROGRAM_OPTIONS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_DEBUG
Boost_REGEX_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_REGEX_LIBRARY_RELEASE
Boost_REGEX_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_DEBUG
Boost_SYSTEM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_SYSTEM_LIBRARY_RELEASE
Boost_SYSTEM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_DEBUG
Boost_THREAD_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: Boost_THREAD_LIBRARY_RELEASE
Boost_THREAD_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=d:/keji/KejiBuild
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=31
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=3
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Visual Studio 17 2022
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=C:/Program Files/Microsoft Visual Studio/2022/Community
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=
//Have library pthreads
CMAKE_HAVE_PTHREADS_CREATE:INTERNAL=
//Have library pthread
CMAKE_HAVE_PTHREAD_CREATE:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=D:/keji/azerothcore-pbot
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MT
CMAKE_MT-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=30
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//noop for ranlib
CMAKE_RANLIB:INTERNAL=:
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.31
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Test CXX_FILESYSTEM_NO_LINK_NEEDED
CXX_FILESYSTEM_NO_LINK_NEEDED:INTERNAL=1
//Details about finding Boost
FIND_PACKAGE_MESSAGE_DETAILS_Boost:INTERNAL=[C:/local/boost_1_81_0][cfound components: system filesystem program_options iostreams regex thread chrono atomic ][v1.81.0(1.78)]
//Details about finding MySQL
FIND_PACKAGE_MESSAGE_DETAILS_MySQL:INTERNAL=[c ][v()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[optimized;C:/Program Files/OpenSSL-Win64/lib/VC/x64/MD/libcrypto.lib;debug;C:/Program Files/OpenSSL-Win64/lib/VC/x64/MDd/libcrypto.lib][C:/Program Files/OpenSSL-Win64/include][cfound components: Crypto SSL ][v3.4.0()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Have symbol _strtod_l
HAVE_STRTOD_L:INTERNAL=1
//STRINGS property for variable: MODULES
MODULES-STRINGS:INTERNAL=none;static;dynamic
//STRINGS property for variable: MODULE_MOD-PBOT
MODULE_MOD-PBOT-STRINGS:INTERNAL=default;disabled;static;dynamic
//ADVANCED property for variable: MYSQL_EXECUTABLE
MYSQL_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MYSQL_INCLUDE_DIR
MYSQL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: MYSQL_LIBRARY
MYSQL_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//STRINGS property for variable: SCRIPTS
SCRIPTS-STRINGS:INTERNAL=none;static;dynamic;minimal-static;minimal-dynamic
//STRINGS property for variable: SCRIPTS_COMMANDS
SCRIPTS_COMMANDS-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_CUSTOM
SCRIPTS_CUSTOM-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_EASTERNKINGDOMS
SCRIPTS_EASTERNKINGDOMS-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_EVENTS
SCRIPTS_EVENTS-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_KALIMDOR
SCRIPTS_KALIMDOR-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_NORTHREND
SCRIPTS_NORTHREND-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_OUTDOORPVP
SCRIPTS_OUTDOORPVP-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_OUTLAND
SCRIPTS_OUTLAND-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_PET
SCRIPTS_PET-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_SPELLS
SCRIPTS_SPELLS-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: SCRIPTS_WORLD
SCRIPTS_WORLD-STRINGS:INTERNAL=default;disabled;static;dynamic
//STRINGS property for variable: TOOLS_BUILD
TOOLS_BUILD-STRINGS:INTERNAL=none;all;db-only;maps-only
//STRINGS property for variable: TOOL_DBIMPORT
TOOL_DBIMPORT-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: TOOL_MAP_EXTRACTOR
TOOL_MAP_EXTRACTOR-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: TOOL_MMAPS_GENERATOR
TOOL_MMAPS_GENERATOR-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: TOOL_VMAP4_ASSEMBLER
TOOL_VMAP4_ASSEMBLER-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: TOOL_VMAP4_EXTRACTOR
TOOL_VMAP4_EXTRACTOR-STRINGS:INTERNAL=default;enabled;disabled
//STRINGS property for variable: WITH_SOURCE_TREE
WITH_SOURCE_TREE-STRINGS:INTERNAL=no;flat;hierarchical
//Components requested for this build tree.
_Boost_COMPONENTS_SEARCHED:INTERNAL=atomic;chrono;filesystem;iostreams;program_options;regex;system;thread
//Last used Boost_INCLUDE_DIR value.
_Boost_INCLUDE_DIR_LAST:INTERNAL=C:/local/boost_1_81_0
//Last used Boost_LIBRARY_DIR_DEBUG value.
_Boost_LIBRARY_DIR_DEBUG_LAST:INTERNAL=C:/local/boost_1_81_0/lib64-msvc-14.3
//Last used Boost_LIBRARY_DIR_RELEASE value.
_Boost_LIBRARY_DIR_RELEASE_LAST:INTERNAL=C:/local/boost_1_81_0/lib64-msvc-14.3
//Last used Boost_NAMESPACE value.
_Boost_NAMESPACE_LAST:INTERNAL=boost
//Last used Boost_USE_MULTITHREADED value.
_Boost_USE_MULTITHREADED_LAST:INTERNAL=ON
//Last used Boost_USE_STATIC_LIBS value.
_Boost_USE_STATIC_LIBS_LAST:INTERNAL=ON
//Last used Boost_USE_STATIC_RUNTIME value.
_Boost_USE_STATIC_RUNTIME_LAST:INTERNAL=OFF
//ADVANCED property for variable: _CXX_FILESYSTEM_HAVE_HEADER
_CXX_FILESYSTEM_HAVE_HEADER-ADVANCED:INTERNAL=1
//Have include filesystem
_CXX_FILESYSTEM_HAVE_HEADER:INTERNAL=1
//Cached branch name
rev_branch_cached:INTERNAL=Archived
//Cached commit-hash
rev_hash_cached:INTERNAL=unknown

