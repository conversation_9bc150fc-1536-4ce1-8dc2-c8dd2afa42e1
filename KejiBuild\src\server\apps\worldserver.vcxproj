﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{34C3F4A2-CDCC-3518-A976-66A176E510A7}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>worldserver</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">worldserver.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">worldserver.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">worldserver.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">worldserver.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /bigobj /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/apps/worldserver.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;AC_MODULES_LIST="mod-pbot,";CONFIG_FILE_LIST="mod_pbot.conf.dist,";_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;AC_MODULES_LIST=\"mod-pbot,\";CONFIG_FILE_LIST=\"mod_pbot.conf.dist,\";_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-pbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\Debug\modules.lib;..\scripts\Debug\scripts.lib;..\game\Debug\game.lib;..\..\..\deps\gsoap\Debug\gsoap.lib;..\shared\Debug\shared.lib;..\database\Debug\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\Debug\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-gd-x64-1_81.lib;..\..\..\deps\argon2\Debug\argon2.lib;..\..\..\deps\SFMT\Debug\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MDd\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MDd\libcrypto.lib;..\..\..\deps\fmt\Debug\fmt.lib;..\..\..\deps\g3dlite\Debug\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\Debug\Detour.lib;..\..\..\deps\zlib\Debug\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/KejiBuild/src/server/apps/Debug/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/KejiBuild/bin/Debug/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/apps/worldserver.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-pbot,";CONFIG_FILE_LIST="mod_pbot.conf.dist,";_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-pbot,\";CONFIG_FILE_LIST=\"mod_pbot.conf.dist,\";_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-pbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\Release\modules.lib;..\scripts\Release\scripts.lib;..\game\Release\game.lib;..\..\..\deps\gsoap\Release\gsoap.lib;..\shared\Release\shared.lib;..\database\Release\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\Release\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\Release\argon2.lib;..\..\..\deps\SFMT\Release\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\Release\fmt.lib;..\..\..\deps\g3dlite\Release\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\Release\Detour.lib;..\..\..\deps\zlib\Release\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/KejiBuild/src/server/apps/Release/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/KejiBuild/bin/Release/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/apps/worldserver.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-pbot,";CONFIG_FILE_LIST="mod_pbot.conf.dist,";_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-pbot,\";CONFIG_FILE_LIST=\"mod_pbot.conf.dist,\";_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-pbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\MinSizeRel\modules.lib;..\scripts\MinSizeRel\scripts.lib;..\game\MinSizeRel\game.lib;..\..\..\deps\gsoap\MinSizeRel\gsoap.lib;..\shared\MinSizeRel\shared.lib;..\database\MinSizeRel\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\MinSizeRel\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\MinSizeRel\argon2.lib;..\..\..\deps\SFMT\MinSizeRel\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\MinSizeRel\fmt.lib;..\..\..\deps\g3dlite\MinSizeRel\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\MinSizeRel\Detour.lib;..\..\..\deps\zlib\MinSizeRel\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/KejiBuild/src/server/apps/MinSizeRel/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/KejiBuild/bin/MinSizeRel/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/KejiBuild/src/server/apps/worldserver.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-pbot,";CONFIG_FILE_LIST="mod_pbot.conf.dist,";_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-pbot,\";CONFIG_FILE_LIST=\"mod_pbot.conf.dist,\";_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-pbot\src\server\apps;D:\keji\azerothcore-pbot\src\server\apps\worldserver;D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess;D:\keji\KejiBuild\src\server\apps\worldserver;D:\keji\azerothcore-pbot\modules;D:\keji\azerothcore-pbot\modules\mod-pbot\src;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Battlegrounds\CommandBG;D:\keji\azerothcore-pbot\modules\mod-pbot\src\Movement;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotArenaAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotBGAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotDuelAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotFieldAI;D:\keji\azerothcore-pbot\modules\mod-pbot\src\PlayerAI\BotGroupAI;D:\keji\azerothcore-pbot\src\server\game;D:\keji\azerothcore-pbot\src\server\game\AI;D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-pbot\src\server\game\Accounts;D:\keji\azerothcore-pbot\src\server\game\Achievements;D:\keji\azerothcore-pbot\src\server\game\Addons;D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-pbot\src\server\game\AuctionHouse;D:\keji\azerothcore-pbot\src\server\game\Autobroadcast;D:\keji\azerothcore-pbot\src\server\game\Battlefield;D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-pbot\src\server\game\Cache;D:\keji\azerothcore-pbot\src\server\game\Calendar;D:\keji\azerothcore-pbot\src\server\game\Chat;D:\keji\azerothcore-pbot\src\server\game\Chat\Channels;D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-pbot\src\server\game\Combat;D:\keji\azerothcore-pbot\src\server\game\Conditions;D:\keji\azerothcore-pbot\src\server\game\DataStores;D:\keji\azerothcore-pbot\src\server\game\DungeonFinding;D:\keji\azerothcore-pbot\src\server\game\Entities;D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-pbot\src\server\game\Entities\Creature;D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-pbot\src\server\game\Entities\Item;D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-pbot\src\server\game\Entities\Object;D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-pbot\src\server\game\Entities\Pet;D:\keji\azerothcore-pbot\src\server\game\Entities\Player;D:\keji\azerothcore-pbot\src\server\game\Entities\Totem;D:\keji\azerothcore-pbot\src\server\game\Entities\Transport;D:\keji\azerothcore-pbot\src\server\game\Entities\Unit;D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-pbot\src\server\game\Events;D:\keji\azerothcore-pbot\src\server\game\Globals;D:\keji\azerothcore-pbot\src\server\game\Grids;D:\keji\azerothcore-pbot\src\server\game\Grids\Cells;D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-pbot\src\server\game\Groups;D:\keji\azerothcore-pbot\src\server\game\Guilds;D:\keji\azerothcore-pbot\src\server\game\Handlers;D:\keji\azerothcore-pbot\src\server\game\Instances;D:\keji\azerothcore-pbot\src\server\game\Loot;D:\keji\azerothcore-pbot\src\server\game\Mails;D:\keji\azerothcore-pbot\src\server\game\Maps;D:\keji\azerothcore-pbot\src\server\game\Misc;D:\keji\azerothcore-pbot\src\server\game\Miscellaneous;D:\keji\azerothcore-pbot\src\server\game\Modules;D:\keji\azerothcore-pbot\src\server\game\Motd;D:\keji\azerothcore-pbot\src\server\game\Movement;D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-pbot\src\server\game\Movement\Spline;D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-pbot\src\server\game\Petitions;D:\keji\azerothcore-pbot\src\server\game\Pools;D:\keji\azerothcore-pbot\src\server\game\Quests;D:\keji\azerothcore-pbot\src\server\game\Reputation;D:\keji\azerothcore-pbot\src\server\game\Scripting;D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-pbot\src\server\game\Server;D:\keji\azerothcore-pbot\src\server\game\Server\Packets;D:\keji\azerothcore-pbot\src\server\game\Server\Protocol;D:\keji\azerothcore-pbot\src\server\game\Skills;D:\keji\azerothcore-pbot\src\server\game\Spells;D:\keji\azerothcore-pbot\src\server\game\Spells\Auras;D:\keji\azerothcore-pbot\src\server\game\Texts;D:\keji\azerothcore-pbot\src\server\game\Tickets;D:\keji\azerothcore-pbot\src\server\game\Time;D:\keji\azerothcore-pbot\src\server\game\Tools;D:\keji\azerothcore-pbot\src\server\game\Warden;D:\keji\azerothcore-pbot\src\server\game\Warden\Modules;D:\keji\azerothcore-pbot\src\server\game\Weather;D:\keji\azerothcore-pbot\src\server\game\World;D:\keji\azerothcore-pbot\src\server\shared;D:\keji\azerothcore-pbot\src\server\shared\DataStores;D:\keji\azerothcore-pbot\src\server\shared\Network;D:\keji\azerothcore-pbot\src\server\shared\Packets;D:\keji\azerothcore-pbot\src\server\shared\Realms;D:\keji\azerothcore-pbot\src\server\shared\Secrets;D:\keji\azerothcore-pbot\src\server\database;D:\keji\azerothcore-pbot\src\server\database\Database;D:\keji\azerothcore-pbot\src\server\database\Database\Implementation;D:\keji\azerothcore-pbot\src\server\database\Logging;D:\keji\azerothcore-pbot\src\server\database\Updater;D:\keji\KejiBuild;D:\keji\azerothcore-pbot\src\common;D:\keji\azerothcore-pbot\src\common\Asio;D:\keji\azerothcore-pbot\src\common\Collision;D:\keji\azerothcore-pbot\src\common\Collision\Management;D:\keji\azerothcore-pbot\src\common\Collision\Maps;D:\keji\azerothcore-pbot\src\common\Collision\Models;D:\keji\azerothcore-pbot\src\common\Configuration;D:\keji\azerothcore-pbot\src\common\Cryptography;D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-pbot\src\common\DataStores;D:\keji\azerothcore-pbot\src\common\Debugging;D:\keji\azerothcore-pbot\src\common\Dynamic;D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-pbot\src\common\Encoding;D:\keji\azerothcore-pbot\src\common\IPLocation;D:\keji\azerothcore-pbot\src\common\Logging;D:\keji\azerothcore-pbot\src\common\Metric;D:\keji\azerothcore-pbot\src\common\Navigation;D:\keji\azerothcore-pbot\src\common\Platform;D:\keji\azerothcore-pbot\src\common\Threading;D:\keji\azerothcore-pbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-pbot\deps\argon2;D:\keji\azerothcore-pbot\deps\SFMT;D:\keji\azerothcore-pbot\deps\utf8cpp;D:\keji\azerothcore-pbot\deps\fmt\include;D:\keji\azerothcore-pbot\deps\g3dlite\include;D:\keji\azerothcore-pbot\deps\zlib;D:\keji\azerothcore-pbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-pbot\src\server\scripts;D:\keji\azerothcore-pbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-pbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/KejiBuild/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\RelWithDebInfo\modules.lib;..\scripts\RelWithDebInfo\scripts.lib;..\game\RelWithDebInfo\game.lib;..\..\..\deps\gsoap\RelWithDebInfo\gsoap.lib;..\shared\RelWithDebInfo\shared.lib;..\database\RelWithDebInfo\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\RelWithDebInfo\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\RelWithDebInfo\argon2.lib;..\..\..\deps\SFMT\RelWithDebInfo\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\RelWithDebInfo\fmt.lib;..\..\..\deps\g3dlite\RelWithDebInfo\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\RelWithDebInfo\Detour.lib;..\..\..\deps\zlib\RelWithDebInfo\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/KejiBuild/src/server/apps/RelWithDebInfo/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/KejiBuild/bin/RelWithDebInfo/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-pbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\worldserver.dir\cmake_pch.cxx">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/worldserver.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/worldserver.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/worldserver.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/worldserver.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\Main.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\resource.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap\ACSoap.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\ACSoap\ACSoap.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine\CliRunnable.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\CommandLine\CliRunnable.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess\RASession.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\RemoteAccess\RASession.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Debugging\WheatyExceptionReport.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Debugging\WheatyExceptionReport.h" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Platform\ServiceWin32.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/KejiBuild/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Platform\ServiceWin32.h" />
    <ResourceCompile Include="D:\keji\azerothcore-pbot\src\server\apps\worldserver\worldserver.rc">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">NotUsing</PrecompiledHeader>
    </ResourceCompile>
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\worldserver.dir\Debug\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\worldserver.dir\Release\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\worldserver.dir\MinSizeRel\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\worldserver.dir\RelWithDebInfo\cmake_pch.hxx" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\KejiBuild\ZERO_CHECK.vcxproj">
      <Project>{2B34230A-489D-329E-A203-4300066A20FA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{7762352D-F6A8-3C78-B40C-F6D9EC6696BB}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\argon2\argon2.vcxproj">
      <Project>{0193C8EB-A537-3520-85BF-0E144631F6EE}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\common\common.vcxproj">
      <Project>{C1B3B1F1-588C-3E0B-8C28-A2834C66B5BA}</Project>
      <Name>common</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\database\database.vcxproj">
      <Project>{91634308-AE96-3E40-B8FC-644394DD71B3}</Project>
      <Name>database</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\fmt\fmt.vcxproj">
      <Project>{7360C5EE-63A8-3467-9347-9D01A58DD42F}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\g3dlite\g3dlib.vcxproj">
      <Project>{81F9C921-10D9-36E3-88FE-F780EF060AE7}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\game\game.vcxproj">
      <Project>{32663C99-0698-35F6-82F9-57D2B46880A2}</Project>
      <Name>game</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\gsoap\gsoap.vcxproj">
      <Project>{86CE857F-1FBF-3B37-A37A-5CDB7D36B2A4}</Project>
      <Name>gsoap</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\modules\modules.vcxproj">
      <Project>{00F9C9A8-D330-3B91-8E11-D7911F4153DB}</Project>
      <Name>modules</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\genrev\revision.h.vcxproj">
      <Project>{001DE7BF-7FB1-3018-AAEA-7D06F0FC7440}</Project>
      <Name>revision.h</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\scripts\scripts.vcxproj">
      <Project>{4D7D21A0-0841-3F61-899D-42DA6DF22DA0}</Project>
      <Name>scripts</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\SFMT\sfmt.vcxproj">
      <Project>{125ADA47-4D19-3387-AB3D-C8276D002DDC}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\shared\shared.vcxproj">
      <Project>{5792A934-83A4-3A5A-B22B-41461D3B9094}</Project>
      <Name>shared</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\zlib\zlib.vcxproj">
      <Project>{792C559D-6BD0-3B63-9273-7B424A678DC9}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>