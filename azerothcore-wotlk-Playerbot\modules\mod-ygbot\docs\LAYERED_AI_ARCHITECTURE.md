# 分层AI架构设计文档

## 概述

本文档描述了机器人战斗系统的分层AI架构，该架构支持多场景和职业特化，提供了灵活、可扩展的AI决策系统。

## 架构层级

### 1. 战略层 (Strategic Layer)
- **优先级**: 1000
- **职责**: 场景分析、目标选择、战术决策
- **更新频率**: 每秒1次
- **决策内容**: 
  - 威胁等级分析
  - 主要目标选择
  - 交战策略确定
  - 撤退决策

### 2. 战术层 (Tactical Layer)
- **优先级**: 800
- **职责**: 技能轮换、位置管理、资源管理
- **更新频率**: 每秒2次
- **决策内容**:
  - 怒气/法力管理
  - 战斗姿态选择
  - 增益技能使用
  - 防御冷却管理

### 3. 操作层 (Operational Layer)
- **优先级**: 600
- **职责**: 具体技能执行、移动控制
- **更新频率**: 每0.5秒1次
- **决策内容**:
  - 技能轮换执行
  - 基础攻击
  - 移动控制
  - 目标切换

### 4. 反应层 (Reactive Layer)
- **优先级**: 1200 (最高)
- **职责**: 紧急情况处理、打断、逃跑
- **更新频率**: 每0.1秒1次
- **决策内容**:
  - 低血量处理
  - 法术打断
  - 紧急技能使用
  - 反击处理

## 场景特化

### 野外PvE (World PvE)
- **适用条件**: 不在副本、战场、竞技场、决斗中
- **特点**: 
  - 注重效率和安全
  - 支持多目标处理
  - 资源保守管理
  - 避免不必要风险

### 战场 (Battleground)
- **适用条件**: `Player::InBattleground()`
- **特点**:
  - 团队协作优先
  - 目标优先级调整
  - 生存能力增强
  - 支援队友

### 竞技场 (Arena)
- **适用条件**: `Player::InArena()`
- **特点**:
  - 爆发输出优先
  - 控制技能重要
  - 预判对手行为
  - 精确资源管理

### 决斗 (Duel)
- **适用条件**: `Player::duel && duel->State == DUEL_STATE_INPROGRESS`
- **特点**:
  - 1v1优化
  - 技能组合重要
  - 心理战术
  - 适应性强

### 副本 (Dungeon)
- **适用条件**: `Map::IsDungeon()`
- **特点**:
  - 角色定位明确
  - 团队配合
  - 机制处理
  - 资源分配

### 团队副本 (Raid)
- **适用条件**: `Map::IsRaid()`
- **特点**:
  - 复杂机制处理
  - 精确定位
  - 团队协调
  - 长期战斗

## 职业特化

### 战士 (Warrior)

#### 武器战 (Arms)
- **特点**: 单目标高伤害，控制能力强
- **核心技能轮换**:
  1. 斩杀 (目标<20%血量)
  2. 致死打击 (主要输出)
  3. 压制 (目标闪避后)
  4. 撕裂 (DoT效果)
  5. 英勇打击 (基础攻击)

#### 狂暴战 (Fury)
- **特点**: 双持武器，持续输出，群体伤害
- **核心技能轮换**:
  1. 嗜血 (主要输出+治疗)
  2. 旋风斩 (群体伤害)
  3. 斩杀 (目标<20%血量)

#### 防护战 (Protection)
- **特点**: 坦克定位，威胁生成，生存能力
- **核心技能轮换**:
  1. 盾牌猛击 (高威胁+伤害)
  2. 复仇 (反击技能)
  3. 毁灭打击 (威胁+减甲)

## 决策系统

### 决策权重计算
```cpp
float score = priority * confidence * urgency;
if (cooldown > 0) score *= 0.5f; // 冷却惩罚
```

### 决策选择流程
1. 收集所有AI层级的决策
2. 计算每个决策的综合分数
3. 选择分数最高的决策
4. 执行决策并记录结果

### 决策冲突处理
- 高优先级层级的决策优先
- 相同优先级时比较置信度
- 紧急程度作为决胜因素

## 扩展指南

### 添加新场景AI
1. 继承`IScenarioAI`接口
2. 实现场景检测逻辑
3. 创建场景特定的AI层级
4. 在`BotAIInitializer`中注册

### 添加新职业AI
1. 继承`IClassAI`接口
2. 实现职业检测逻辑
3. 创建职业特定的AI层级
4. 定义技能轮换和优先级
5. 在`BotAIInitializer`中注册

### 添加新AI层级
1. 继承`IAILayer`接口
2. 实现决策生成逻辑
3. 定义执行逻辑
4. 设置合适的优先级

## 性能考虑

### 更新频率优化
- 战略层: 1秒 (低频率，高级决策)
- 战术层: 0.5秒 (中频率，资源管理)
- 操作层: 0.5秒 (中频率，技能执行)
- 反应层: 0.1秒 (高频率，紧急处理)

### 内存管理
- 使用智能指针管理AI对象
- 及时清理无效的AI实例
- 避免循环引用

### 异常处理
- 每个AI层级独立异常处理
- 失败时回退到传统AI系统
- 详细的错误日志记录

## 配置选项

### 启用/禁用新AI系统
```cpp
bool m_useNewAI = true; // 在BotCombatAI中配置
```

### 调试模式
```cpp
bool m_debugMode = false; // 启用详细日志
```

### 层级优先级调整
```cpp
// 在各AI层级类中修改GetPriority()返回值
uint32 GetPriority() const override { return 1200; }
```

## 未来扩展

### 计划中的功能
1. **机器学习集成**: 基于历史数据优化决策
2. **动态优先级**: 根据战斗情况调整层级优先级
3. **团队协调**: 多机器人之间的协调AI
4. **自适应策略**: 根据对手行为调整策略
5. **性能分析**: AI决策效果的统计分析

### 技术债务
1. 完善所有职业的AI实现
2. 添加更多场景特化
3. 优化决策算法性能
4. 增加单元测试覆盖

## 总结

分层AI架构提供了一个灵活、可扩展的机器人战斗系统，支持多场景和职业特化。通过明确的层级分工和决策机制，系统能够做出智能的战斗决策，同时保持良好的性能和可维护性。

当前实现了野外PvE场景和战士职业的完整AI，为后续扩展奠定了坚实的基础。
