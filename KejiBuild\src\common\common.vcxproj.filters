﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Banner.cpp" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Common.cpp" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\GitRevision.cpp" />
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchy.cpp">
      <Filter>Collision</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\DynamicTree.cpp">
      <Filter>Collision</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapFactory.cpp">
      <Filter>Collision\Management</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapMgr.cpp">
      <Filter>Collision\Management</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapFactory.cpp">
      <Filter>Collision\Management</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapMgr2.cpp">
      <Filter>Collision\Management</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapTree.cpp">
      <Filter>Collision\Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\TileAssembler.cpp">
      <Filter>Collision\Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\GameObjectModel.cpp">
      <Filter>Collision\Models</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelInstance.cpp">
      <Filter>Collision\Models</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\WorldModel.cpp">
      <Filter>Collision\Models</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Configuration\BuiltInConfig.cpp">
      <Filter>Configuration</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Configuration\Config.cpp">
      <Filter>Configuration</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\AES.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\ARC4.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Argon2.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\BigNumber.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoRandom.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\OpenSSLCrypto.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\TOTP.cpp">
      <Filter>Cryptography</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthCrypt.cpp">
      <Filter>Cryptography\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\SRP6.cpp">
      <Filter>Cryptography\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\DataStores\DBCFileLoader.cpp">
      <Filter>DataStores</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base32.cpp">
      <Filter>Encoding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base64.cpp">
      <Filter>Encoding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\IPLocation\IPLocation.cpp">
      <Filter>IPLocation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Appender.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderConsole.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderFile.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Log.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\LogMessage.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\LogOperation.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\Logger.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\enuminfo_AppenderConsole.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Logging\enuminfo_LogCommon.cpp">
      <Filter>Logging</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Metric\Metric.cpp">
      <Filter>Metric</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Navigation\DetourExtended.cpp">
      <Filter>Navigation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Threading\ProcessPriority.cpp">
      <Filter>Threading</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Threading\Threading.cpp">
      <Filter>Threading</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventMap.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventProcessor.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Random.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\SFMTRand.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\StartProcess.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringFormat.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\TaskScheduler.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Timer.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tokenize.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Utilities\Util.cpp">
      <Filter>Utilities</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Debugging\Errors.cpp">
      <Filter>Debugging</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Banner.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Common.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\CompilerDefs.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Define.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\GitRevision.h" />
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\AsioHacksFwd.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IoContext.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IpAddress.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\IpNetwork.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\Resolver.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\SteadyTimer.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Asio\Strand.h">
      <Filter>Asio</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchy.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\BoundingIntervalHierarchyWrapper.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\DynamicTree.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\RegularGrid.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\VMapDefinitions.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\VMapTools.h">
      <Filter>Collision</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\IMMAPMgr.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\IVMapMgr.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapFactory.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\MMapMgr.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapFactory.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Management\VMapMgr2.h">
      <Filter>Collision\Management</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapDefines.h">
      <Filter>Collision\Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\MapTree.h">
      <Filter>Collision\Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Maps\TileAssembler.h">
      <Filter>Collision\Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\GameObjectModel.h">
      <Filter>Collision\Models</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelIgnoreFlags.h">
      <Filter>Collision\Models</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\ModelInstance.h">
      <Filter>Collision\Models</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Collision\Models\WorldModel.h">
      <Filter>Collision\Models</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\BuiltInConfig.h">
      <Filter>Configuration</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\Config.h">
      <Filter>Configuration</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Configuration\ConfigValueCache.h">
      <Filter>Configuration</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\AES.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\ARC4.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Argon2.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\BigNumber.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoConstants.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoGenerics.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoHash.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\CryptoRandom.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\HMAC.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\OpenSSLCrypto.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\SessionKeyGenerator.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\TOTP.h">
      <Filter>Cryptography</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthCrypt.h">
      <Filter>Cryptography\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\AuthDefines.h">
      <Filter>Cryptography\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Cryptography\Authentication\SRP6.h">
      <Filter>Cryptography\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\DataStores\DBCFileLoader.h">
      <Filter>DataStores</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\FactoryHolder.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedList.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\ObjectRegistry.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainer.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerFunctions.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerFunctionsPtr.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeContainerVisitor.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\TypeList.h">
      <Filter>Dynamic</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference\RefMgr.h">
      <Filter>Dynamic\LinkedReference</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Dynamic\LinkedReference\Reference.h">
      <Filter>Dynamic\LinkedReference</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base32.h">
      <Filter>Encoding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\Base64.h">
      <Filter>Encoding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Encoding\BaseEncoding.h">
      <Filter>Encoding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\IPLocation\IPLocation.h">
      <Filter>IPLocation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Appender.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderConsole.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\AppenderFile.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Log.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogCommon.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogMessage.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\LogOperation.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Logging\Logger.h">
      <Filter>Logging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Metric\Metric.h">
      <Filter>Metric</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Navigation\DetourExtended.h">
      <Filter>Navigation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\LockedQueue.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\MPSCQueue.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\PCQueue.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\PolicyLock.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\ProcessPriority.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\Threading.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Threading\ThreadingModel.h">
      <Filter>Threading</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\AsyncCallbackProcessor.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\ByteConverter.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\CircularBuffer.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Containers.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\DataMap.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Duration.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EnumFlag.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventEmitter.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventMap.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\EventProcessor.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Geometry.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\IteratorPair.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\MathUtil.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\MessageBuffer.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Optional.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Physics.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Random.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SFMTRand.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SignalHandler.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\SmartEnum.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StartProcess.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringConvert.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\StringFormat.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\TaskScheduler.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Timer.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tokenize.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Tuples.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Types.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\Util.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Utilities\advstd.h">
      <Filter>Utilities</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Debugging\Errors.h">
      <Filter>Debugging</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\common\CMakeFiles\common.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\common\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Asio">
      <UniqueIdentifier>{6B2672D0-E4E4-34FA-8E8C-41C283C5F341}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collision">
      <UniqueIdentifier>{DEE3C6D7-D314-3361-A2EB-9E277E421D6D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collision\Management">
      <UniqueIdentifier>{E7A953F2-9403-33A4-BD8D-A02F1F49BDEE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collision\Maps">
      <UniqueIdentifier>{26932153-A3C5-3D9B-800A-2D13B7200B0E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Collision\Models">
      <UniqueIdentifier>{2F5B4D60-6B70-3D60-B99A-E399F298632B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Configuration">
      <UniqueIdentifier>{72855B81-FF1C-32A4-9FC8-8BF5398B0F63}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cryptography">
      <UniqueIdentifier>{2CA960E0-FBE5-3D42-90B2-05FFE253569E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cryptography\Authentication">
      <UniqueIdentifier>{C152FF50-E108-33C6-9EAC-D7401D6B3501}</UniqueIdentifier>
    </Filter>
    <Filter Include="DataStores">
      <UniqueIdentifier>{04EC1F01-CF0A-3346-89CA-65C2767375B7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Debugging">
      <UniqueIdentifier>{7B378313-2FF8-3F7F-86E2-EE564DE2D734}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic">
      <UniqueIdentifier>{23F32D8D-9CA4-329F-BCD3-F9B83A707E7F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Dynamic\LinkedReference">
      <UniqueIdentifier>{AF0B92AB-013A-3AD9-AC83-10C780862E44}</UniqueIdentifier>
    </Filter>
    <Filter Include="Encoding">
      <UniqueIdentifier>{DA92C060-9110-33DC-97B8-EC119868FD8E}</UniqueIdentifier>
    </Filter>
    <Filter Include="IPLocation">
      <UniqueIdentifier>{867611C0-E764-30BD-93F3-45F528BD6F80}</UniqueIdentifier>
    </Filter>
    <Filter Include="Logging">
      <UniqueIdentifier>{19902B88-49CA-38FC-950B-8EC705CC1038}</UniqueIdentifier>
    </Filter>
    <Filter Include="Metric">
      <UniqueIdentifier>{787EA9EC-8E86-30F2-9ADE-64AB12FC023D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Navigation">
      <UniqueIdentifier>{12CBFFA7-5642-3758-9F91-BF7D2BD225EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{90DBB2E8-B7B8-3A5B-B6BF-533AA6796052}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{13BB9762-CF46-3603-980C-A0F8777D13B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Threading">
      <UniqueIdentifier>{E8BEEA9C-874A-3D0B-910F-8F0A49B08B0B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Utilities">
      <UniqueIdentifier>{BFF3143A-C6A6-3D66-88AB-D76E6D5782E2}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
