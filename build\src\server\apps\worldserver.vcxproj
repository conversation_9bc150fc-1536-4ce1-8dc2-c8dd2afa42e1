﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D7A4BE3F-9B80-363D-A43A-B6CDB9F925B8}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>worldserver</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">worldserver.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">worldserver.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">worldserver.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">worldserver.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">worldserver</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /bigobj /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/build/src/server/apps/worldserver.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;AC_MODULES_LIST="mod-eluna,mod-ygbot,";CONFIG_FILE_LIST="mod_eluna.conf.dist,mod_ygbot.conf.dist,";AZEROTHCORE;WOTLK;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;AC_MODULES_LIST=\"mod-eluna,mod-ygbot,\";CONFIG_FILE_LIST=\"mod_eluna.conf.dist,mod_ygbot.conf.dist,\";AZEROTHCORE;WOTLK;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\Debug\modules.lib;..\scripts\Debug\scripts.lib;..\game\Debug\game.lib;..\..\..\deps\gsoap\Debug\gsoap.lib;..\..\..\modules\mod-eluna\src\lualib\lua\Debug\lua52.lib;..\shared\Debug\shared.lib;..\database\Debug\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\Debug\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-gd-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-gd-x64-1_81.lib;..\..\..\deps\argon2\Debug\argon2.lib;..\..\..\deps\SFMT\Debug\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MDd\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MDd\libcrypto.lib;..\..\..\deps\fmt\Debug\fmt.lib;..\..\..\deps\g3dlite\Debug\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\Debug\Detour.lib;..\..\..\deps\zlib\Debug\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/build/src/server/apps/Debug/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/build/bin/Debug/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/build/src/server/apps/worldserver.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-eluna,mod-ygbot,";CONFIG_FILE_LIST="mod_eluna.conf.dist,mod_ygbot.conf.dist,";AZEROTHCORE;WOTLK;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-eluna,mod-ygbot,\";CONFIG_FILE_LIST=\"mod_eluna.conf.dist,mod_ygbot.conf.dist,\";AZEROTHCORE;WOTLK;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\Release\modules.lib;..\scripts\Release\scripts.lib;..\game\Release\game.lib;..\..\..\deps\gsoap\Release\gsoap.lib;..\..\..\modules\mod-eluna\src\lualib\lua\Release\lua52.lib;..\shared\Release\shared.lib;..\database\Release\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\Release\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\Release\argon2.lib;..\..\..\deps\SFMT\Release\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\Release\fmt.lib;..\..\..\deps\g3dlite\Release\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\Release\Detour.lib;..\..\..\deps\zlib\Release\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/build/src/server/apps/Release/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/build/bin/Release/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/build/src/server/apps/worldserver.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-eluna,mod-ygbot,";CONFIG_FILE_LIST="mod_eluna.conf.dist,mod_ygbot.conf.dist,";AZEROTHCORE;WOTLK;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-eluna,mod-ygbot,\";CONFIG_FILE_LIST=\"mod_eluna.conf.dist,mod_ygbot.conf.dist,\";AZEROTHCORE;WOTLK;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\MinSizeRel\modules.lib;..\scripts\MinSizeRel\scripts.lib;..\game\MinSizeRel\game.lib;..\..\..\deps\gsoap\MinSizeRel\gsoap.lib;..\..\..\modules\mod-eluna\src\lualib\lua\MinSizeRel\lua52.lib;..\shared\MinSizeRel\shared.lib;..\database\MinSizeRel\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\MinSizeRel\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\MinSizeRel\argon2.lib;..\..\..\deps\SFMT\MinSizeRel\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\MinSizeRel\fmt.lib;..\..\..\deps\g3dlite\MinSizeRel\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\MinSizeRel\Detour.lib;..\..\..\deps\zlib\MinSizeRel\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/build/src/server/apps/MinSizeRel/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/build/bin/MinSizeRel/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Program Files/OpenSSL-Win64/include" /utf-8 /w34100 /w34101 /w34189 /w34389</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4091;4996;4985;4244;4267;4619;4512</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>D:/keji/build/src/server/apps/worldserver.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatSpecificWarningsAsErrors>4263;4264</TreatSpecificWarningsAsErrors>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST="mod-eluna,mod-ygbot,";CONFIG_FILE_LIST="mod_eluna.conf.dist,mod_ygbot.conf.dist,";AZEROTHCORE;WOTLK;_CONF_DIR="C:/Program Files (x86)/AzerothCore";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE="$(ConfigurationName)";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;AC_MODULES_LIST=\"mod-eluna,mod-ygbot,\";CONFIG_FILE_LIST=\"mod_eluna.conf.dist,mod_ygbot.conf.dist,\";AZEROTHCORE;WOTLK;_CONF_DIR=\"C:/Program Files (x86)/AzerothCore\";ENABLE_VMAP_CHECKS;_WIN64;_BUILD_DIRECTIVE=\"$(ConfigurationName)\";_CRT_SECURE_CPP_OVERLOAD_STANDARD_NAMES;_CRT_SECURE_NO_WARNINGS;_CRT_NONSTDC_NO_WARNINGS;__STDC_LIMIT_MACROS;NOMINMAX;LUAJIT_VERSION;BOOST_DATE_TIME_NO_LIB;BOOST_REGEX_NO_LIB;BOOST_CHRONO_NO_LIB;BOOST_SERIALIZATION_NO_LIB;BOOST_CONFIG_SUPPRESS_OUTDATED_MESSAGE;BOOST_ASIO_NO_DEPRECATED;BOOST_SYSTEM_USE_UTF8;BOOST_BIND_NO_PLACEHOLDERS;SFMT_MEXP=19937;HAVE_SSE2;FMT_LOCALE;FMT_CONSTEVAL=;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\keji\azerothcore-wotlk-Playerbot\src\server\apps;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine;D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess;D:\keji\build\src\server\apps\worldserver;D:\keji\azerothcore-wotlk-Playerbot\modules;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\.github\workflows;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\static;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\docs\ElunaDoc\templates;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\extensions\StackTracePlus;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\hooks;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\libs\rigtorp;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\LuaEngine\methods;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\lua;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-eluna\src\lualib\luajit;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotArenaAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotBGAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotDuelAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotFieldAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\CombatSystem\BotGroupAI;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\DeathSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\FakePlayers;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\Faker;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\MovementSystem;D:\keji\azerothcore-wotlk-Playerbot\modules\mod-ygbot\src\TalentSystem;D:\keji\build\_deps\lua52-src\src;D:\keji\azerothcore-wotlk-Playerbot\src\server\game;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\CoreAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\ScriptedAI;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AI\SmartScripts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Accounts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Achievements;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Addons;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\ArenaSpectator;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\AuctionHouse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Autobroadcast;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlefield\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\ArenaSeason;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Battlegrounds\Zones;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Cache;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Calendar;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\Channels;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Chat\ChatCommands;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Combat;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Conditions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\DungeonFinding;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Corpse;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Creature;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\DynamicObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\GameObject;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Item\Container;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Object\Updates;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Pet;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Player;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Totem;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Transport;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Unit;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Entities\Vehicle;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Events;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Globals;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Cells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Grids\Notifiers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Groups;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Guilds;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Handlers;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Instances;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Loot;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Mails;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Misc;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Miscellaneous;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Motd;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\MovementGenerators;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Spline;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Movement\Waypoints;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\OutdoorPvP;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Petitions;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Pools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Quests;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Reputation;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Scripting\ScriptDefines;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Server\Protocol;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Skills;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Spells\Auras;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Texts;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tickets;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Time;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Tools;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Warden\Modules;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\Weather;D:\keji\azerothcore-wotlk-Playerbot\src\server\game\World;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Network;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Packets;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Realms;D:\keji\azerothcore-wotlk-Playerbot\src\server\shared\Secrets;D:\keji\azerothcore-wotlk-Playerbot\src\server\database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Database\Implementation;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\server\database\Updater;D:\keji\build;D:\keji\azerothcore-wotlk-Playerbot\src\common;D:\keji\azerothcore-wotlk-Playerbot\src\common\Asio;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Management;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Maps;D:\keji\azerothcore-wotlk-Playerbot\src\common\Collision\Models;D:\keji\azerothcore-wotlk-Playerbot\src\common\Configuration;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography;D:\keji\azerothcore-wotlk-Playerbot\src\common\Cryptography\Authentication;D:\keji\azerothcore-wotlk-Playerbot\src\common\DataStores;D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic;D:\keji\azerothcore-wotlk-Playerbot\src\common\Dynamic\LinkedReference;D:\keji\azerothcore-wotlk-Playerbot\src\common\Encoding;D:\keji\azerothcore-wotlk-Playerbot\src\common\IPLocation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Logging;D:\keji\azerothcore-wotlk-Playerbot\src\common\Metric;D:\keji\azerothcore-wotlk-Playerbot\src\common\Navigation;D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform;D:\keji\azerothcore-wotlk-Playerbot\src\common\Threading;D:\keji\azerothcore-wotlk-Playerbot\src\common\Utilities;C:\local\boost_1_81_0;D:\keji\azerothcore-wotlk-Playerbot\deps\argon2;D:\keji\azerothcore-wotlk-Playerbot\deps\SFMT;D:\keji\azerothcore-wotlk-Playerbot\deps\utf8cpp;D:\keji\azerothcore-wotlk-Playerbot\deps\fmt\include;D:\keji\azerothcore-wotlk-Playerbot\deps\g3dlite\include;D:\keji\azerothcore-wotlk-Playerbot\deps\zlib;D:\keji\azerothcore-wotlk-Playerbot\deps\recastnavigation\Detour\Include;D:\keji\azerothcore-wotlk-Playerbot\src\server\scripts;D:\keji\azerothcore-wotlk-Playerbot\deps\gsoap;C:\Program Files\OpenSSL-Win64\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PostBuildEvent>
      <Message>	</Message>
      <Command>setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E copy D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/worldserver/worldserver.conf.dist D:/keji/build/bin/$(ConfigurationName)/configs
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>..\..\..\modules\RelWithDebInfo\modules.lib;..\scripts\RelWithDebInfo\scripts.lib;..\game\RelWithDebInfo\game.lib;..\..\..\deps\gsoap\RelWithDebInfo\gsoap.lib;..\..\..\modules\mod-eluna\src\lualib\lua\RelWithDebInfo\lua52.lib;..\shared\RelWithDebInfo\shared.lib;..\database\RelWithDebInfo\database.lib;C:\mysql\lib\libmysql.lib;..\..\common\RelWithDebInfo\common.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_system-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_filesystem-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_program_options-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_iostreams-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_regex-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_thread-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_chrono-vc143-mt-x64-1_81.lib;C:\local\boost_1_81_0\lib64-msvc-14.3\libboost_atomic-vc143-mt-x64-1_81.lib;..\..\..\deps\argon2\RelWithDebInfo\argon2.lib;..\..\..\deps\SFMT\RelWithDebInfo\sfmt.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libssl.lib;C:\Program Files\OpenSSL-Win64\lib\VC\x64\MD\libcrypto.lib;..\..\..\deps\fmt\RelWithDebInfo\fmt.lib;..\..\..\deps\g3dlite\RelWithDebInfo\g3dlib.lib;..\..\..\deps\recastnavigation\Detour\RelWithDebInfo\Detour.lib;..\..\..\deps\zlib\RelWithDebInfo\zlib.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/keji/build/src/server/apps/RelWithDebInfo/worldserver.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/keji/build/bin/RelWithDebInfo/worldserver.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-wotlk-Playerbot -BD:/keji/build --check-stamp-file D:/keji/build/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\build\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-wotlk-Playerbot -BD:/keji/build --check-stamp-file D:/keji/build/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\build\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-wotlk-Playerbot -BD:/keji/build --check-stamp-file D:/keji/build/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\build\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-wotlk-Playerbot/src/server/apps/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-wotlk-Playerbot -BD:/keji/build --check-stamp-file D:/keji/build/src/server/apps/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\build\src\server\apps\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\keji\build\src\server\apps\CMakeFiles\worldserver.dir\cmake_pch.cxx">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/worldserver.dir/Debug/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/worldserver.dir/Release/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/worldserver.dir/MinSizeRel/cmake_pch.pch</PrecompiledHeaderOutputFile>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/worldserver.dir/RelWithDebInfo/cmake_pch.pch</PrecompiledHeaderOutputFile>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\Main.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\resource.h" />
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap\ACSoap.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\ACSoap\ACSoap.h" />
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine\CliRunnable.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\CommandLine\CliRunnable.h" />
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess\RASession.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\RemoteAccess\RASession.h" />
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging\WheatyExceptionReport.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\common\Debugging\WheatyExceptionReport.h" />
    <ClCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform\ServiceWin32.cpp">
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Debug/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/Release/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/MinSizeRel/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
      <ForcedIncludeFiles Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:/keji/build/src/server/apps/CMakeFiles/worldserver.dir/RelWithDebInfo/cmake_pch.hxx;%(ForcedIncludeFiles)</ForcedIncludeFiles>
    </ClCompile>
    <ClInclude Include="D:\keji\azerothcore-wotlk-Playerbot\src\common\Platform\ServiceWin32.h" />
    <ResourceCompile Include="D:\keji\azerothcore-wotlk-Playerbot\src\server\apps\worldserver\worldserver.rc">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">NotUsing</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">NotUsing</PrecompiledHeader>
    </ResourceCompile>
    <ClInclude Include="D:\keji\build\src\server\apps\CMakeFiles\worldserver.dir\Debug\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\build\src\server\apps\CMakeFiles\worldserver.dir\Release\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\build\src\server\apps\CMakeFiles\worldserver.dir\MinSizeRel\cmake_pch.hxx" />
    <ClInclude Include="D:\keji\build\src\server\apps\CMakeFiles\worldserver.dir\RelWithDebInfo\cmake_pch.hxx" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\build\ZERO_CHECK.vcxproj">
      <Project>{DB8B46FF-4F57-36AC-A330-EE781AE64228}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{DE4264AA-EBFA-341F-BEC5-8FD48310B5A9}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\argon2\argon2.vcxproj">
      <Project>{B9D2648D-8BDF-3368-A81F-E641626C7865}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\common\common.vcxproj">
      <Project>{8E9ED098-B996-34D7-B39C-5BC2D452AD7A}</Project>
      <Name>common</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\server\database\database.vcxproj">
      <Project>{941A86B9-C654-3F1B-A3FA-B44F14988771}</Project>
      <Name>database</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\fmt\fmt.vcxproj">
      <Project>{11E95958-5A76-3790-A143-436E7F72FC55}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\g3dlite\g3dlib.vcxproj">
      <Project>{8CC1FFC3-24B8-3E88-97CF-E552BDF49B3E}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\server\game\game.vcxproj">
      <Project>{093270B4-263E-3B31-9A8A-1894B55A4C8D}</Project>
      <Name>game</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\gsoap\gsoap.vcxproj">
      <Project>{773560CC-ABCE-3654-BD98-CCB5CC560F8F}</Project>
      <Name>gsoap</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\modules\mod-eluna\src\lualib\lua\lualib.vcxproj">
      <Project>{D52C4691-3D77-3C4C-83F1-80A9796667BF}</Project>
      <Name>lualib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\modules\modules.vcxproj">
      <Project>{C07754BF-F3AA-3E80-AF21-52C86A3E5CA7}</Project>
      <Name>modules</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\genrev\revision.h.vcxproj">
      <Project>{8CD4ACC4-C25C-317F-86F8-8131F1AD854E}</Project>
      <Name>revision.h</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\server\scripts\scripts.vcxproj">
      <Project>{A4F978CD-31C6-30F8-9C9D-9198FF1E2D93}</Project>
      <Name>scripts</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\SFMT\sfmt.vcxproj">
      <Project>{351ECFE8-E4E4-3AC6-9F77-587A0E27B178}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\src\server\shared\shared.vcxproj">
      <Project>{0FE62F75-8720-3F59-A976-5855FA67C4F7}</Project>
      <Name>shared</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\build\deps\zlib\zlib.vcxproj">
      <Project>{3AC30259-E6A3-3375-B6E6-2252E308CE76}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>