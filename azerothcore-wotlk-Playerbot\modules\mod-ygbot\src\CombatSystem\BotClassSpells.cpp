#include "BotClassSpells.h"
#include "Log.h"
#include "Player.h"
#include "SpellMgr.h"
#include "SpellInfo.h"
#include "Spell.h"
#include "Item.h"
#include "Bag.h"
#include "ObjectMgr.h"
#include "Map.h"

// BotClassSpells 基类实现
uint32 BotClassSpells::FindMaxRankSpellByExist(uint32 spellID, Player* player)
{
    if (spellID == 0 || !player)
        return 0;

    uint32 selectSpell = sSpellMgr->GetLastSpellInChain(spellID);
    if (selectSpell == 0)
    {
        if (player->HasSpell(spellID))
            return spellID;
        return 0;
    }

    while (!player->HasSpell(selectSpell))
    {
        SpellInfo const* spellInfo = sSpellMgr->GetSpellInfo(selectSpell);
        if (!spellInfo || !spellInfo->GetPrevRankSpell())
            break;
        selectSpell = spellInfo->GetPrevRankSpell()->Id;
    }

    uint32 result = player->HasSpell(selectSpell) ? selectSpell : 0;

    if (result > 0)
    {
        LOG_DEBUG("server", "BotClassSpells::FindMaxRankSpellByExist: 玩家 {} (等级{}) 基础技能ID {} → 适配技能ID {}",
                  player->GetName(), player->GetLevel(), spellID, result);
    }
    else
    {
        LOG_WARN("server", "BotClassSpells::FindMaxRankSpellByExist: 玩家 {} (等级{}) 没有学会技能链中的任何技能，基础ID: {}",
                 player->GetName(), player->GetLevel(), spellID);
    }

    return result;
}

bool BotClassSpells::HasSpell(uint32 spellID, Player* player)
{
    return player && spellID && player->HasSpell(spellID);
}

uint32 BotClassSpells::GetSpellWithRank(uint32 baseSpellId, uint32 rank)
{
    return sSpellMgr->GetSpellWithRank(baseSpellId, rank);
}

// BotRogueSpells 实现
void BotRogueSpells::InitializeSpells(Player* player)
{
    if (!player)
        return;

    InitializeGuardSpells(player);
    InitializeSneakSpells(player);
    InitializeAssistSpells(player);
    InitializeAttackSpells(player);

    LOG_INFO("server", "BotRogueSpells: 为盗贼 {} 初始化技能完成", player->GetName());
}

void BotRogueSpells::InitializeGuardSpells(Player* player)
{
    RogueGuard_Sneak = FindMaxRankSpellByExist(1784, player);           // 潜行
    RogueGuard_ShadowCloak = FindMaxRankSpellByExist(31224, player);    // 暗影斗篷
    RogueGuard_Disappear = FindMaxRankSpellByExist(26889, player);      // 消失
    RogueGuard_Dodge = FindMaxRankSpellByExist(26669, player);          // 闪避
    RogueGuard_Sprint = FindMaxRankSpellByExist(11305, player);         // 疾跑

    LOG_INFO("server", "BotRogueSpells::InitializeGuardSpells: 潜行ID={}, 斗篷ID={}, 消失ID={}, 闪避ID={}",
             RogueGuard_Sneak, RogueGuard_ShadowCloak, RogueGuard_Disappear, RogueGuard_Dodge);
}

void BotRogueSpells::InitializeSneakSpells(Player* player)
{
    // 根据天赋专精初始化潜行技能
    uint32 talentType = GetTalentType(player);

    // 通用潜行技能
    RogueSneak_Surprise = FindMaxRankSpellByExist(1833, player);        // 偷袭（所有专精）

    // 专精特定技能
    if (talentType == 0) // 刺杀专精
    {
        RogueSneak_Premeditate = FindMaxRankSpellByExist(14183, player);    // 预谋（刺杀）
        LOG_DEBUG("server", "BotRogueSpells: 刺杀专精，初始化预谋技能");
    }
    else if (talentType == 1) // 战斗专精
    {
        // 战斗专精主要使用偷袭
        LOG_DEBUG("server", "BotRogueSpells: 战斗专精，使用基础潜行技能");
    }
    else if (talentType == 2) // 敏锐专精
    {
        RogueSneak_Ambush = FindMaxRankSpellByExist(48691, player);         // 伏击（敏锐）
        RogueSneak_Stick = FindMaxRankSpellByExist(51724, player);          // 预谋（敏锐）
        LOG_DEBUG("server", "BotRogueSpells: 敏锐专精，初始化伏击和预谋技能");
    }
}

void BotRogueSpells::InitializeAssistSpells(Player* player)
{
    // 根据天赋专精初始化辅助技能
    uint32 talentType = GetTalentType(player);

    // 通用辅助技能（所有专精都有）
    RogueAssist_Blind = FindMaxRankSpellByExist(2094, player);          // 致盲
    RogueAssist_BlockCast = FindMaxRankSpellByExist(1766, player);      // 脚踢
    RogueAssist_Paralyze = FindMaxRankSpellByExist(1776, player);       // 凿击

    // 专精特定技能
    if (talentType == 0) // 刺杀专精
    {
        RogueAssist_NextCrit = FindMaxRankSpellByExist(14177, player);      // 冷血（刺杀）
        RogueAssist_FastEnergy = FindMaxRankSpellByExist(13750, player);    // 冲动（刺杀）
        LOG_DEBUG("server", "BotRogueSpells: 刺杀专精，初始化冷血和冲动技能");
    }
    else if (talentType == 1) // 战斗专精
    {
        RogueAssist_FastSpeed = FindMaxRankSpellByExist(13877, player);     // 急速（战斗）
        RogueAssist_Disarm = FindMaxRankSpellByExist(51722, player);        // 缴械（战斗）
        LOG_DEBUG("server", "BotRogueSpells: 战斗专精，初始化急速和缴械技能");
    }
    else if (talentType == 2) // 敏锐专精
    {
        RogueAssist_ShadowDance = FindMaxRankSpellByExist(51713, player);   // 暗影之舞（敏锐）
        RogueAssist_ShadowFlash = FindMaxRankSpellByExist(36554, player);   // 暗影步（敏锐）
        RogueAssist_ReadyCD = FindMaxRankSpellByExist(14185, player);       // 预备（敏锐）
        LOG_DEBUG("server", "BotRogueSpells: 敏锐专精，初始化暗影之舞、暗影步和预备技能");
    }
}

void BotRogueSpells::InitializeAttackSpells(Player* player)
{
    // 根据天赋专精初始化攻击技能
    uint32 talentType = GetTalentType(player);

    // 通用攻击技能（所有专精都有）
    RogueAttack_EvilAtt = FindMaxRankSpellByExist(48638, player);       // 邪恶攻击
    RogueAttack_BackAtt = FindMaxRankSpellByExist(48657, player);       // 背刺
    RogueAttack_Separate = FindMaxRankSpellByExist(48672, player);      // 切割
    RogueAttack_Incision = FindMaxRankSpellByExist(48672, player);      // 切割（战斗天赋版本，同一技能）
    RogueAttack_Stun = FindMaxRankSpellByExist(8643, player);           // 肾击
    RogueRange_Throw = FindMaxRankSpellByExist(48674, player);          // 投掷
    RogueAOE_Knife = FindMaxRankSpellByExist(51723, player);            // 刀扇

    // 专精特定技能
    if (talentType == 0) // 刺杀专精
    {
        RogueAttack_Injure = FindMaxRankSpellByExist(48666, player);        // 致伤毒药（刺杀主要）
        RogueAttack_PoisonAtt = FindMaxRankSpellByExist(5938, player);      // 毒药攻击（刺杀）
        RogueAttack_PoisonDmg = FindMaxRankSpellByExist(57993, player);     // 毒伤（刺杀）
        RogueAttack_Damage = FindMaxRankSpellByExist(48668, player);        // 剔骨（刺杀）
        LOG_DEBUG("server", "BotRogueSpells: 刺杀专精，初始化毒药相关技能");
    }
    else if (talentType == 1) // 战斗专精
    {
        RogueAOE_AllDance = FindMaxRankSpellByExist(51690, player);         // 剑刃乱舞（战斗）
        RogueAttack_Damage = FindMaxRankSpellByExist(48668, player);        // 剔骨（战斗也用）
        LOG_DEBUG("server", "BotRogueSpells: 战斗专精，初始化剑刃乱舞技能");
    }
    else if (talentType == 2) // 敏锐专精
    {
        RogueAttack_Blood = FindMaxRankSpellByExist(48660, player);         // 出血（敏锐）
        RogueAttack_Ghost = FindMaxRankSpellByExist(14278, player);         // 鬼魅攻击（敏锐）
        RogueFlag_Dance = 51713;                                            // 暗影之舞标记（敏锐）
        LOG_DEBUG("server", "BotRogueSpells: 敏锐专精，初始化出血和鬼魅攻击技能");
    }

    LOG_INFO("server", "BotRogueSpells::InitializeAttackSpells: 邪恶攻击ID={}, 致伤ID={}, 剔骨ID={}, 切割ID={}",
             RogueAttack_EvilAtt, RogueAttack_Injure, RogueAttack_Damage, RogueAttack_Separate);

    LOG_INFO("server", "BotRogueSpells::InitializeAttackSpells: 邪恶攻击ID={}, 致伤ID={}, 剔骨ID={}, 切割ID={}",
             RogueAttack_EvilAtt, RogueAttack_Injure, RogueAttack_Damage, RogueAttack_Separate);
}

// ========== 盗贼战斗逻辑实现（从GroupRogueAI移动到这里）==========

bool BotRogueSpells::ProcessSneakSpell(Unit* pTarget)
{
    if (!pTarget || !GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 检查是否在潜行状态
    if (!player->HasAura(RogueGuard_Sneak))
    {
        LOG_DEBUG("server", "BotRogueSpells::ProcessSneakSpell: 不在潜行状态");
        return false;
    }

    LOG_INFO("server", "BotRogueSpells::ProcessSneakSpell: 潜行状态下处理偷袭技能，目标: {}", pTarget->GetName());

    // 优先使用伏击（敏锐专精）
    if (RogueSneak_Ambush > 0 && TryCastSpell(RogueSneak_Ambush, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "BotRogueSpells::ProcessSneakSpell: 伏击成功，技能ID: {}", RogueSneak_Ambush);
        return true;
    }

    // 备用：偷袭
    if (RogueSneak_Surprise > 0 && TryCastSpell(RogueSneak_Surprise, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "BotRogueSpells::ProcessSneakSpell: 偷袭成功，技能ID: {}", RogueSneak_Surprise);
        return true;
    }

    LOG_INFO("server", "BotRogueSpells::ProcessSneakSpell: 潜行状态下无法释放任何技能");
    return false;
}

bool BotRogueSpells::ProcessMeleeBlind(Unit* pTarget)
{
    if (!pTarget || !GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 检查是否可以致盲
    if (!CanBlind(pTarget))
        return false;

    // 使用致盲
    if (RogueAssist_Blind > 0 && TryCastSpell(RogueAssist_Blind, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "BotRogueSpells::ProcessMeleeBlind: 致盲成功，目标: {}", pTarget->GetName());
        return true;
    }

    return false;
}

bool BotRogueSpells::CanConsumeCombo(Unit* pTarget)
{
    if (!pTarget || !GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 检查连击点数
    uint8 comboPoints = player->GetComboPoints();
    if (comboPoints < 1)
        return false;

    // 检查能量
    uint32 energyPer = GetEnergyPowerPer();
    if (energyPer < 25) // 需要至少25%的能量
        return false;

    return true;
}

bool BotRogueSpells::CastCloakByNeed()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 检查是否需要暗影斗篷
    if (RogueGuard_ShadowCloak > 0 && !player->HasAura(RogueGuard_ShadowCloak))
    {
        // 检查是否有法术攻击者
        // 简化实现：当生命值较低时使用
        if (player->GetHealthPct() < 50.0f)
        {
            if (TryCastSpell(RogueGuard_ShadowCloak, player) == SpellCastResult::SPELL_CAST_OK)
            {
                LOG_INFO("server", "BotRogueSpells::CastCloakByNeed: 暗影斗篷成功");
                return true;
            }
        }
    }

    return false;
}

bool BotRogueSpells::CanBlind(Unit* pTarget)
{
    if (!pTarget || !GetPlayer())
        return false;

    // 检查目标是否可以被致盲
    if (pTarget->IsImmunedToSpell(sSpellMgr->GetSpellInfo(RogueAssist_Blind)))
        return false;

    // 检查目标是否已经有致盲效果
    if (pTarget->HasAura(RogueAssist_Blind))
        return false;

    return true;
}

bool BotRogueSpells::CanStartSpell()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 检查是否在施法中
    if (player->IsNonMeleeSpellCast(false))
        return false;

    // 检查是否被沉默
    if (HasAuraMechanic(player, MECHANIC_SILENCE))
        return false;

    return true;
}

uint32 BotRogueSpells::GetEnergyPowerPer()
{
    if (!GetPlayer())
        return 0;

    Player* player = GetPlayer();
    float per = (float)player->GetPower(POWER_ENERGY) / (float)player->GetMaxPower(POWER_ENERGY);
    return (uint32)(per * 100);
}

// 实现毒药系统方法
uint32 BotRogueSpells::GetPoisonEntryByWeaponType(EquipmentSlots equipSlot)
{
    if (!GetPlayer())
        return 0;

    Player* player = GetPlayer();
    uint32 level = player->GetLevel();

    // 完全按照mod-pbot-keji的毒药选择逻辑
    if (equipSlot == EQUIPMENT_SLOT_MAINHAND)
    {
        // 主手武器毒药（速效毒药）
        if (level < 20)
            return 0;
        else if (level < 28)
            return 6947;  // 速效毒药I
        else if (level < 36)
            return 6949;  // 速效毒药II
        else if (level < 44)
            return 6950;  // 速效毒药III
        else if (level < 52)
            return 8926;  // 速效毒药IV
        else if (level < 60)
            return 8927;  // 速效毒药V
        else if (level < 68)
            return 8928;  // 速效毒药VI
        else if (level < 73)
            return 21927; // 速效毒药VII
        else if (level < 79)
            return 43230; // 速效毒药VIII
        else
            return 43231; // 速效毒药IX
    }
    else if (equipSlot == EQUIPMENT_SLOT_OFFHAND)
    {
        // 副手武器毒药（致命毒药）
        if (level < 20)
            return 0;
        else if (level < 30)
            return 6947;  // 速效毒药I（低等级用速效）
        else if (level < 38)
            return 2892;  // 致命毒药I
        else if (level < 46)
            return 2893;  // 致命毒药II
        else if (level < 54)
            return 8984;  // 致命毒药III
        else if (level < 60)
            return 8985;  // 致命毒药IV
        else if (level < 70)
            return 20844; // 致命毒药V
        else if (level < 80)
            return 22054; // 致命毒药VI
        else
            return 43233; // 致命毒药VII
    }

    return 0;
}

bool BotRogueSpells::ProcessUpPoison()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    if (player->GetLevel() < 20)
    {
        LOG_DEBUG("server", "BotRogueSpells::ProcessUpPoison: 玩家等级 {} 低于20级", player->GetLevel());
        return false;
    }

    LOG_INFO("server", "BotRogueSpells::ProcessUpPoison: 开始检查毒药，玩家: {}", player->GetName());

    bool applied = false;

    // 按照mod-playerbots-liyunfan的触发器逻辑
    bool mainHandNeedsPoison = IsMainHandWeaponNoEnchant();
    bool offHandNeedsPoison = IsOffHandWeaponNoEnchant();

    LOG_INFO("server", "BotRogueSpells::ProcessUpPoison: 主手需要毒药: {}, 副手需要毒药: {}",
             mainHandNeedsPoison, offHandNeedsPoison);

    if (mainHandNeedsPoison)
    {
        if (UseInstantPoisonOnMainHand())
        {
            LOG_INFO("server", "BotRogueSpells::ProcessUpPoison: 主手武器上毒成功");
            applied = true;
        }
        else
        {
            LOG_WARN("server", "BotRogueSpells::ProcessUpPoison: 主手武器上毒失败");
        }
    }

    if (offHandNeedsPoison)
    {
        if (UseDeadlyPoisonOnOffHand())
        {
            LOG_INFO("server", "BotRogueSpells::ProcessUpPoison: 副手武器上毒成功");
            applied = true;
        }
        else
        {
            LOG_WARN("server", "BotRogueSpells::ProcessUpPoison: 副手武器上毒失败");
        }
    }

    LOG_INFO("server", "BotRogueSpells::ProcessUpPoison: 毒药检查完成，应用了毒药: {}", applied);
    return applied;
}

// 完全按照mod-playerbots-liyunfan实现的触发器
bool BotRogueSpells::IsMainHandWeaponNoEnchant()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();
    Item* itemForSpell = player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_MAINHAND);

    if (!itemForSpell)
    {
        LOG_DEBUG("server", "BotRogueSpells::IsMainHandWeaponNoEnchant: 没有主手武器");
        return false;
    }

    uint32 enchantId = itemForSpell->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
    bool hasEnchant = enchantId > 0;

    LOG_DEBUG("server", "BotRogueSpells::IsMainHandWeaponNoEnchant: 主手武器 {}, 附魔ID: {}, 需要毒药: {}",
              itemForSpell->GetTemplate()->Name1, enchantId, !hasEnchant);

    return !hasEnchant;
}

bool BotRogueSpells::IsOffHandWeaponNoEnchant()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();
    Item* itemForSpell = player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_OFFHAND);

    if (!itemForSpell)
    {
        LOG_DEBUG("server", "BotRogueSpells::IsOffHandWeaponNoEnchant: 没有副手武器");
        return false;
    }

    uint32 enchantId = itemForSpell->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
    bool hasEnchant = enchantId > 0;

    LOG_DEBUG("server", "BotRogueSpells::IsOffHandWeaponNoEnchant: 副手武器 {}, 附魔ID: {}, 需要毒药: {}",
              itemForSpell->GetTemplate()->Name1, enchantId, !hasEnchant);

    return !hasEnchant;
}









// 完全按照mod-playerbots-liyunfan实现的毒药使用
bool BotRogueSpells::UseInstantPoisonOnMainHand()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    LOG_INFO("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 开始查找速效毒药");

    // 按照mod-playerbots-liyunfan的逻辑查找毒药
    std::vector<Item*> items = FindPoisonItems("Instant Poison");
    if (items.empty())
    {
        LOG_WARN("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 未找到速效毒药，尝试创建");

        // 🔧 修复：根据等级创建合适的毒药
        uint32 poisonEntry = 0;
        uint32 level = player->GetLevel();

        if (level >= 79)
            poisonEntry = 43231; // Instant Poison IX
        else if (level >= 73)
            poisonEntry = 43230; // Instant Poison VIII
        else if (level >= 68)
            poisonEntry = 21927; // Instant Poison VII
        else if (level >= 60)
            poisonEntry = 8928;  // Instant Poison VI
        else if (level >= 52)
            poisonEntry = 8927;  // Instant Poison V
        else if (level >= 44)
            poisonEntry = 8926;  // Instant Poison IV
        else if (level >= 36)
            poisonEntry = 6950;  // Instant Poison III
        else if (level >= 28)
            poisonEntry = 6949;  // Instant Poison II
        else if (level >= 20)
            poisonEntry = 6947;  // Instant Poison I
        else
        {
            LOG_WARN("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 等级 {} 太低，无法使用毒药", level);
            return false;
        }

        Item* createdItem = StoreNewItemByEntry(player, poisonEntry);
        if (createdItem)
        {
            LOG_INFO("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 成功创建适合等级 {} 的速效毒药: {}",
                     level, createdItem->GetTemplate()->Name1);
            items.push_back(createdItem);
        }
        else
        {
            LOG_ERROR("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 无法创建毒药，物品ID: {}", poisonEntry);
            return false;
        }
    }
    else
    {
        LOG_INFO("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 找到速效毒药: {}", items[0]->GetTemplate()->Name1);
    }

    Item* weapon = player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_MAINHAND);
    if (!weapon)
    {
        LOG_WARN("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 没有主手武器");
        return false;
    }

    LOG_INFO("server", "BotRogueSpells::UseInstantPoisonOnMainHand: 尝试给主手武器 {} 上毒", weapon->GetTemplate()->Name1);
    return UseItemOnWeapon(items[0], weapon);
}

bool BotRogueSpells::UseDeadlyPoisonOnOffHand()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    LOG_INFO("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 开始查找致命毒药");

    // 按照mod-playerbots-liyunfan的逻辑查找毒药
    std::vector<Item*> items = FindPoisonItems("Deadly Poison");
    if (items.empty())
    {
        LOG_WARN("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 未找到致命毒药，尝试创建");

        // 🔧 修复：根据等级创建合适的毒药
        uint32 poisonEntry = 0;
        uint32 level = player->GetLevel();

        if (level >= 76)
            poisonEntry = 43233; // Deadly Poison VII
        else if (level >= 62)
            poisonEntry = 22054; // Deadly Poison VI
        else if (level >= 60)
            poisonEntry = 20844; // Deadly Poison V
        else if (level >= 46)
            poisonEntry = 8985;  // Deadly Poison IV
        else if (level >= 38)
            poisonEntry = 8984;  // Deadly Poison III
        else if (level >= 30)
            poisonEntry = 2893;  // Deadly Poison II
        else if (level >= 22)
            poisonEntry = 2892;  // Deadly Poison I
        else
        {
            LOG_WARN("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 等级 {} 太低，无法使用致命毒药", level);
            return false;
        }

        Item* createdItem = StoreNewItemByEntry(player, poisonEntry);
        if (createdItem)
        {
            LOG_INFO("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 成功创建适合等级 {} 的致命毒药: {}",
                     level, createdItem->GetTemplate()->Name1);
            items.push_back(createdItem);
        }
        else
        {
            LOG_ERROR("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 无法创建毒药，物品ID: {}", poisonEntry);
            return false;
        }
    }
    else
    {
        LOG_INFO("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 找到致命毒药: {}", items[0]->GetTemplate()->Name1);
    }

    Item* weapon = player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_OFFHAND);
    if (!weapon)
    {
        LOG_WARN("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 没有副手武器");
        return false;
    }

    LOG_INFO("server", "BotRogueSpells::UseDeadlyPoisonOnOffHand: 尝试给副手武器 {} 上毒", weapon->GetTemplate()->Name1);
    return UseItemOnWeapon(items[0], weapon);
}

std::vector<Item*> BotRogueSpells::FindPoisonItems(const std::string& baseName)
{
    if (!GetPlayer())
        return {};

    Player* player = GetPlayer();

    // 完全按照mod-playerbots-liyunfan的逻辑
    std::vector<std::string> poison_suffixs = {" IX", " VIII", " VII", " VI", " V", " IV", " III", " II", ""};
    std::vector<Item*> items;

    for (const std::string& suffix : poison_suffixs)
    {
        std::string poison_name = baseName + suffix;
        items = FindItemsByName(player, poison_name);
        if (!items.empty())
        {
            break;
        }
    }

    return items;
}

bool BotRogueSpells::UseItemOnWeapon(Item* item, Item* weapon)
{
    if (!item || !weapon || !GetPlayer())
    {
        LOG_ERROR("server", "BotRogueSpells::UseItemOnWeapon: 参数为空 - item:{}, weapon:{}, player:{}",
                  item != nullptr, weapon != nullptr, GetPlayer() != nullptr);
        return false;
    }

    Player* player = GetPlayer();

    // 使用UseItemAction的逻辑
    InventoryResult result = player->CanUseItem(item);
    if (result != EQUIP_ERR_OK)
    {
        LOG_WARN("server", "BotRogueSpells::UseItemOnWeapon: 无法使用物品 {}, 错误: {}",
                 item->GetTemplate()->Name1, static_cast<uint32>(result));
        return false;
    }

    if (player->IsNonMeleeSpellCast(false))
    {
        LOG_WARN("server", "BotRogueSpells::UseItemOnWeapon: 玩家正在施法中");
        return false;
    }

    // 获取物品的法术ID
    uint32 spellId = 0;
    for (uint8 i = 0; i < MAX_ITEM_PROTO_SPELLS; ++i)
    {
        if (item->GetTemplate()->Spells[i].SpellId > 0)
        {
            spellId = item->GetTemplate()->Spells[i].SpellId;
            LOG_DEBUG("server", "BotRogueSpells::UseItemOnWeapon: 找到法术ID: {}", spellId);
            break;
        }
    }

    if (spellId == 0)
    {
        LOG_ERROR("server", "BotRogueSpells::UseItemOnWeapon: 物品 {} 没有法术ID", item->GetTemplate()->Name1);
        return false;
    }

    LOG_INFO("server", "BotRogueSpells::UseItemOnWeapon: 准备使用毒药 {} (ID:{}) 到武器 {} (插槽:{},{})",
             item->GetTemplate()->Name1, item->GetEntry(), weapon->GetTemplate()->Name1,
             item->GetBagSlot(), item->GetSlot());

    // 创建使用物品的数据包 - 按照mod-playerbots-liyunfan的方式
    WorldPacket packet(CMSG_USE_ITEM);
    packet << item->GetBagSlot();
    packet << item->GetSlot();
    packet << uint8(1);  // cast_count
    packet << spellId;
    packet << item->GetGUID();
    packet << uint32(0); // glyphIndex
    packet << uint8(0);  // castFlags

    // 设置目标为武器
    packet << uint32(TARGET_FLAG_ITEM);
    packet << weapon->GetGUID().WriteAsPacked();

    // 发送数据包
    try
    {
        player->GetSession()->HandleUseItemOpcode(packet);
        LOG_INFO("server", "BotRogueSpells::UseItemOnWeapon: 成功发送使用毒药数据包");
        return true;
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "BotRogueSpells::UseItemOnWeapon: 发送数据包异常: {}", e.what());
        return false;
    }
}

// 完全按照mod-playerbots-liyunfan实现的潜行系统
bool BotRogueSpells::ShouldStealth()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    // 完全按照mod-playerbots-liyunfan的StealthTrigger逻辑
    if (player->HasAura(1784))
    {
        LOG_DEBUG("server", "BotRogueSpells::ShouldStealth: 已经潜行，不需要再潜行");
        return false;
    }

    if (player->IsInCombat())
    {
        LOG_DEBUG("server", "BotRogueSpells::ShouldStealth: 在战斗中，无法潜行");
        return false;
    }

    if (player->HasSpellCooldown(1784))
    {
        LOG_DEBUG("server", "BotRogueSpells::ShouldStealth: 潜行技能冷却中");
        return false;
    }

    float distance = 30.0f;

    // 查找目标 - 按照mod-playerbots-liyunfan的逻辑
    Unit* target = nullptr;

    // 首先查找当前目标
    if (player->GetSelectedUnit())
    {
        target = player->GetSelectedUnit();
    }

    if (!target)
    {
        LOG_DEBUG("server", "BotRogueSpells::ShouldStealth: 没有选择目标");
        return false;
    }

    // 按照mod-playerbots-liyunfan的距离调整逻辑
    if (target && target->GetVictim())
        distance -= 10;

    if (target->isMoving() && target->GetVictim())
        distance -= 10;

    float actualDistance = player->GetDistance(target);
    bool shouldStealth = actualDistance < distance;

    LOG_INFO("server", "BotRogueSpells::ShouldStealth: 目标:{}, 距离{:.1f}, 阈值{:.1f}, 应该潜行:{}",
             target->GetName(), actualDistance, distance, shouldStealth);

    return shouldStealth;
}

bool BotRogueSpells::CanStealth()
{
    if (!GetPlayer())
        return false;

    Player* player = GetPlayer();

    bool canStealth = player->GetLevel() > 10 && player->HasSpell(1784) && !player->IsInCombat();

    LOG_DEBUG("server", "BotRogueSpells::CanStealth: 等级:{}, 有潜行技能:{}, 不在战斗:{}, 可以潜行:{}",
              player->GetLevel(), player->HasSpell(1784), !player->IsInCombat(), canStealth);

    return canStealth;
}

// 实现潜行系统方法
void BotRogueSpells::OnCastSneak()
{
    if (!GetPlayer())
        return;

    Player* player = GetPlayer();
    LOG_INFO("server", "BotRogueSpells::OnCastSneak: 进入潜行状态，清除附近敌人的目标锁定");

    // 简化实现：遍历附近的敌人并清除它们的目标
    std::list<Unit*> nearbyEnemies;

    // 使用简单的范围搜索
    Map* map = player->GetMap();
    if (map)
    {
        Map::PlayerList const& players = map->GetPlayers();
        for (Map::PlayerList::const_iterator itr = players.begin(); itr != players.end(); ++itr)
        {
            Player* otherPlayer = itr->GetSource();
            if (otherPlayer && otherPlayer != player &&
                player->GetDistance(otherPlayer) <= 30.0f &&
                !player->IsFriendlyTo(otherPlayer))
            {
                nearbyEnemies.push_back(otherPlayer);
            }
        }
    }

    // 清除敌人的目标锁定
    for (Unit* enemy : nearbyEnemies)
    {
        if (enemy && enemy->GetTarget() == player->GetGUID())
        {
            enemy->SetTarget(ObjectGuid::Empty);
            LOG_DEBUG("server", "BotRogueSpells::OnCastSneak: 清除敌人 {} 的目标锁定", enemy->GetName());
        }
    }

    LOG_INFO("server", "BotRogueSpells::OnCastSneak: 潜行处理完成，清除了 {} 个敌人的目标锁定", nearbyEnemies.size());
}

void BotRogueSpells::OnCastFlash(Unit* pTarget)
{
    if (!GetPlayer() || !pTarget)
        return;

    Player* player = GetPlayer();

    // 暗影步后的处理
    player->GetMotionMaster()->Clear();
    Position pos = pTarget->GetPosition();

    // 简化实现：记录暗影步使用
    LOG_INFO("server", "BotRogueSpells::OnCastFlash: 使用暗影步接近目标 {}", pTarget->GetName());
}

// 实现HasAuraMechanic方法（这是导致链接错误的方法）
bool BotRogueSpells::HasAuraMechanic(Unit* pTarget, Mechanics mask)
{
    if (!pTarget)
        return false;

    // 检查目标是否有指定机制的光环
    return pTarget->HasAuraWithMechanic(1 << mask);
}

// 毒药系统辅助方法实现
Item* BotRogueSpells::FindItemFromAllBag(Player* player, uint32 itemEntry)
{
    if (!player)
        return nullptr;

    // 搜索背包中的物品
    for (uint8 i = INVENTORY_SLOT_ITEM_START; i < INVENTORY_SLOT_ITEM_END; ++i)
    {
        Item* item = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i);
        if (item && item->GetEntry() == itemEntry)
            return item;
    }

    // 搜索额外背包
    for (uint8 i = INVENTORY_SLOT_BAG_START; i < INVENTORY_SLOT_BAG_END; ++i)
    {
        if (Bag* bag = player->GetBagByPos(i))
        {
            for (uint32 j = 0; j < bag->GetBagSize(); ++j)
            {
                Item* item = bag->GetItemByPos(uint8(j));
                if (item && item->GetEntry() == itemEntry)
                    return item;
            }
        }
    }

    return nullptr;
}

Item* BotRogueSpells::StoreNewItemByEntry(Player* player, uint32 itemEntry)
{
    if (!player)
        return nullptr;

    ItemTemplate const* itemTemplate = sObjectMgr->GetItemTemplate(itemEntry);
    if (!itemTemplate)
        return nullptr;

    uint32 count = 1;
    uint32 noSpaceForCount = 0;
    ItemPosCountVec dest;
    InventoryResult msg = player->CanStoreNewItem(NULL_BAG, NULL_SLOT, dest, itemEntry, count, &noSpaceForCount);

    if (msg != EQUIP_ERR_OK)
        count -= noSpaceForCount;

    if (count <= 0 || dest.empty())
        return nullptr;

    Item* item = player->StoreNewItem(dest, itemEntry, true, Item::GenerateItemRandomPropertyId(itemEntry));
    if (item)
    {
        LOG_INFO("server", "BotRogueSpells::StoreNewItemByEntry: 为玩家 {} 创建物品 {}", player->GetName(), itemEntry);
    }

    return item;
}

std::vector<Item*> BotRogueSpells::FindItemsByName(Player* player, const std::string& itemName)
{
    std::vector<Item*> items;
    if (!player)
        return items;

    // 搜索背包中的物品
    for (uint8 i = INVENTORY_SLOT_ITEM_START; i < INVENTORY_SLOT_ITEM_END; ++i)
    {
        Item* item = player->GetItemByPos(INVENTORY_SLOT_BAG_0, i);
        if (item && item->GetTemplate()->Name1 == itemName)
            items.push_back(item);
    }

    // 搜索额外背包
    for (uint8 i = INVENTORY_SLOT_BAG_START; i < INVENTORY_SLOT_BAG_END; ++i)
    {
        if (Bag* bag = player->GetBagByPos(i))
        {
            for (uint32 j = 0; j < bag->GetBagSize(); ++j)
            {
                Item* item = bag->GetItemByPos(uint8(j));
                if (item && item->GetTemplate()->Name1 == itemName)
                    items.push_back(item);
            }
        }
    }

    return items;
}



uint32 BotRogueSpells::GetPoisonEntryByName(const std::string& poisonName)
{
    // 毒药名称到物品ID的映射
    static std::map<std::string, uint32> poisonMap = {
        // 速效毒药系列
        {"Instant Poison", 6947},
        {"Instant Poison II", 6949},
        {"Instant Poison III", 6950},
        {"Instant Poison IV", 8926},
        {"Instant Poison V", 8927},
        {"Instant Poison VI", 8928},
        {"Instant Poison VII", 21927},
        {"Instant Poison VIII", 43230},
        {"Instant Poison IX", 43231},

        // 致命毒药系列
        {"Deadly Poison", 2892},
        {"Deadly Poison II", 2893},
        {"Deadly Poison III", 8984},
        {"Deadly Poison IV", 8985},
        {"Deadly Poison V", 20844},
        {"Deadly Poison VI", 22054},
        {"Deadly Poison VII", 43233},
        {"Deadly Poison VIII", 43232},
        {"Deadly Poison IX", 43233},

        // 致伤毒药系列
        {"Wound Poison", 3775},
        {"Wound Poison II", 6952},
        {"Wound Poison III", 8930},
        {"Wound Poison IV", 10921},
        {"Wound Poison V", 10922},
        {"Wound Poison VI", 22055},
        {"Wound Poison VII", 43234},
        {"Wound Poison VIII", 43235}
    };

    auto it = poisonMap.find(poisonName);
    if (it != poisonMap.end())
    {
        return it->second;
    }

    LOG_WARN("server", "BotRogueSpells::GetPoisonEntryByName: 未知的毒药名称: {}", poisonName);
    return 0;
}



std::string BotRogueSpells::GetBestPoisonForLevel(uint32 level, EquipmentSlots slot)
{
    if (level < 20)
        return "";

    if (slot == EQUIPMENT_SLOT_MAINHAND)
    {
        // 主手武器：速效毒药
        if (level >= 80) return "Instant Poison IX";
        if (level >= 73) return "Instant Poison VIII";
        if (level >= 68) return "Instant Poison VII";
        if (level >= 60) return "Instant Poison VI";
        if (level >= 52) return "Instant Poison V";
        if (level >= 44) return "Instant Poison IV";
        if (level >= 36) return "Instant Poison III";
        if (level >= 28) return "Instant Poison II";
        return "Instant Poison";
    }
    else if (slot == EQUIPMENT_SLOT_OFFHAND)
    {
        // 副手武器：致命毒药
        if (level >= 80) return "Deadly Poison IX";
        if (level >= 70) return "Deadly Poison VII";
        if (level >= 60) return "Deadly Poison VI";
        if (level >= 54) return "Deadly Poison V";
        if (level >= 46) return "Deadly Poison IV";
        if (level >= 38) return "Deadly Poison III";
        if (level >= 30) return "Deadly Poison II";
        return "Deadly Poison";
    }

    return "";
}



// 盗贼天赋检测方法 - 使用TalentSystem
uint8 BotRogueSpells::GetTalentType(Player* player)
{
    if (!player)
        return 0;

    // 使用YGbotTalentDetector检测主要天赋专精
    uint32 primarySpec = YGbotTalentDetector::DetectPrimaryTalentSpec(player);

    // 对于盗贼：0=刺杀, 1=战斗, 2=敏锐
    return static_cast<uint8>(primarySpec);
}

bool BotRogueSpells::IsAssassinationSpec(Player* player)
{
    return GetTalentType(player) == 0;
}

bool BotRogueSpells::IsCombatSpec(Player* player)
{
    return GetTalentType(player) == 1;
}

bool BotRogueSpells::IsSubtletySpec(Player* player)
{
    return GetTalentType(player) == 2;
}

// AzerothCore模块系统要求的AddSC函数
void AddSC_BotClassSpells()
{
    // BotClassSpells是纯数据类，不需要注册脚本
    LOG_INFO("server", "AddSC_BotClassSpells: BotClassSpells系统已加载");
}

// BotWarriorSpells 实现
void BotWarriorSpells::InitializeSpells(Player* player)
{
    if (!player)
        return;

    InitializeDefanceSpells(player);
    InitializeWeaponSpells(player);
    InitializeRageSpells(player);

    LOG_INFO("server", "BotWarriorSpells: 为战士 {} 初始化技能完成", player->GetName());
}

void BotWarriorSpells::InitializeDefanceSpells(Player* player)
{
    WarriorDefance_Pull = FindMaxRankSpellByExist(355, player);         // 嘲讽
    WarriorDefance_Charge = FindMaxRankSpellByExist(100, player);       // 冲锋
    WarriorDefance_MaxLife = FindMaxRankSpellByExist(12975, player);    // 破釜沉舟
    WarriorDefance_ShieldBlock = FindMaxRankSpellByExist(2565, player); // 盾牌格挡
    WarriorDefance_ShieldWall = FindMaxRankSpellByExist(871, player);   // 盾墙
    WarriorDefance_Disarm = FindMaxRankSpellByExist(676, player);       // 缴械
}

void BotWarriorSpells::InitializeWeaponSpells(Player* player)
{
    WarriorWeapon_DeadAtt = FindMaxRankSpellByExist(47486, player);     // 致死打击
    WarriorWeapon_WhirlwindAtt = FindMaxRankSpellByExist(1680, player); // 旋风斩
    WarriorWeapon_Sweep = FindMaxRankSpellByExist(47520, player);       // 横扫攻击
    WarriorWeapon_Heroic = FindMaxRankSpellByExist(47450, player);      // 英勇打击
    WarriorWeapon_Thunder = FindMaxRankSpellByExist(47502, player);     // 雷霆一击
}

void BotWarriorSpells::InitializeRageSpells(Player* player)
{
    WarriorRage_Whirlwind = FindMaxRankSpellByExist(1680, player);      // 旋风斩
    WarriorRage_HeadAtt = FindMaxRankSpellByExist(12809, player);       // 拳击
    WarriorRage_Intercept = FindMaxRankSpellByExist(20252, player);     // 拦截
    WarriorRage_Berserker = FindMaxRankSpellByExist(18499, player);     // 狂暴

    WarriorCommon_PowerAtt = FindMaxRankSpellByExist(47450, player);    // 英勇打击
    WarriorCommon_PowerRelife = FindMaxRankSpellByExist(12975, player); // 破釜沉舟
}

// BotPaladinSpells 实现
void BotPaladinSpells::InitializeSpells(Player* player)
{
    if (!player)
        return;

    InitializeHolySpells(player);
    InitializeGuardSpells(player);
    InitializeJudgeSpells(player);

    LOG_INFO("server", "BotPaladinSpells: 为圣骑士 {} 初始化技能完成", player->GetName());
}

void BotPaladinSpells::InitializeHolySpells(Player* player)
{
    PaladinHoly_Light = FindMaxRankSpellByExist(48782, player);         // 圣光术
    PaladinHoly_Shock = FindMaxRankSpellByExist(48825, player);         // 神圣震击
    PaladinHoly_Wrath = FindMaxRankSpellByExist(48817, player);         // 愤怒之锤
    PaladinHoly_Consecration = FindMaxRankSpellByExist(48819, player);  // 奉献
}

void BotPaladinSpells::InitializeGuardSpells(Player* player)
{
    PaladinGuard_BlessArmor = FindMaxRankSpellByExist(27149, player);   // 护甲祝福
    PaladinGuard_BlessMight = FindMaxRankSpellByExist(48932, player);   // 力量祝福
    PaladinGuard_BlessWisdom = FindMaxRankSpellByExist(48936, player);  // 智慧祝福
    PaladinGuard_DivineShield = FindMaxRankSpellByExist(642, player);   // 圣盾术
    PaladinGuard_DivineProtection = FindMaxRankSpellByExist(498, player); // 圣佑术
}

void BotPaladinSpells::InitializeJudgeSpells(Player* player)
{
    PaladinJudge_Light = FindMaxRankSpellByExist(20271, player);        // 审判之光
    PaladinJudge_Wisdom = FindMaxRankSpellByExist(53408, player);       // 审判之智
    PaladinJudge_Justice = FindMaxRankSpellByExist(53407, player);      // 审判之正义
    PaladinJudge_Command = FindMaxRankSpellByExist(20425, player);      // 审判之令
}

// BotMageSpells 实现
void BotMageSpells::InitializeSpells(Player* player)
{
    if (!player)
        return;

    InitializeFireSpells(player);
    InitializeFrostSpells(player);
    InitializeArcaneSpells(player);

    LOG_INFO("server", "BotMageSpells: 为法师 {} 初始化技能完成", player->GetName());
}

void BotMageSpells::InitializeFireSpells(Player* player)
{
    MageFire_Fireball = FindMaxRankSpellByExist(42833, player);         // 火球术
    MageFire_Scorch = FindMaxRankSpellByExist(42859, player);           // 灼烧
    MageFire_Pyroblast = FindMaxRankSpellByExist(42891, player);        // 炎爆术
    MageFire_Combustion = FindMaxRankSpellByExist(11129, player);       // 燃烧
}

void BotMageSpells::InitializeFrostSpells(Player* player)
{
    MageFrost_Frostbolt = FindMaxRankSpellByExist(42842, player);       // 寒冰箭
    MageFrost_Blizzard = FindMaxRankSpellByExist(42940, player);        // 暴风雪
    MageFrost_IceBlock = FindMaxRankSpellByExist(45438, player);        // 寒冰屏障
    MageFrost_FrostNova = FindMaxRankSpellByExist(42917, player);       // 冰霜新星
}

void BotMageSpells::InitializeArcaneSpells(Player* player)
{
    MageArcane_Missiles = FindMaxRankSpellByExist(42846, player);       // 奥术飞弹
    MageArcane_Explosion = FindMaxRankSpellByExist(42921, player);      // 奥术爆炸
    MageArcane_Polymorph = FindMaxRankSpellByExist(12826, player);      // 变形术
    MageArcane_Counterspell = FindMaxRankSpellByExist(2139, player);    // 法术反制
}
