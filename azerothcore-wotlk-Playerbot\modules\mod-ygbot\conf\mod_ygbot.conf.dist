#
# This file is part of the AzerothCore Project. See AUTHORS file for Copyright information
#
# This file is free software; as a special exception the author gives
# unlimited permission to copy and/or distribute it, with or without
# modifications, as long as this notice is preserved.
#
# This program is distributed in the hope that it will be useful, but
# WITHOUT ANY WARRANTY, to the extent permitted by law; without even the
# implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.
#

[worldserver]

########################################
# YGbot模块配置
########################################

# 启用YGbot模块
YGbot.Enable = 1

# 最大假人数量
YGbot.MaxPlayers = 0

# 初始假人数量
YGbot.StartPlayers = 0

# 更新间隔（毫秒）
YGbot.UpdateInterval = 5000

# 机器人账号前缀
AiPlayerbot.RandomBotAccountPrefix = rndbot

########################################
# 交互响应配置 (百分比 0-100)
########################################

# 公会邀请响应概率 (默认: 70%)
YGbot.Response.GuildInvite = 70

# 组队邀请响应概率 (默认: 80%)
YGbot.Response.GroupInvite = 80

# 竞技场战队邀请响应概率 (默认: 60%)
YGbot.Response.ArenaTeamInvite = 60

# 交易请求响应概率 (默认: 75%)
YGbot.Response.TradeRequest = 75

# 交易确认响应概率 (默认: 90%)
YGbot.Response.TradeAccept = 90

# 公会章程签名响应概率 (默认: 60%)
YGbot.Response.GuildCharter = 60

# 竞技场章程签名响应概率 (默认: 50%)
YGbot.Response.ArenaCharter = 50

# 决斗请求响应概率 (默认: 80%)
YGbot.Response.DuelRequest = 80

# 战场邀请响应概率 (默认: 70%)
YGbot.Response.BattlefieldInvite = 70

########################################
# 天赋雕文系统配置
########################################
# 天赋预设名称格式：YGbot.PremadeSpecName.<职业ID>.<排序ID> = <专精名称>
# 职业ID对照：
# 1 = 战士 (Warrior)
# 2 = 圣骑士 (Paladin)
# 3 = 猎人 (Hunter)
# 4 = 盗贼 (Rogue)
# 5 = 牧师 (Priest)
# 6 = 死亡骑士 (Death Knight)
# 7 = 萨满 (Shaman)
# 8 = 法师 (Mage)
# 9 = 术士 (Warlock)
# 11 = 德鲁伊 (Druid)

# 排序ID：只用来排序无他用

# 雕文预设格式YGbot.PremadeSpecGlyph.<职业ID>.<排序ID> = <大雕文ID,小雕文ID,大雕文ID,小雕文ID,大雕文ID,小雕文ID>
# 例如:YGbot.PremadeSpecGlyph.1.0 = 43418,43395,43423,43399,49084,43421

# 天赋链接格式说明
# 使用NFU天赋模拟器加点方案的完整链接，直接复制网站地址链接黏贴即可。
# 例如：https://www.nfuwow.com/talents/80/warrior/index.html?3020322023335100002012213231251305043000020000000000000000000000000000000000000000000

########################################

# 战士预设天赋雕文

YGbot.PremadeSpecName.1.0 = 武器
YGbot.PremadeSpecLink.1.0.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3020322023335100002012213231251305043000020000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.1.0 = 43418,43395,43423,43399,49084,43421

YGbot.PremadeSpecName.1.1 = 狂暴
YGbot.PremadeSpecLink.1.1.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3200320123300000000000000000000305053000500310053120511351000000000000000000000000000
YGbot.PremadeSpecGlyph.1.1 = 43418,43395,43414,43399,49084,43432

YGbot.PremadeSpecName.1.2 = 防护
YGbot.PremadeSpecLink.1.2.80 = https://www.nfuwow.com/talents/80/warrior/index.html?3502000023000000000000000000000300000000000000000000000000053351225000212521030113321
YGbot.PremadeSpecGlyph.1.2 = 43424,43395,43425,43399,49084,45793

########################################

# 圣骑士预设天赋雕文

YGbot.PremadeSpecName.2.0 = 神圣
YGbot.PremadeSpecLink.2.0.80 = https://www.nfuwow.com/talents/80/paladin/index.html?503511523000130531005152215032113020000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.2.0 = 41106,43367,45741,43369,43365,41109

YGbot.PremadeSpecName.2.1 = 防护
YGbot.PremadeSpecLink.2.1.80 = https://www.nfuwow.com/talents/80/paladin/index.html?000000000000000000000000000530503500310231133331232150230201200300000000000000
YGbot.PremadeSpecGlyph.2.1 = 41099,43367,43869,43369,43365,45745

YGbot.PremadeSpecName.2.2 = 惩戒
YGbot.PremadeSpecLink.2.2.80 = https://www.nfuwow.com/talents/80/paladin/index.html?050321000000000000000000000500000000000000000000000005232051203331302133231331
YGbot.PremadeSpecGlyph.2.2 = 41092,43367,41099,43369,43365,43869


########################################

# 猎人预设天赋雕文

YGbot.PremadeSpecName.3.0 = 兽王
YGbot.PremadeSpecLink.3.0.80 = https://www.nfuwow.com/talents/80/hunter/index.html?050032015252122301305313510050052310000000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.3.0 = 42912,43350,42902,43351,43338,45732

YGbot.PremadeSpecName.3.1 = 射击
YGbot.PremadeSpecLink.3.1.80 = https://www.nfuwow.com/talents/80/hunter/index.html?502000000000000000000000000253051012300132330352313515000002000000000000000000000
YGbot.PremadeSpecGlyph.3.1 = 42912,43350,42914,43351,43338,45732

YGbot.PremadeSpecName.3.2 = 生存
YGbot.PremadeSpecLink.3.2.80 = https://www.nfuwow.com/talents/80/hunter/index.html?000000000000000000000000000053051010000000000000000005000032500033330522135301331
YGbot.PremadeSpecGlyph.3.2 = 42912,43350,45731,43351,43338,45732


########################################

# 盗贼预设天赋雕文

YGbot.PremadeSpecName.4.0 = 刺杀
YGbot.PremadeSpecLink.4.0.80 = https://www.nfuwow.com/talents/80/rogue/index.html?00530300535210052010333105100500500500300000000000000002000000000000000000000000000
YGbot.PremadeSpecGlyph.4.0 = 45768,43379,45761,43380,43378,45766

YGbot.PremadeSpecName.4.1 = 战斗
YGbot.PremadeSpecLink.4.1.80 = https://www.nfuwow.com/talents/80/rogue/index.html?30520000514000000000000000002520510000350152231005012510000000000000000000000000000
YGbot.PremadeSpecGlyph.4.1 = 42962,43379,45762,43380,43378,42969

YGbot.PremadeSpecName.4.2 = 敏锐
YGbot.PremadeSpecLink.4.2.80 = https://www.nfuwow.com/talents/80/rogue/index.html?30530010500000000000000000000000000000000000000000000005320232030302121050135201251
YGbot.PremadeSpecGlyph.4.2 = 42967,43379,45764,43380,43378,45767


########################################

# 牧师预设天赋雕文

YGbot.PremadeSpecName.5.0 = 戒律
YGbot.PremadeSpecLink.5.0.80 = https://www.nfuwow.com/talents/80/priest/index.html?0503203130300512331323231251105500030000000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.5.0 = 42408,43371,42400,43374,43342,45756

YGbot.PremadeSpecName.5.1 = 神圣
YGbot.PremadeSpecLink.5.1.80 = https://www.nfuwow.com/talents/80/priest/index.html?0503203100000000000000000000225501032002152530320311351000000000000000000000000000
YGbot.PremadeSpecGlyph.5.1 = 42408,43371,42400,43374,43342,42396

YGbot.PremadeSpecName.5.2 = 暗影
YGbot.PremadeSpecLink.5.2.80 = https://www.nfuwow.com/talents/80/priest/index.html?0503203000000000000000000000000000000000000000000000000325023051223012123152301351
YGbot.PremadeSpecGlyph.5.2 = 42406,43371,42407,43374,43342,42415


########################################

# 死亡骑士预设天赋雕文

YGbot.PremadeSpecName.6.0 = 鲜血
YGbot.PremadeSpecLink.6.0.80 = https://www.nfuwow.com/talents/80/deathknight/index.html?0055121533303313201020131350005000000000000000000000000000052300000000000000000000000000
YGbot.PremadeSpecGlyph.6.0 = 45805,43673,43827,43544,43672,43554

YGbot.PremadeSpecName.6.1 = 冰霜
YGbot.PremadeSpecLink.6.1.80 = https://www.nfuwow.com/talents/80/deathknight/index.html?0000000000000000000000000000320023503522030123000331013512302003050030000000000000000000
YGbot.PremadeSpecGlyph.6.1 = 45805,43673,43547,43544,43672,43543

YGbot.PremadeSpecName.6.2 = 邪恶
YGbot.PremadeSpecLink.6.2.80 = https://www.nfuwow.com/talents/80/deathknight/index.html?0000000000000000000000000000320050500002000000000000000002302003350032152000150003133151
YGbot.PremadeSpecGlyph.6.2 = 43542,43673,43546,43544,43672,43549


########################################

# 萨满预设天赋雕文

YGbot.PremadeSpecName.7.0 = 元素
YGbot.PremadeSpecLink.7.0.80 = https://www.nfuwow.com/talents/80/shaman/index.html?05330015232133513223013513020500310000000000000000000000000000000000000000000000
YGbot.PremadeSpecGlyph.7.0 = 41536,43385,41532,43386,44923,45776

YGbot.PremadeSpecName.7.1 = 增强
YGbot.PremadeSpecLink.7.1.80 = https://www.nfuwow.com/talents/80/shaman/index.html?05303005200000000000000003020503310502133303113103105100000000000000000000000000
YGbot.PremadeSpecGlyph.7.1 = 41542,43385,41539,43386,44923,45771

YGbot.PremadeSpecName.7.2 = 恢复
YGbot.PremadeSpecLink.7.2.80 = https://www.nfuwow.com/talents/80/shaman/index.html?00000000000000000000000000050503000000000000000000000050005331335310501122331251
YGbot.PremadeSpecGlyph.7.2 = 41517,43385,41527,43386,44923,45775


########################################

# 法师预设天赋雕文

YGbot.PremadeSpecName.8.0 = 奥术
YGbot.PremadeSpecLink.8.0.80 = https://www.nfuwow.com/talents/80/mage/index.html?23002513010033015032310251532103000000000000000000000000002030230010000000000000000000
YGbot.PremadeSpecGlyph.8.0 = 42735,43339,44955,43364,43361,42751

YGbot.PremadeSpecName.8.1 = 火焰
YGbot.PremadeSpecLink.8.1.80 = https://www.nfuwow.com/talents/80/mage/index.html?23000503110003000000000000000000550320123033300531203003510000000000000000000000000000
YGbot.PremadeSpecGlyph.8.1 = 42739,43339,45737,43364,44920,42751

YGbot.PremadeSpecName.8.2 = 冰霜
YGbot.PremadeSpecLink.8.2.80 = https://www.nfuwow.com/talents/80/mage/index.html?23002500310003000000000000000000000000000000000000000000000533030310233100230152231051
YGbot.PremadeSpecGlyph.8.2 = 42742,43339,50045,43364,43361,42751


########################################

# 术士预设天赋雕文

YGbot.PremadeSpecName.9.0 = 痛苦
YGbot.PremadeSpecLink.9.0.80 = https://www.nfuwow.com/talents/80/warlock/index.html?205022201102351025351033115100000000000000000000000000055000005000000000000000000
YGbot.PremadeSpecGlyph.9.0 = 45785,43390,50077,43394,43393,45779

YGbot.PremadeSpecName.9.1 = 恶魔
YGbot.PremadeSpecLink.9.1.80 = https://www.nfuwow.com/talents/80/warlock/index.html?000000000000000000000000000000320330113520253013522135155000005000000000000000000
YGbot.PremadeSpecGlyph.9.1 = 45785,43390,50077,43394,43393,42459

YGbot.PremadeSpecName.9.2 = 毁灭
YGbot.PremadeSpecLink.9.2.80 = https://www.nfuwow.com/talents/80/warlock/index.html?000000000000000000000000000003220030003000000000000000005203205220231051335230351
YGbot.PremadeSpecGlyph.9.2 = 45785,43390,50077,43394,43393,42454


########################################

# 德鲁伊预设天赋雕文

YGbot.PremadeSpecName.11.0 = 平衡
YGbot.PremadeSpecLink.11.0.80 = https://www.nfuwow.com/talents/80/druid/index.html?5102203125331303213315311031000000000000000000000000000000205003012000000000000000000
YGbot.PremadeSpecGlyph.11.0 = 40916,43331,40921,43335,44922,40919

YGbot.PremadeSpecName.11.1 = 野性
YGbot.PremadeSpecLink.11.1.80 = https://www.nfuwow.com/talents/80/druid/index.html?0000000000000000000000000000503232130320010353120303213511203203012000000000000000000
YGbot.PremadeSpecGlyph.11.1 = 40897,43331,46372,43335,43332,40899

YGbot.PremadeSpecName.11.2 = 恢复
YGbot.PremadeSpecLink.11.2.80 = https://www.nfuwow.com/talents/80/druid/index.html?1532003100300000000000000000000000000000000000000000000000230033312031502511050313051
YGbot.PremadeSpecGlyph.11.2 = 40913,43331,40906,43335,44922,45602

########################################
# 机器人自动学习技能系统配置
########################################

# 启用机器人自动学习技能系统
BotAutoLearn.Enable = 1

# 机器人自动学习技能的最大等级
BotAutoLearn.MaxLevel = 80

# 是否在机器人首次登录时学习技能
BotAutoLearn.OnFirstLogin = 1

# ========================================
# 新的训练师学习系统（推荐）
# ========================================

# 启用训练师学习系统（参考mod-playerbots-liyunfan）
# 这是核心系统，从游戏训练师学习所有职业技能
BotAutoLearn.UseTrainerSystem = 1

# 启用关键技能补充系统
BotAutoLearn.UseEssentialSpells = 1

# 启用武器技能熟练度更新
BotAutoLearn.UpdateWeaponSkills = 1

# ========================================
# 旧系统配置（已禁用，保留用于兼容性）
# ========================================

# 是否学习种族技能（已禁用 - 由训练师系统处理）
BotAutoLearn.RacialSpells = 0

# 是否自动提升武器技能熟练度到满级（已禁用 - 使用UpdateWeaponSkills代替）
BotAutoLearn.WeaponSkills = 0

# 是否学习专业技能（已禁用 - 机器人不需要商业技能）
BotAutoLearn.ProfessionSpells = 0

# 是否根据天赋专精学习相应技能（已禁用 - 由训练师系统处理）
BotAutoLearn.TalentSpecificSpells = 0

########################################
# 机器人自动天赋雕文系统配置
########################################

# 启用机器人自动应用天赋和雕文系统
BotAutoTalentGlyph.Enable = 1

# 是否随机选择天赋配置（0=使用第一个配置, 1=随机选择）
BotAutoTalentGlyph.RandomSelection = 1

# 是否在机器人登录时应用天赋雕文
BotAutoTalentGlyph.ApplyOnLogin = 1

# 是否在机器人升级时应用天赋雕文
BotAutoTalentGlyph.ApplyOnLevelUp = 1

