#include "GroupRogueAI.h"
#include "Log.h"
#include "../BotAITool.h"
#include "ObjectAccessor.h"
#include "WorldPacket.h"
#include "Opcodes.h"
#include "Map.h"
#include "Player.h"
#include <list>

GroupRogueAI::GroupRogueAI(Player* player) : BotGroupAI(player), BotRogueSpells()
{
    LOG_INFO("server", "GroupRogueAI: 为盗贼玩家 {} 创建组队AI", player->GetName());
    UpdateTalentType();

    // 初始化盗贼技能
    InitializeSpells(player);

    // 场景AI层不再负责非战斗上毒（移至Faker系统）

    // 设置队长（如果在组队中）
    if (player->GetGroup())
    {
        m_MasterPlayer = ObjectAccessor::FindConnectedPlayer(player->GetGroup()->GetLeaderGUID());
    }

    // 场景AI层不再负责非战斗上毒（移至Faker系统）
}

void GroupRogueAI::ResetBotAI()
{
    BotGroupAI::ResetBotAI();
    UpdateTalentType();
    m_IsUpedPoison = 0;
    
    // 初始化盗贼技能
    InitializeSpells(m_player);
}

void GroupRogueAI::ProcessMeleeSpell(Unit* pTarget)
{
    if (!pTarget)
        return;

    // 1. 潜行开场检查（非战斗状态下）
    if (!m_player->IsInCombat() && !m_player->HasAura(RogueGuard_Sneak))
    {
        LOG_INFO("server", "GroupRogueAI::ProcessMeleeSpell: 非战斗状态，尝试潜行");
        if (TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "GroupRogueAI::ProcessMeleeSpell: 潜行成功");
            OnCastSneak();
            return;
        }
    }

    // 2. 潜行状态下的技能处理
    if (ProcessSneakSpell(pTarget))
        return;

    // 3. 能量检查
    if (!CanStartSpell())
    {
        LOG_DEBUG("server", "GroupRogueAI::ProcessMeleeSpell: 能量不足，等待恢复");
        return;
    }

    // 4. 致盲控制
    if (ProcessMeleeBlind(pTarget))
        return;

    // 5. 多目标AOE检测
    std::vector<Unit*> enemies = RangeEnemyListByTargetIsMe(BotAITool::NEEDFLEE_CHECKRANGE);
    if (enemies.size() > 1 && TryCastSpell(RogueGuard_Dodge, m_player) == SpellCastResult::SPELL_CAST_OK)
        return;

    if (RangeEnemyListByNonAura(0, BotAITool::NEEDFLEE_CHECKRANGE).size() > 2)
    {
        if (RogueAOE_AllDance && TryCastSpell(RogueAOE_AllDance, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
        if (RogueAOE_Knife && TryCastSpell(RogueAOE_Knife, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
    }

    // 6. 解控技能
    if (CastCloakByNeed())
        return;

    // 7. 打断技能
    if (RogueAssist_BlockCast && pTarget->HasUnitState(UNIT_STATE_CASTING) &&
        TryCastSpell(RogueAssist_BlockCast, pTarget) == SpellCastResult::SPELL_CAST_OK)
        return;

    // 8. 根据天赋类型执行专精化轮换
    LOG_INFO("server", "GroupRogueAI::ProcessMeleeSpell: 执行天赋轮换，天赋类型: {}", m_BotTalentType);

    switch (m_BotTalentType)
    {
        case 0: // 刺杀天赋
            ProcessAssassinationRotation(pTarget);
            break;
        case 1: // 战斗天赋
            ProcessCombatRotation(pTarget);
            break;
        case 2: // 敏锐天赋
            ProcessSubtletyRotation(pTarget);
            break;
        default:
            ProcessGenericRotation(pTarget);
            break;
    }
}

void GroupRogueAI::ProcessRangeSpell(Unit* pTarget)
{
    if (m_player->GetSelectedUnit() != pTarget)
        return;

    if (m_player->GetSelectedUnit() && m_player->GetDistance(pTarget) < 20)
    {
        if (!m_player->IsInCombat() && TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            OnCastSneak();
            return;
        }
        if (ProcessSneakSpell(pTarget))
            return;
        if (ProcessMeleeBlind(pTarget))
            return;
    }

    if (RogueAssist_ShadowFlash)
    {
        if (m_player->HasAura(RogueGuard_Sneak) || (!BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_ROOT) && !BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_STUN)))
        {
            if (TryCastSpell(RogueAssist_ShadowFlash, pTarget) == SpellCastResult::SPELL_CAST_OK)
            {
                OnCastFlash(pTarget);
                return;
            }
        }
    }

    uint8 combo = m_player->GetComboPoints();
    if (RogueRange_Throw && pTarget->HasUnitState(UNIT_STATE_CASTING) && combo > 0)
    {
        if (TryCastSpell(RogueRange_Throw, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
    }
    else if (RogueRange_Throw && combo > 2)
    {
        if (TryCastSpell(RogueRange_Throw, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
    }

    if (CastCloakByNeed())
        return;
}

bool GroupRogueAI::ProcessNormalSpell()
{
    if (m_player->IsInCombat())
        return false;

    // 完全按keji逻辑
    if (!m_player->IsMounted() && ProcessUpPoison())
        return true;

    Unit* pUnit = m_player->GetSelectedUnit();
    if (pUnit && pUnit->IsAlive())
    {
        if (!m_player->IsInCombat() && TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            OnCastSneak();
            return false;
        }
    }
    else
    {
        if (m_player->HasAura(RogueGuard_Sneak))
        {
            m_player->RemoveOwnedAura(RogueGuard_Sneak, ObjectGuid::Empty, 0, AURA_REMOVE_BY_CANCEL);
        }
    }
    if (!m_player->HasAura(RogueGuard_Sneak) && TryUpMount())
        return true;
    return false;
}

void GroupRogueAI::UpEnergy()
{
    uint32 energyPer = GetEnergyPowerPer();
    if (energyPer < 20 && !m_player->HasAura(RogueGuard_Sprint))
    {
        if (TryCastSpell(RogueGuard_Sprint, m_player) == SpellCastResult::SPELL_CAST_OK)
            return;
    }
}

bool GroupRogueAI::NeedFlee()
{
    if (m_Fleeing)
        return true;

    if (!m_player->IsInCombat())
        return false;

    uint32 manaPct = GetEnergyPowerPer();
    if (manaPct < 10)
        return true;

    uint32 lifePct = GetLifePCT(m_player);
    if (lifePct < 20)
        return true;

    std::vector<Unit*> enemys = RangeEnemyListByTargetIsMe(BotAITool::NEEDFLEE_CHECKRANGE);
    if (enemys.size() > 1)
    {
        if (TryCastSpell(RogueGuard_Sprint, m_player) == SpellCastResult::SPELL_CAST_OK)
            return false;
        if (TryCastSpell(RogueGuard_Dodge, m_player) == SpellCastResult::SPELL_CAST_OK)
            return false;
        if (!BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_BLEED))
        {
            if (TryCastSpell(RogueGuard_Disappear, m_player) == SpellCastResult::SPELL_CAST_OK)
            {
                m_player->AttackStop();
                return false;
            }
        }
        if (m_BotTalentType == 2)
        {
            if (RogueAssist_ShadowDance && !m_player->HasAura(RogueFlag_Dance) && 
                TryCastSpell(RogueAssist_ShadowDance, m_player) == SpellCastResult::SPELL_CAST_OK)
                return false;
            if (RogueAssist_ReadyCD && TryCastSpell(RogueAssist_ReadyCD, m_player) == SpellCastResult::SPELL_CAST_OK)
                return false;
        }
    }

    return false;
}

void GroupRogueAI::ProcessSeduceSpell(Unit* pTarget)
{
    if (!pTarget) return;
    ProcessMeleeSpell(pTarget);
}

uint32 GroupRogueAI::GetSeducePriority()
{
    if (!m_player || !m_player->IsAlive())
        return 0;
    return 3;
}

void GroupRogueAI::UpdateTalentType()
{
    // 使用BotRogueSpells的天赋检测方法
    m_BotTalentType = GetTalentType(m_player);

    LOG_INFO("server", "GroupRogueAI::UpdateTalentType: 盗贼 {} 天赋类型: {} (0=刺杀, 1=战斗, 2=敏锐)",
             m_player->GetName(), m_BotTalentType);

    // 根据天赋类型调整战斗策略
    // 0 = 刺杀, 1 = 战斗, 2 = 敏锐
}

uint32 GroupRogueAI::GetEnergyPowerPer()
{
    // 委托给BotRogueSpells的实现
    return BotRogueSpells::GetEnergyPowerPer();
}

uint32 GroupRogueAI::GetPoisonEntryByWeaponType(EquipmentSlots equipSlot)
{
    // 委托给BotRogueSpells的实现
    return BotRogueSpells::GetPoisonEntryByWeaponType(equipSlot);
}

bool GroupRogueAI::ProcessUpPoison()
{
    // 委托给BotRogueSpells的实现
    return BotRogueSpells::ProcessUpPoison();
}

// ProcessSneakSpell方法的重复定义已删除 - 使用文件末尾的完整实现

bool GroupRogueAI::ProcessMeleeBlind(Unit* pTarget)
{
    std::vector<Unit*> enemies = RangeEnemyListByTargetIsMe(BotAITool::NEEDFLEE_CHECKRANGE);
    for (Unit* player : enemies)
    {
        if (player == pTarget || !CanBlind(pTarget))
            continue;
        if (m_player->HasAura(RogueFlag_Dance))
        {
            if (TryCastSpell(RogueSneak_Surprise, pTarget) == SpellCastResult::SPELL_CAST_OK)
                return true;
        }
        else if (m_BotTalentType == 2 && RogueAssist_ShadowDance)
        {
            if (TryCastSpell(RogueAssist_ShadowDance, m_player) == SpellCastResult::SPELL_CAST_OK)
                return true;
        }
        if (RogueAssist_Blind && TryCastSpell(RogueAssist_Blind, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return true;
        return false;
    }
    return false;
}

bool GroupRogueAI::CanConsumeCombo(Unit* pTarget)
{
    if (!pTarget)
        return false;
    uint8 combo = m_player->GetComboPoints();
    float targetLife = pTarget->GetHealthPct();
    if (targetLife < 3)
        return true;
    else if (targetLife < 10 && combo > 2)
        return true;
    else if (combo >= 5)
        return true;
    return false;
}

bool GroupRogueAI::CastCloakByNeed()
{
    if (!RogueGuard_ShadowCloak)
        return false;
    if (BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_ROOT))
    {
        if (TryCastSpell(RogueGuard_ShadowCloak, m_player) == SpellCastResult::SPELL_CAST_OK)
            return true;
    }
    if (BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_SNARE))
    {
        if (TryCastSpell(RogueGuard_ShadowCloak, m_player) == SpellCastResult::SPELL_CAST_OK)
            return true;
    }
    return false;
}

// OnCastSneak方法已在文件末尾实现，删除重复定义

void GroupRogueAI::OnCastFlash(Unit* pTarget)
{
    // 暗影闪现后的处理
}

// 重写基类的关键方法
void GroupRogueAI::UpdateBotAI(uint32 diff)
{
    // 🔧 添加调试日志，确认方法被调用
    static uint32 debugCallCount = 0;
    if (++debugCallCount % 100 == 0) // 每100次调用输出一次日志
    {
        LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 方法被调用，调用次数: {}, 战斗状态: {}",
                 debugCallCount, m_player ? m_player->IsInCombat() : false);
    }

    if (!m_player || !m_player->IsInWorld())
        return;

    // 更新天赋类型
    static uint32 talentCheckTick = 0;
    if (++talentCheckTick % 100 == 0) // 每10秒检查一次
        UpdateTalentType();

    // 🔧 修复：只在非战斗时检查毒药
    // 战斗中不检查毒药，避免影响战斗性能和逻辑
    if (!m_player->IsInCombat())
    {
        static uint32 lastPoisonCheck = 0;
        uint32 currentTime = getMSTime();

        // 每3秒检查一次毒药状态
        if (lastPoisonCheck == 0 || (currentTime - lastPoisonCheck) > 3000)
        {
            LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 非战斗状态，开始毒药检查");

            // 检查主手武器是否需要毒药
            if (IsMainHandWeaponNoEnchant())
            {
                LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 主手武器需要毒药，执行上毒");
                if (UseInstantPoisonOnMainHand())
                {
                    LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 主手武器上毒成功");
                }
                else
                {
                    LOG_WARN("server", "GroupRogueAI::UpdateBotAI: 主手武器上毒失败");
                }
            }
            else
            {
                LOG_DEBUG("server", "GroupRogueAI::UpdateBotAI: 主手武器已有毒药");
            }

            // 检查副手武器是否需要毒药
            if (IsOffHandWeaponNoEnchant())
            {
                LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 副手武器需要毒药，执行上毒");
                if (UseDeadlyPoisonOnOffHand())
                {
                    LOG_INFO("server", "GroupRogueAI::UpdateBotAI: 副手武器上毒成功");
                }
                else
                {
                    LOG_WARN("server", "GroupRogueAI::UpdateBotAI: 副手武器上毒失败");
                }
            }
            else
            {
                LOG_DEBUG("server", "GroupRogueAI::UpdateBotAI: 副手武器已有毒药");
            }

            lastPoisonCheck = currentTime;
        }
    }
    else
    {
        LOG_DEBUG("server", "GroupRogueAI::UpdateBotAI: 战斗中，跳过毒药检查");
    }

    // 场景AI层不再负责非战斗上毒（移至Faker系统）

    // 调用基类的更新逻辑
    BotGroupAI::UpdateBotAI(diff);
}

void GroupRogueAI::ProcessBotCommand(Player* srcPlayer, const std::string& cmd)
{
    if (!srcPlayer || !m_player)
        return;

    std::string param;
    if (!CanReciveCommand(cmd, param))
        return;

    if (cmd == "attack")
    {
        ProcessAttackCommand();
        LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到攻击命令", m_player->GetName());
    }
    else if (cmd == "follow")
    {
        m_player->SetSelection(ObjectGuid::Empty);
        m_ForceFlee = false;
        // 其他跟随逻辑
        LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到跟随命令", m_player->GetName());
    }
    else if (cmd == "summon")
    {
        ProcessSummonCommand();
        LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到召唤命令", m_player->GetName());
    }
    else if (cmd == "flee")
    {
        ProcessFleeCommand();
        LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到逃跑命令", m_player->GetName());
    }
    else if (cmd == "stop")
    {
        ProcessStopCommand();
        LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到停止命令", m_player->GetName());
    }
}

void GroupRogueAI::ProcessAttackCommand()
{
    if (!m_MasterPlayer)
        return;

    Unit* pMasterTarget = m_MasterPlayer->GetSelectedUnit();
    if (!pMasterTarget || !pMasterTarget->IsAlive())
        return;
    if (!m_player->IsValidAttackTarget(pMasterTarget))
        return;

    m_player->SetSelection(pMasterTarget->GetGUID());
    m_ForceFlee = false;

    // 盗贼特殊处理：立即尝试潜行开场
    float distance = m_player->GetDistance(pMasterTarget);
    LOG_INFO("server", "GroupRogueAI: 盗贼 {} 收到攻击命令，目标 {}，距离 {:.1f}码",
             m_player->GetName(), pMasterTarget->GetName(), distance);

    // 立即尝试潜行（盗贼的标志性开场）
    if (!m_player->IsInCombat() && !m_player->HasAura(RogueGuard_Sneak))
    {
        LOG_INFO("server", "GroupRogueAI: 尝试潜行开场");
        if (TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "GroupRogueAI: 潜行成功，准备偷袭");
            OnCastSneak();
            return;
        }
        else
        {
            LOG_WARN("server", "GroupRogueAI: 潜行失败，将进行常规攻击");
        }
    }
    else if (m_player->HasAura(RogueGuard_Sneak))
    {
        LOG_INFO("server", "GroupRogueAI: 已在潜行状态，准备偷袭");
    }
    else
    {
        LOG_INFO("server", "GroupRogueAI: 已在战斗中，无法潜行");
    }

    // 如果距离较远，先移动接近
    if (distance > 25.0f)
    {
        LOG_INFO("server", "GroupRogueAI: 距离较远，开始接近目标");
        // 让移动系统处理接近
    }
}

// 实现基类的一些关键方法
bool GroupRogueAI::CanReciveCommand(const std::string& cmd, std::string& param)
{
    // 简化的命令接收检查
    return true;
}

void GroupRogueAI::ProcessSummonCommand()
{
    // 召唤命令处理
    if (!m_MasterPlayer)
        return;
    m_ForceFlee = false;
    m_player->SetSelection(ObjectGuid::Empty);
}

void GroupRogueAI::ProcessFleeCommand()
{
    // 逃跑命令处理
    m_ForceFlee = true;
    m_player->AttackStop();
}

void GroupRogueAI::ProcessStopCommand()
{
    // 停止命令处理
    m_player->AttackStop();
    m_player->SetSelection(ObjectGuid::Empty);
    m_ForceFlee = false;
}

SpellCastResult GroupRogueAI::TryCastSpell(uint32 spellID, Unit* pTarget, bool force)
{
    if (!spellID || !m_player)
    {
        LOG_ERROR("server", "GroupRogueAI::TryCastSpell: 技能ID或玩家为空");
        return SPELL_FAILED_DONT_REPORT;
    }

    if (!pTarget)
        pTarget = m_player;

    LOG_INFO("server", "GroupRogueAI::TryCastSpell: 尝试释放技能 {} 对目标 {}",
             spellID, pTarget ? pTarget->GetName() : "自己");

    // 详细的状态检查
    LOG_INFO("server", "GroupRogueAI::TryCastSpell: 机器人状态 - 移动:{}, 移动状态:{}, 施法:{}",
             m_player->isMoving(),
             m_player->HasUnitState(UNIT_STATE_MOVING),
             m_player->HasUnitState(UNIT_STATE_CASTING));

    // 检查技能是否存在
    if (!m_player->HasSpell(spellID))
    {
        LOG_ERROR("server", "GroupRogueAI::TryCastSpell: 机器人 {} 没有技能 {}", m_player->GetName(), spellID);
        return SPELL_FAILED_SPELL_LEARNED;
    }

    // 检查冷却时间
    if (m_player->HasSpellCooldown(spellID))
    {
        LOG_INFO("server", "GroupRogueAI::TryCastSpell: 技能 {} 正在冷却中", spellID);
        return SPELL_FAILED_NOT_READY;
    }

    // 检查能量
    uint32 energyPer = BotRogueSpells::GetEnergyPowerPer();
    if (energyPer < 20) // 需要20%能量
    {
        LOG_INFO("server", "GroupRogueAI::TryCastSpell: 能量不足 {}%", energyPer);
        return SPELL_FAILED_NO_POWER;
    }

    // 检查目标
    if (pTarget && !m_player->IsValidAttackTarget(pTarget))
    {
        LOG_ERROR("server", "GroupRogueAI::TryCastSpell: 无效目标");
        return SPELL_FAILED_BAD_TARGETS;
    }

    // 面向目标
    if (pTarget && pTarget != m_player)
    {
        m_player->SetFacingToObject(pTarget);
    }

    // 强制停止移动以确保技能释放（修复错误98）
    if (m_player->isMoving() || m_player->HasUnitState(UNIT_STATE_MOVING))
    {
        LOG_INFO("server", "GroupRogueAI::TryCastSpell: 机器人正在移动，强制停止");
        m_player->StopMoving();
        m_player->GetMotionMaster()->Clear();
        m_player->GetMotionMaster()->MoveIdle();

        // 等待移动状态清除
        if (m_player->HasUnitState(UNIT_STATE_MOVING))
        {
            m_player->ClearUnitState(UNIT_STATE_MOVING);
        }
    }

    // 确保面向目标
    if (pTarget && pTarget != m_player)
    {
        m_player->SetFacingToObject(pTarget);
    }

    // 释放技能 - 参考mod-jbbot的实现
    SpellCastResult result = m_player->CastSpell(pTarget, spellID, false);

    if (result == SPELL_CAST_OK)
    {
        LOG_INFO("server", "GroupRogueAI::TryCastSpell: 机器人 {} 成功释放技能 {} 对目标 {}",
                 m_player->GetName(), spellID, pTarget ? pTarget->GetName() : "自己");
    }
    else
    {
        LOG_ERROR("server", "GroupRogueAI::TryCastSpell: 机器人 {} 释放技能 {} 失败，错误: {}",
                 m_player->GetName(), spellID, static_cast<uint32>(result));

        // 如果正常释放失败，尝试使用触发模式（仅在force为true时）
        if (force)
        {
            LOG_INFO("server", "GroupRogueAI::TryCastSpell: 尝试强制释放技能");
            result = m_player->CastSpell(pTarget, spellID, TRIGGERED_IGNORE_POWER_AND_REAGENT_COST);
            if (result == SPELL_CAST_OK)
            {
                LOG_INFO("server", "GroupRogueAI::TryCastSpell: 强制释放技能成功");
            }
        }
    }

    return result;
}

// HasAuraMechanic方法已在BotRogueSpells基类中实现，这里删除重复定义

bool GroupRogueAI::CanBlind(Unit* pTarget)
{
    if (!pTarget)
        return false;

    // 检查目标是否可以被致盲
    return !BotRogueSpells::HasAuraMechanic(pTarget, Mechanics::MECHANIC_CHARM) &&
           !BotRogueSpells::HasAuraMechanic(pTarget, Mechanics::MECHANIC_DISORIENTED);
}

bool GroupRogueAI::CanStartSpell()
{
    return GetEnergyPowerPer() >= 60;
}

uint32 GroupRogueAI::GetLifePCT(Unit* pTarget)
{
    if (!pTarget)
        return 0;

    return pTarget->GetHealthPct();
}

// 实现搜索方法的简化版本
std::vector<Unit*> GroupRogueAI::RangeEnemyListByTargetIsMe(float range)
{
    std::vector<Unit*> enemies;
    if (!m_player)
        return enemies;

    // 使用引擎维护的攻击者集合
    for (Unit* u : m_player->getAttackers())
    {
        if (!u || !u->IsAlive()) continue;
        if (m_player->GetDistance(u) > range) continue;
        enemies.push_back(u);
    }
    return enemies;
}

std::vector<Unit*> GroupRogueAI::RangeEnemyListByNonAura(uint32 aura, float range)
{
    std::vector<Unit*> enemies;
    if (!m_player)
        return enemies;

    for (Unit* u : m_player->getAttackers())
    {
        if (!u || !u->IsAlive()) continue;
        if (m_player->GetDistance(u) > range) continue;
        if (aura == 0 || !u->HasAura(aura))
            enemies.push_back(u);
    }
    return enemies;
}

// 参考mod-pbot-keji的潜行技能处理方法
bool GroupRogueAI::ProcessSneakSpell(Unit* pTarget)
{
    if (!pTarget || !m_player->HasAura(RogueGuard_Sneak))
        return false;

    LOG_INFO("server", "GroupRogueAI::ProcessSneakSpell: 处理潜行状态下的技能，目标: {}", pTarget->GetName());

    // 优先使用偷袭
    if (RogueSneak_Surprise > 0 && TryCastSpell(RogueSneak_Surprise, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "GroupRogueAI::ProcessSneakSpell: 偷袭成功，技能ID: {}", RogueSneak_Surprise);
        return true;
    }

    // 备用：伏击（需要在目标背后）
    if (RogueSneak_Ambush > 0 && TryCastSpell(RogueSneak_Ambush, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "GroupRogueAI::ProcessSneakSpell: 伏击成功，技能ID: {}", RogueSneak_Ambush);
        return true;
    }

    // 备用：背刺
    if (RogueAttack_BackAtt > 0 && TryCastSpell(RogueAttack_BackAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
    {
        LOG_INFO("server", "GroupRogueAI::ProcessSneakSpell: 背刺成功，技能ID: {}", RogueAttack_BackAtt);
        return true;
    }

    LOG_INFO("server", "GroupRogueAI::ProcessSneakSpell: 潜行状态下无法释放任何技能");
    return false;
}

// 潜行后的处理（参考mod-pbot-keji）
void GroupRogueAI::OnCastSneak()
{
    LOG_INFO("server", "GroupRogueAI::OnCastSneak: 进入潜行状态，清除附近敌人的目标锁定");

    // 简化实现：遍历附近的敌人并清除它们的目标
    std::list<Unit*> nearbyEnemies;

    // 使用简单的范围搜索，避免复杂的Trinity命名空间问题
    Map* map = m_player->GetMap();
    if (map)
    {
        Map::PlayerList const& players = map->GetPlayers();
        for (Map::PlayerList::const_iterator itr = players.begin(); itr != players.end(); ++itr)
        {
            Player* player = itr->GetSource();
            if (player && player != m_player &&
                m_player->GetDistance(player) <= 30.0f &&
                !m_player->IsFriendlyTo(player))
            {
                nearbyEnemies.push_back(player);
            }
        }
    }

    // 清除敌人的目标锁定
    for (Unit* enemy : nearbyEnemies)
    {
        if (enemy && enemy->GetTarget() == m_player->GetGUID())
        {
            enemy->SetTarget(ObjectGuid::Empty);
            LOG_DEBUG("server", "GroupRogueAI::OnCastSneak: 清除敌人 {} 的目标锁定", enemy->GetName());
        }
    }

    LOG_INFO("server", "GroupRogueAI::OnCastSneak: 潜行处理完成，清除了 {} 个敌人的目标锁定", nearbyEnemies.size());
}

// 刺杀天赋轮换逻辑
void GroupRogueAI::ProcessAssassinationRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查多目标AOE
    auto nearbyEnemies = RangeEnemyListByNonAura(0, BotAITool::NEEDFLEE_CHECKRANGE);
    if (nearbyEnemies.size() > 3)
    {
        // AOE输出：刀扇
        if (RogueAOE_Knife && energy >= 60 && TryCastSpell(RogueAOE_Knife, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用刀扇进行AOE输出");
            return;
        }
    }

    // 检查重要BUFF和DEBUFF
    bool hasSliceAndDice = m_player->HasAura(5171);  // 切割BUFF
    bool hasRupture = pTarget->HasAura(1943, m_player->GetGUID());  // 割裂DOT
    bool hasDeadlyPoison = pTarget->HasAuraState(AURA_STATE_DEADLY_POISON, nullptr, m_player);

    LOG_INFO("server", "刺杀天赋状态: 连击点={}, 能量={}, 切割={}, 割裂={}, 致命毒药={}",
             combo, energy, hasSliceAndDice, hasRupture, hasDeadlyPoison);

    // 优先级1: 保持切割BUFF（对自己施放）
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用切割提升攻击速度");
            return;
        }
    }

    // 优先级2: 割裂DOT（对目标施放）
    if (!hasRupture && combo >= 2 && energy >= 25)
    {
        // 这里应该用割裂技能，暂时用切割代替，需要在BotClassSpells中添加割裂技能
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用割裂DOT");
            return;
        }
    }

    // 优先级3: 毒伤爆发（需要致命毒药状态）
    if (hasDeadlyPoison && combo >= 3 && energy >= 35)
    {
        if (RogueAttack_PoisonDmg && TryCastSpell(RogueAttack_PoisonDmg, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用毒伤，连击点: {}, 能量: {}", combo, energy);
            return;
        }
    }

    // 优先级4: 5星剔骨（高伤害终结技）
    if (combo >= 5 && energy >= 35)
    {
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 5星剔骨高伤害");
            return;
        }
    }

    // 优先级4: 连击点建立
    if (combo < 5)
    {
        // 毁伤 - 刺杀天赋主要攒星技能
        if (RogueAttack_Injure && energy >= 40 && TryCastSpell(RogueAttack_Injure, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用毁伤攒连击点");
            return;
        }

        // 邪恶攻击作为备选
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用邪恶攻击攒连击点");
            return;
        }
    }

    // 冷血爆发
    if (combo >= 4 && RogueAssist_NextCrit && !m_player->HasSpellCooldown(14177))
    {
        if (TryCastSpell(RogueAssist_NextCrit, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "刺杀天赋: 使用冷血提升爆发");
            return;
        }
    }
}

// 战斗天赋轮换逻辑
void GroupRogueAI::ProcessCombatRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查重要BUFF
    bool hasSliceAndDice = m_player->HasAura(5171);  // 切割BUFF

    LOG_INFO("server", "战斗天赋状态: 连击点={}, 能量={}, 切割={}",
             combo, energy, hasSliceAndDice);

    // 优先级1: 切割优先级最高（对自己施放）
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 开启切割提升攻击速度");
            return;
        }
    }

    // 优先级2: 5星剔骨（战斗天赋核心输出）
    if (combo >= 5 && energy >= 35)
    {
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 5星剔骨高额伤害");
            return;
        }
    }

    // 优先级3: 刷新切割时间（切割时间不足时）
    if (hasSliceAndDice && combo >= 2 && energy >= 25)
    {
        // 检查切割剩余时间，如果少于8秒就刷新
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 刷新切割时间");
            return;
        }
    }

    // 优先级3: 连击点建立 - 影袭(邪恶攻击)
    if (combo < 5)
    {
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 使用影袭攒连击点");
            return;
        }

        // 背刺作为备选
        if (RogueAttack_BackAtt && energy >= 40 && TryCastSpell(RogueAttack_BackAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "战斗天赋: 使用背刺攒连击点");
            return;
        }
    }

    // 爆发技能：剑刃乱舞、杀戮盛宴、冲动
    if (combo >= 3 && energy >= 60)
    {
        // 剑刃乱舞
        if (RogueAOE_AllDance && !m_player->HasSpellCooldown(13877))
        {
            if (TryCastSpell(RogueAOE_AllDance, m_player) == SpellCastResult::SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 开启剑刃乱舞");
                return;
            }
        }

        // 冲动
        if (RogueAssist_FastEnergy && !m_player->HasSpellCooldown(13750))
        {
            if (TryCastSpell(RogueAssist_FastEnergy, m_player) == SpellCastResult::SPELL_CAST_OK)
            {
                LOG_INFO("server", "战斗天赋: 使用冲动提升能量恢复");
                return;
            }
        }
    }
}

// 敏锐天赋轮换逻辑
void GroupRogueAI::ProcessSubtletyRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 检查重要BUFF和DEBUFF
    bool hasSliceAndDice = m_player->HasAura(5171);   // 切割BUFF
    bool hasRupture = pTarget->HasAura(1943, m_player->GetGUID());  // 割裂DOT
    bool hasShadowDance = m_player->HasAura(51713);    // 暗影之舞

    LOG_INFO("server", "敏锐天赋状态: 连击点={}, 能量={}, 切割={}, 割裂={}, 暗影之舞={}",
             combo, energy, hasSliceAndDice, hasRupture, hasShadowDance);

    // 优先级1: 切割BUFF（对自己施放）
    if (!hasSliceAndDice && combo >= 1 && energy >= 25)
    {
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 开启切割");
            return;
        }
    }

    // 优先级2: 5星割裂DOT（对目标施放）
    if (!hasRupture && combo >= 5 && energy >= 25)
    {
        // 这里应该用割裂技能，暂时用剔骨代替
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 5星割裂DOT");
            return;
        }
    }

    // 优先级3: 养精蓄锐（回血回能量）
    if (combo >= 2 && energy >= 25 && m_player->GetHealthPct() < 80)
    {
        // 暂时用剔骨代替养精蓄锐，需要在BotClassSpells中添加养精蓄锐技能
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 养精蓄锐回复");
            return;
        }
    }

    // 优先级4: 5星剔骨（高伤害输出）
    if (combo >= 5 && hasSliceAndDice && energy >= 35)
    {
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 5星剔骨高伤害");
            return;
        }
    }

    // 暗影之舞爆发
    if (!hasShadowDance && combo >= 3 && RogueAssist_ShadowDance && !m_player->HasSpellCooldown(51713))
    {
        if (TryCastSpell(RogueAssist_ShadowDance, m_player) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 开启暗影之舞");
            return;
        }
    }

    // 暗影步接近目标
    if (m_player->GetDistance(pTarget) > 8.0f && RogueAssist_ShadowFlash && !m_player->HasSpellCooldown(36554))
    {
        if (TryCastSpell(RogueAssist_ShadowFlash, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 暗影步接近目标");
            return;
        }
    }

    // 连击点建立
    if (combo < 5 && energy >= 40)
    {
        // 在目标背后使用背刺
        if (RogueAttack_BackAtt && TryCastSpell(RogueAttack_BackAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 背刺攒星");
            return;
        }

        // 在目标正面使用出血
        if (RogueAttack_Blood && TryCastSpell(RogueAttack_Blood, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 出血攒星");
            return;
        }

        // 邪恶攻击作为备选
        if (RogueAttack_EvilAtt && energy >= 60 && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 邪恶攻击攒星");
            return;
        }
    }

    // 能量即将溢出时的处理
    if (energyNearCap && combo >= 1)
    {
        if (RogueAttack_Damage && energy >= 35 && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
        {
            LOG_INFO("server", "敏锐天赋: 能量溢出，使用剔骨");
            return;
        }
    }
}

// 通用轮换逻辑（备用）
void GroupRogueAI::ProcessGenericRotation(Unit* pTarget)
{
    if (!pTarget)
        return;

    uint8 combo = m_player->GetComboPoints();
    uint32 energy = m_player->GetPower(POWER_ENERGY);

    // 简单的通用轮换
    if (combo >= 3 && energy >= 25)
    {
        if (RogueAttack_Damage && TryCastSpell(RogueAttack_Damage, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
        if (RogueAttack_Separate && TryCastSpell(RogueAttack_Separate, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
    }

    if (combo < 5 && energy >= 40)
    {
        if (RogueAttack_EvilAtt && TryCastSpell(RogueAttack_EvilAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
        if (RogueAttack_BackAtt && TryCastSpell(RogueAttack_BackAtt, pTarget) == SpellCastResult::SPELL_CAST_OK)
            return;
    }
}

uint32 GroupRogueAI::GetAuraRemainingTime(Unit* unit, uint32 spellId)
{
    if (!unit)
        return 0;

    if (Aura* aura = unit->GetAura(spellId))
    {
        return aura->GetDuration();
    }

    return 0;
}

bool GroupRogueAI::IsPoisonBuffExpiringSoon()
{
    if (!m_player)
        return false;

    // 检查主手武器毒药buff剩余时间
    Item* mainHand = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_MAINHAND);
    if (mainHand)
    {
        uint32 enchantId = mainHand->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
        if (enchantId > 0)
        {
            uint32 duration = mainHand->GetEnchantmentDuration(TEMP_ENCHANTMENT_SLOT);
            uint32 remainingMinutes = duration / 60000; // 转换为分钟

            LOG_DEBUG("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 主手武器毒药剩余时间: {}分钟", remainingMinutes);

            if (remainingMinutes < 5)
            {
                LOG_INFO("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 主手武器毒药即将过期");
                return true;
            }
        }
        else
        {
            LOG_DEBUG("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 主手武器没有毒药buff");
            return true; // 没有毒药buff也需要上毒
        }
    }

    // 检查副手武器毒药buff剩余时间
    Item* offHand = m_player->GetItemByPos(INVENTORY_SLOT_BAG_0, EQUIPMENT_SLOT_OFFHAND);
    if (offHand)
    {
        uint32 enchantId = offHand->GetEnchantmentId(TEMP_ENCHANTMENT_SLOT);
        if (enchantId > 0)
        {
            uint32 duration = offHand->GetEnchantmentDuration(TEMP_ENCHANTMENT_SLOT);
            uint32 remainingMinutes = duration / 60000; // 转换为分钟

            LOG_DEBUG("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 副手武器毒药剩余时间: {}分钟", remainingMinutes);

            if (remainingMinutes < 5)
            {
                LOG_INFO("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 副手武器毒药即将过期");
                return true;
            }
        }
        else
        {
            LOG_DEBUG("server", "GroupRogueAI::IsPoisonBuffExpiringSoon: 副手武器没有毒药buff");
            return true; // 没有毒药buff也需要上毒
        }
    }

    return false;
}

void GroupRogueAI::ProcessFlee()
{
    // FleeMovement(); // 暂时注释，需要实现移动系统
    if (!m_player->IsInCombat() && TryCastSpell(RogueGuard_Sneak, m_player) == SpellCastResult::SPELL_CAST_OK)
    {
        OnCastSneak();
        return;
    }
    if (m_player->HasAura(RogueGuard_Sneak))
        return;

    if (CastCloakByNeed())
        return;
    std::vector<Unit*> enemies = RangeEnemyListByTargetIsMe(BotAITool::NEEDFLEE_CHECKRANGE);
    for (Unit* player : enemies)
    {
        if (!BotRogueSpells::HasAuraMechanic(player, Mechanics::MECHANIC_CHARM) && !BotRogueSpells::HasAuraMechanic(player, Mechanics::MECHANIC_DISORIENTED))
        {
            if (TryCastSpell(RogueAssist_Blind, player) == SpellCastResult::SPELL_CAST_OK)
                return;
        }
        if (!BotRogueSpells::HasAuraMechanic(player, Mechanics::MECHANIC_KNOCKOUT))
        {
            if (TryCastSpell(RogueAssist_Paralyze, player) == SpellCastResult::SPELL_CAST_OK)
                return;
        }
        if (m_player->HasAura(RogueFlag_Dance) && !BotRogueSpells::HasAuraMechanic(player, Mechanics::MECHANIC_STUN))
        {
            if (TryCastSpell(RogueSneak_Surprise, player) == SpellCastResult::SPELL_CAST_OK)
                return;
        }
    }
    if (enemies.size() > 1)
    {
        if (TryCastSpell(RogueGuard_Sprint, m_player) == SpellCastResult::SPELL_CAST_OK)
            return;
        if (TryCastSpell(RogueGuard_Dodge, m_player) == SpellCastResult::SPELL_CAST_OK)
            return;
        if (!BotRogueSpells::HasAuraMechanic(m_player, Mechanics::MECHANIC_BLEED))
        {
            if (TryCastSpell(RogueGuard_Disappear, m_player) == SpellCastResult::SPELL_CAST_OK)
            {
                m_player->AttackStop();
                return;
            }
        }
        if (m_BotTalentType == 2)
        {
            if (RogueAssist_ShadowDance && !m_player->HasAura(RogueFlag_Dance) && TryCastSpell(RogueAssist_ShadowDance, m_player) == SpellCastResult::SPELL_CAST_OK)
                return;
            if (RogueAssist_ReadyCD && TryCastSpell(RogueAssist_ReadyCD, m_player) == SpellCastResult::SPELL_CAST_OK)
                return;
        }
    }
}

bool GroupRogueAI::TryBlockCastingByTarget(Unit* pTarget)
{
    if (!pTarget)
        return false;
    if (RogueAssist_BlockCast && TryCastSpell(RogueAssist_BlockCast, pTarget) == SpellCastResult::SPELL_CAST_OK)
        return true;
    return false;
}
