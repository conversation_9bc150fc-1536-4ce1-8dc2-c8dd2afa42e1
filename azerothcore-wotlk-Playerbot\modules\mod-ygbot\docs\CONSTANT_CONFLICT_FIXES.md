# 常量冲突修复

## 问题描述

在战斗系统中定义的常量与现有的MovementSystem中的常量发生了命名冲突：

### 冲突的常量
- `BOT_MOVEMENT_UPDATE_INTERVAL` - 在MovementSystem和CombatSystem中都有定义
- `BOT_MOVEMENT_TIMEOUT` - 在MovementSystem和CombatSystem中都有定义

## 解决方案

为了避免命名冲突，将战斗系统中的移动相关常量重命名，添加 `COMBAT_` 前缀以区分：

### 重命名映射表

| 原常量名 | 新常量名 |
|----------|----------|
| `BOT_MOVEMENT_UPDATE_INTERVAL` | `BOT_COMBAT_MOVEMENT_UPDATE_INTERVAL` |
| `BOT_MOVEMENT_POSITION_TOLERANCE` | `BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE` |
| `BOT_MOVEMENT_TIMEOUT` | `BOT_COMBAT_MOVEMENT_TIMEOUT` |
| `BOT_MOVEMENT_MAX_PATHFIND_ATTEMPTS` | `BOT_COMBAT_MOVEMENT_MAX_PATHFIND_ATTEMPTS` |

## 修改的文件

### BotCombatMovement.h
```cpp
// 修改前
extern const uint32 BOT_MOVEMENT_UPDATE_INTERVAL;
extern const float BOT_MOVEMENT_POSITION_TOLERANCE;
extern const float BOT_MOVEMENT_TIMEOUT;
extern const uint32 BOT_MOVEMENT_MAX_PATHFIND_ATTEMPTS;

// 修改后
extern const uint32 BOT_COMBAT_MOVEMENT_UPDATE_INTERVAL;
extern const float BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE;
extern const float BOT_COMBAT_MOVEMENT_TIMEOUT;
extern const uint32 BOT_COMBAT_MOVEMENT_MAX_PATHFIND_ATTEMPTS;
```

### BotCombatMovement.cpp
```cpp
// 修改前
const uint32 BOT_MOVEMENT_UPDATE_INTERVAL = 200;
const float BOT_MOVEMENT_POSITION_TOLERANCE = 1.5f;
const float BOT_MOVEMENT_TIMEOUT = 10000.0f;
const uint32 BOT_MOVEMENT_MAX_PATHFIND_ATTEMPTS = 3;

// 修改后
const uint32 BOT_COMBAT_MOVEMENT_UPDATE_INTERVAL = 200;
const float BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE = 1.5f;
const float BOT_COMBAT_MOVEMENT_TIMEOUT = 10000.0f;
const uint32 BOT_COMBAT_MOVEMENT_MAX_PATHFIND_ATTEMPTS = 3;
```

## 更新的使用位置

在 `BotCombatMovement.cpp` 中更新了以下使用位置：

1. **Update方法中的间隔检查**
   ```cpp
   if (currentTime < m_lastUpdateTime + BOT_COMBAT_MOVEMENT_UPDATE_INTERVAL)
   ```

2. **距离容差检查**
   ```cpp
   if (currentDistance <= optimalDistance + BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE)
   ```

3. **移动超时检查**
   ```cpp
   if (currentTime > m_movementState.startTime + BOT_COMBAT_MOVEMENT_TIMEOUT)
   ```

4. **位置到达检查**
   ```cpp
   return distance <= BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE;
   ```

5. **移动需求判断**
   ```cpp
   return std::abs(currentDistance - optimalDistance) > BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE;
   ```

6. **最优距离检查**
   ```cpp
   return std::abs(currentDistance - optimalDistance) <= BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE;
   ```

## 常量值对比

### MovementSystem 常量 (BotMovementConstants.h)
- `BOT_MOVEMENT_UPDATE_INTERVAL` = 100ms (通用移动系统更新间隔)
- `BOT_MOVEMENT_TIMEOUT` = 30000ms (通用移动超时)

### CombatSystem 常量 (BotCombatMovement.h/.cpp)
- `BOT_COMBAT_MOVEMENT_UPDATE_INTERVAL` = 200ms (战斗移动更新间隔，较慢)
- `BOT_COMBAT_MOVEMENT_TIMEOUT` = 10000ms (战斗移动超时，较短)
- `BOT_COMBAT_MOVEMENT_POSITION_TOLERANCE` = 1.5f (战斗位置容差)
- `BOT_COMBAT_MOVEMENT_MAX_PATHFIND_ATTEMPTS` = 3 (最大寻路尝试次数)

## 设计理念

### 为什么使用不同的值？

1. **更新间隔差异**
   - 通用移动系统：100ms (更频繁，适用于精确移动)
   - 战斗移动系统：200ms (较慢，减少战斗中的性能开销)

2. **超时时间差异**
   - 通用移动系统：30000ms (30秒，适用于长距离移动)
   - 战斗移动系统：10000ms (10秒，战斗中需要快速响应)

3. **独立性**
   - 战斗移动系统有自己的参数调优需求
   - 避免与通用移动系统的参数冲突
   - 便于独立调试和优化

## 验证清单

- [x] 重命名所有冲突的常量
- [x] 更新头文件中的声明
- [x] 更新实现文件中的定义
- [x] 更新所有使用位置
- [x] 确保常量值符合战斗系统需求
- [x] 验证与MovementSystem的独立性

## 预期结果

修复后应该解决以下编译错误：
- ✅ C2371 "BOT_MOVEMENT_TIMEOUT": 重定义；不同的基类型
- ✅ C2370 "BOT_MOVEMENT_UPDATE_INTERVAL": 重定义；不同的存储类

现在战斗系统和移动系统可以独立工作，各自使用适合自己需求的参数值。
