# 机器人攻击逻辑修复

## 问题描述

从日志可以看到：
```
MakeBotAttackTarget: 机器人 哀木替 攻击目标 老杂斑野猪
StartCombatUpdateTimer: 为机器人 哀木替 启动战斗更新定时器
MakeBotAttackTarget: 机器人 哀木替 攻击设置完成，开始战斗
命令机器人 哀木替 攻击目标 老杂斑野猪
命令处理结果: 成功
机器人不会攻击  <-- 问题在这里
```

机器人接收到攻击命令，设置了目标，启动了战斗AI，但是没有实际开始攻击。

## 根本原因

经过代码分析发现，战斗AI系统缺少关键的攻击逻辑：

1. **BotCombatStrategy::ExecuteStrategy()** - 只处理技能施放，没有基本攻击
2. **BotCombatAI::EnterCombat()** - 只设置目标，没有调用Attack()
3. **BotCombatAI::SetTarget()** - 只设置目标选择，没有开始攻击

在魔兽世界中，机器人需要显式调用 `Player::Attack(target, true)` 方法来开始自动攻击。

## 修复方案

### 1. 修复BotCombatStrategy::ExecuteStrategy()

**位置**: `BotCombatStrategy.cpp:104-116`

**修复前**:
```cpp
// 执行常规战斗逻辑
Unit* target = m_combatAI->GetCurrentTarget();
BotCombatState combatState = m_combatAI->GetCombatState();

uint32 nextSpell = SelectNextSpell(combatState, target);
if (nextSpell > 0)
{
    if (m_combatAI->TryCastSpell(nextSpell, target))
    {
        m_lastSpellUsed = nextSpell;
        LogDebug("执行技能: " + std::to_string(nextSpell));
    }
}
```

**修复后**:
```cpp
// 执行常规战斗逻辑
Unit* target = m_combatAI->GetCurrentTarget();
BotCombatState combatState = m_combatAI->GetCombatState();

// 确保机器人正在攻击目标
if (target && target->IsAlive())
{
    // 检查是否需要开始自动攻击
    if (!m_bot->GetVictim() || m_bot->GetVictim() != target)
    {
        m_bot->Attack(target, true);
        LogDebug("开始攻击目标: " + target->GetName());
    }
    
    // 确保机器人面向目标
    if (!m_bot->HasInArc(M_PI, target))
    {
        m_bot->SetFacingToObject(target);
    }
}

uint32 nextSpell = SelectNextSpell(combatState, target);
if (nextSpell > 0)
{
    if (m_combatAI->TryCastSpell(nextSpell, target))
    {
        m_lastSpellUsed = nextSpell;
        LogDebug("执行技能: " + std::to_string(nextSpell));
    }
}
```

### 2. 修复BotCombatAI::EnterCombat()

**位置**: `BotCombatAI.cpp:268-281`

**修复前**:
```cpp
// 设置目标
if (target)
{
    SetTarget(target);
}
else
{
    // 自动选择目标
    Unit* autoTarget = SelectBestTarget();
    if (autoTarget)
    {
        SetTarget(autoTarget);
    }
}
```

**修复后**:
```cpp
// 设置目标
if (target)
{
    SetTarget(target);
    // 立即开始攻击目标
    if (target->IsAlive() && m_bot->IsValidAttackTarget(target))
    {
        m_bot->Attack(target, true);
        m_bot->SetFacingToObject(target);
        LogDebug("开始攻击目标: " + target->GetName());
    }
}
else
{
    // 自动选择目标
    Unit* autoTarget = SelectBestTarget();
    if (autoTarget)
    {
        SetTarget(autoTarget);
        // 立即开始攻击自动选择的目标
        if (autoTarget->IsAlive() && m_bot->IsValidAttackTarget(autoTarget))
        {
            m_bot->Attack(autoTarget, true);
            m_bot->SetFacingToObject(autoTarget);
            LogDebug("开始攻击自动目标: " + autoTarget->GetName());
        }
    }
}
```

### 3. 修复BotCombatAI::SetTarget()

**位置**: `BotCombatAI.cpp:370-376`

**修复前**:
```cpp
// 设置新目标
m_combatData.currentTarget = newTargetGuid;

// 通知机器人选择目标
m_bot->SetTarget(newTargetGuid);

LogDebug("设置新目标: " + target->GetName());
```

**修复后**:
```cpp
// 设置新目标
m_combatData.currentTarget = newTargetGuid;

// 通知机器人选择目标
m_bot->SetTarget(newTargetGuid);

// 如果在战斗中，立即开始攻击新目标
if (IsInCombat() && target->IsAlive() && m_bot->IsValidAttackTarget(target))
{
    m_bot->Attack(target, true);
    m_bot->SetFacingToObject(target);
    LogDebug("开始攻击新目标: " + target->GetName());
}
else
{
    LogDebug("设置新目标: " + target->GetName());
}
```

## 修复逻辑说明

### 关键API调用
1. **m_bot->Attack(target, true)** - 开始自动攻击目标
2. **m_bot->SetFacingToObject(target)** - 确保机器人面向目标
3. **m_bot->GetVictim()** - 检查当前攻击的目标
4. **m_bot->IsValidAttackTarget(target)** - 验证目标是否可攻击

### 攻击条件检查
- 目标必须存活 (`target->IsAlive()`)
- 目标必须是有效攻击目标 (`m_bot->IsValidAttackTarget(target)`)
- 机器人当前没有攻击目标或攻击的不是当前目标

### 面向逻辑
- 使用 `m_bot->HasInArc(M_PI, target)` 检查是否面向目标
- 使用 `m_bot->SetFacingToObject(target)` 调整朝向

## 预期效果

修复后，机器人应该能够：

1. **立即开始攻击** - 收到攻击命令后立即开始自动攻击
2. **持续攻击** - 在战斗过程中持续攻击当前目标
3. **正确朝向** - 始终面向攻击目标
4. **目标切换** - 切换目标时立即攻击新目标

## 测试验证

修复后应该看到类似的日志：
```
MakeBotAttackTarget: 机器人 哀木替 攻击目标 老杂斑野猪
开始攻击目标: 老杂斑野猪
StartCombatUpdateTimer: 为机器人 哀木替 启动战斗更新定时器
机器人开始自动攻击  <-- 应该看到攻击行为
```

## 注意事项

1. **性能考虑** - 避免重复调用Attack()方法
2. **状态检查** - 确保目标有效性检查
3. **错误处理** - 添加适当的异常处理
4. **日志记录** - 添加调试日志便于问题排查

这些修复确保了机器人在接收到攻击命令后能够正确执行物理攻击，解决了"机器人不会攻击"的问题。
