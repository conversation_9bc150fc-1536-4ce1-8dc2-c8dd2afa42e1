# 近战机器人攻击修复

## 问题分析

通过深入分析代码发现，近战机器人不攻击的根本原因是：

1. **Unit::Attack()方法正常工作** - 正确设置了`UNIT_STATE_MELEE_ATTACKING`状态
2. **战斗AI缺少关键调用** - 没有调用`DoMeleeAttackIfReady()`方法来执行实际攻击
3. **PlayerUpdates.cpp的自动攻击逻辑** - 需要`UNIT_STATE_MELEE_ATTACKING`状态才能触发

## 关键发现

### PlayerUpdates.cpp中的自动攻击逻辑
```cpp
if (HasUnitState(UNIT_STATE_MELEE_ATTACKING) && !HasUnitState(UNIT_STATE_CASTING) && !HasUnitState(UNIT_STATE_CHARGING))
{
    if (Unit* victim = GetVictim())
    {
        // 执行自动攻击逻辑
    }
}
```

### Unit::Attack()方法的关键部分
```cpp
bool Unit::Attack(Unit* victim, bool meleeAttack)
{
    // ... 各种检查 ...
    
    if (meleeAttack)
        AddUnitState(UNIT_STATE_MELEE_ATTACKING);  // 设置近战攻击状态
    
    if (meleeAttack)
        SendMeleeAttackStart(victim);              // 发送攻击开始消息
    
    return true;
}
```

### UnitAI::DoMeleeAttackIfReady()方法
```cpp
void UnitAI::DoMeleeAttackIfReady()
{
    if (me->HasUnitState(UNIT_STATE_CASTING))
        return;
    
    Unit* victim = me->GetVictim();
    if (!victim || !victim->IsInWorld())
        return;
    
    if (!me->IsWithinMeleeRange(victim))
        return;
    
    if (me->isAttackReady())
    {
        me->AttackerStateUpdate(victim);  // 执行实际攻击
        me->resetAttackTimer();
    }
}
```

## 修复方案

### 1. 在BotCombatStrategy::ExecuteStrategy()中添加DoMeleeAttackIfReady()调用

**位置**: `BotCombatStrategy.cpp:108-129`

**修复内容**:
```cpp
// 确保机器人正在攻击目标
if (target && target->IsAlive())
{
    // 检查是否需要开始自动攻击
    if (!m_bot->GetVictim() || m_bot->GetVictim() != target)
    {
        m_bot->Attack(target, true);
        LogDebug("开始攻击目标: " + target->GetName());
    }
    
    // 确保机器人面向目标
    if (!m_bot->HasInArc(M_PI, target))
    {
        m_bot->SetFacingToObject(target);
    }
    
    // 执行近战攻击（如果准备好了）
    if (m_bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING))
    {
        DoMeleeAttackIfReady();  // 新添加的关键调用
    }
}
```

### 2. 在BotCombatStrategy类中实现DoMeleeAttackIfReady()方法

**头文件声明** (`BotCombatStrategy.h:166`):
```cpp
// 执行近战攻击（如果准备好了）
void DoMeleeAttackIfReady();
```

**实现** (`BotCombatStrategy.cpp:480-529`):
```cpp
void BotCombatStrategy::DoMeleeAttackIfReady()
{
    if (!m_bot)
        return;
    
    // 检查是否正在施法
    if (m_bot->HasUnitState(UNIT_STATE_CASTING))
        return;
    
    Unit* victim = m_bot->GetVictim();
    if (!victim || !victim->IsInWorld())
        return;
    
    // 检查是否在近战范围内
    if (!m_bot->IsWithinMeleeRange(victim))
        return;
    
    // 确保攻击准备好了且没有在施法
    if (m_bot->isAttackReady())
    {
        // 防止主手和副手同时攻击，延迟副手攻击0.2秒
        if (m_bot->HasOffhandWeaponForAttack())
            if (m_bot->getAttackTimer(OFF_ATTACK) < 200)
                m_bot->setAttackTimer(OFF_ATTACK, 200);
        
        m_bot->AttackerStateUpdate(victim);
        m_bot->resetAttackTimer();
        LogDebug("执行主手攻击");
    }
    
    // 处理副手攻击
    if (m_bot->HasOffhandWeaponForAttack() && m_bot->isAttackReady(OFF_ATTACK))
    {
        // 延迟主手攻击如果两者会同时命中
        if (m_bot->getAttackTimer(BASE_ATTACK) < 200)
            m_bot->setAttackTimer(BASE_ATTACK, 200);
        
        m_bot->AttackerStateUpdate(victim, OFF_ATTACK);
        m_bot->resetAttackTimer(OFF_ATTACK);
        LogDebug("执行副手攻击");
    }
}
```

## 修复逻辑说明

### 攻击流程
1. **Attack()调用** - 设置`UNIT_STATE_MELEE_ATTACKING`状态
2. **DoMeleeAttackIfReady()调用** - 执行实际的攻击计算
3. **AttackerStateUpdate()** - 处理伤害计算和应用
4. **resetAttackTimer()** - 重置攻击计时器

### 关键检查
- **施法状态检查** - 施法时不能攻击
- **目标有效性检查** - 目标必须存在且在世界中
- **距离检查** - 必须在近战范围内
- **攻击准备检查** - 攻击计时器必须准备好

### 双持武器支持
- **主手攻击** - 使用`BASE_ATTACK`类型
- **副手攻击** - 使用`OFF_ATTACK`类型
- **攻击延迟** - 防止主手和副手同时攻击

## 预期效果

修复后，近战机器人应该能够：

1. **立即开始攻击** - 收到攻击命令后立即开始近战攻击
2. **持续攻击** - 根据武器速度持续攻击目标
3. **正确计算伤害** - 使用正确的伤害计算公式
4. **支持双持** - 正确处理主手和副手武器攻击
5. **遵循攻击时机** - 不在施法时攻击，遵循攻击间隔

## 测试验证

修复后应该看到：
```
开始攻击目标: 老杂斑野猪
执行主手攻击
执行主手攻击
执行主手攻击
...
```

## 技术细节

### AttackerStateUpdate()方法
这是执行实际攻击的核心方法，它会：
- 计算命中/闪避/格挡/暴击
- 计算伤害值
- 应用伤害到目标
- 触发相关的战斗事件

### 攻击计时器机制
- 每种武器类型都有独立的攻击计时器
- 计时器基于武器的攻击速度
- 只有当计时器准备好时才能攻击

### 状态管理
- `UNIT_STATE_MELEE_ATTACKING` - 表示正在进行近战攻击
- `UNIT_STATE_CASTING` - 表示正在施法（阻止攻击）
- `UNIT_STATE_CHARGING` - 表示正在冲锋（阻止攻击）

这个修复确保了近战机器人能够正确执行物理攻击，解决了"机器人不会攻击"的核心问题。
