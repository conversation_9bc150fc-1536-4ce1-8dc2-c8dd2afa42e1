#pragma once

#include "BotGroupAI.h"
#include "../BotClassSpells.h"

// 组队盗贼AI - 完全参考mod-pbot-keji的GroupRogueAI设计
class GroupRogueAI : public BotGroupAI, public BotRogueSpells
{
public:
    GroupRogueAI(Player* player);
    virtual ~GroupRogueAI() = default;
    
    // 重写虚函数
    void UpdateBotAI(uint32 diff) override;
    void ResetBotAI() override;
    void ProcessMeleeSpell(Unit* pTarget) override;
    void ProcessRangeSpell(Unit* pTarget) override;
    bool ProcessNormalSpell() override;
    void UpEnergy() override;
    bool NeedFlee() override;
    bool IsMeleeBotAI() override { return true; }
    void ProcessBotCommand(Player* srcPlayer, const std::string& cmd);
    void ProcessAttackCommand();

    // 组队特有方法
    void ProcessSeduceSpell(Unit* pTarget);
    uint32 GetSeducePriority();

    // 盗贼特有方法
    void UpdateTalentType();

    // 声明盗贼专用方法（这些方法在GroupRogueAI.cpp中实现，但使用BotRogueSpells的逻辑）
    uint32 GetEnergyPowerPer();
    uint32 GetPoisonEntryByWeaponType(EquipmentSlots equipSlot);
    bool ProcessUpPoison();
    bool ProcessSneakSpell(Unit* pTarget);
    bool ProcessMeleeBlind(Unit* pTarget);
    bool CanConsumeCombo(Unit* pTarget);
    bool CastCloakByNeed();
    void OnCastSneak();
    void OnCastFlash(Unit* pTarget);
    bool CanBlind(Unit* pTarget);
    bool CanStartSpell();

    // 基类的辅助方法
    bool CanReciveCommand(const std::string& cmd, std::string& param);
    void ProcessSummonCommand();
    void ProcessFleeCommand();
    void ProcessStopCommand();
    void ProcessFlee();
    bool TryBlockCastingByTarget(Unit* pTarget);
    uint32 GetLifePCT(Unit* pTarget);
    std::vector<Unit*> RangeEnemyListByTargetIsMe(float range);
    std::vector<Unit*> RangeEnemyListByNonAura(uint32 aura, float range);

    // 实现BotRogueSpells的纯虚方法
    Player* GetPlayer() const override { return m_player; }
    SpellCastResult TryCastSpell(uint32 spellID, Unit* pTarget = nullptr, bool force = false) override;

private:
    // 基础成员变量
    uint32 m_BotTalentType = 0;
    uint32 m_IsUpedPoison = 0;

    // 场景AI层移除毒药定时器，由Faker系统统一处理
    // uint32 m_poisonCheckTimer = 0;
    // bool m_poisonTimerActive = false;

    // 天赋专精轮换方法
    void ProcessAssassinationRotation(Unit* pTarget);  // 刺杀天赋轮换
    void ProcessCombatRotation(Unit* pTarget);         // 战斗天赋轮换
    void ProcessSubtletyRotation(Unit* pTarget);       // 敏锐天赋轮换
    void ProcessGenericRotation(Unit* pTarget);        // 通用轮换

    // 潜行系统方法 - 使用基类的方法
    // bool ShouldStealth() 和 bool CanStealth() 在基类中实现

    // 毒药系统方法（场景AI不再负责非战斗上毒）
    bool IsPoisonBuffExpiringSoon();

    // 辅助方法
    uint32 GetAuraRemainingTime(Unit* unit, uint32 spellId);
};
