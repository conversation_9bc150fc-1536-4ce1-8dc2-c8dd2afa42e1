# YGbot 雕文系统使用指南

## 概述

YGbot雕文系统是从完美运行的tianfu模块移植而来的雕文功能，提供了完整的雕文管理和自动应用功能。

## 功能特性

### ✨ 核心功能
- **雕文预设管理**: 为每个职业的每个专精配置雕文预设
- **自动雕文应用**: 一键应用整套雕文配置
- **智能槽位匹配**: 自动选择合适的雕文槽位
- **等级限制支持**: 根据玩家等级解锁雕文槽位
- **背包管理**: 自动添加缺失的雕文物品

### 🎯 雕文槽位规则
- **15级**: 解锁槽位0(主要)和槽位1(次要)
- **30级**: 解锁槽位3(主要)
- **50级**: 解锁槽位2(次要)
- **70级**: 解锁槽位4(次要)
- **80级**: 解锁槽位5(主要)

## 配置方法

### 1. 配置文件设置

雕文预设配置已经集成到主配置文件 `mod_ygbot.conf.dist` 中：

```ini
# 格式: YGbot.PremadeSpecGlyph.{职业ID}.{专精索引} = 雕文ID1,雕文ID2,雕文ID3

# 战士武器专精示例
YGbot.PremadeSpecGlyph.1.0 = 43418,43395,43423,43399,49084,43421

# 圣骑士神圣专精示例
YGbot.PremadeSpecGlyph.2.0 = 41106,43367,45741,43369,43365,41109
```

**注意**: 配置文件中已经包含了所有职业的完整雕文预设，无需额外配置。

### 2. 职业ID对照表

| 职业ID | 职业名称 | 英文名称 |
|--------|----------|----------|
| 1 | 战士 | Warrior |
| 2 | 圣骑士 | Paladin |
| 3 | 猎人 | Hunter |
| 4 | 盗贼 | Rogue |
| 5 | 牧师 | Priest |
| 6 | 死亡骑士 | Death Knight |
| 7 | 萨满 | Shaman |
| 8 | 法师 | Mage |
| 9 | 术士 | Warlock |
| 11 | 德鲁伊 | Druid |

## 使用命令

### 🔧 GM命令

#### 应用雕文预设
```
.glyph preset <专精索引>
```
- 为选中的玩家应用指定专精的雕文预设
- 示例: `.glyph preset 0` (应用第一个专精的雕文)

#### 清除所有雕文
```
.glyph clear
```
- 清除选中玩家的所有雕文

#### 查看雕文列表
```
.glyph list
```
- 显示选中玩家当前的所有雕文

#### 应用单个雕文
```
.glyph use <雕文物品ID>
```
- 为选中玩家应用指定的雕文
- 示例: `.glyph use 43418`

#### 调试信息
```
.glyph debug
```
- 显示所有已加载的雕文预设
- 用于验证配置文件是否正确加载

### 📋 命令示例

```bash
# 选中一个玩家，然后执行以下命令

# 查看系统调试信息
.glyph debug

# 查看当前雕文
.glyph list

# 清除所有雕文
.glyph clear

# 应用战士武器专精雕文(专精0)
.glyph preset 0

# 应用单个雕文
.glyph use 43418
```

## 集成到天赋系统

雕文功能已经集成到现有的天赋管理系统中：

### 在天赋管理器中使用

```cpp
// 通过专精名称应用雕文
bool success = sYGbotTalentManager->ApplyGlyphsBySpecName(player, "武器战");

// 通过专精索引应用雕文
bool success = sYGbotTalentManager->ApplyGlyphsBySpecIndex(player, 1, 0);
```

## 技术实现

### 🏗️ 架构组件

1. **YGbotGlyphManager**: 核心雕文管理器
   - 单例模式，全局访问
   - 配置加载和缓存
   - 雕文应用逻辑

2. **YGbotGlyphCommands**: GM命令处理器
   - 提供完整的命令接口
   - 参数验证和错误处理

3. **集成到YGbotTalentManager**: 
   - 与现有天赋系统无缝集成
   - 统一的API接口

### 🔄 工作流程

1. **配置加载**: 服务器启动时从 `mod_ygbot.conf.dist` 加载雕文预设
2. **命令处理**: GM使用命令触发雕文应用
3. **槽位匹配**: 系统自动匹配合适的雕文槽位
4. **物品管理**: 自动添加缺失的雕文物品到背包
5. **效果应用**: 应用雕文效果并更新客户端

## 故障排除

### 常见问题

**Q: 雕文应用失败？**
A: 检查以下几点：
- 雕文ID是否正确
- 玩家等级是否足够解锁槽位
- 背包是否有足够空间

**Q: 找不到雕文预设？**
A: 确认配置文件中是否正确配置了对应职业和专精的雕文预设

**Q: 雕文槽位不匹配？**
A: 系统会自动匹配主要/次要雕文槽位，确保雕文ID对应正确的雕文类型

### 调试信息

启用调试日志来查看详细信息：
```ini
Logger.server.level = 6
```

## 更新日志

### v1.0.0 (当前版本)
- ✅ 完整移植tianfu模块的雕文功能
- ✅ 集成到YGbot天赋系统
- ✅ 提供完整的GM命令接口
- ✅ 支持等级限制和槽位匹配
- ✅ 自动背包管理功能

## 贡献

如需添加新的雕文预设或改进功能，请：
1. 修改配置文件添加新的雕文预设
2. 测试确保雕文ID正确有效
3. 更新文档说明

---

*此雕文系统基于tianfu模块的完美运行版本移植，确保了功能的稳定性和可靠性。*
