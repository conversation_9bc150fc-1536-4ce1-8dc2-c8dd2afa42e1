#include "BotControlCommands.h"
#include "FakePlayers.h"
#include "BotAutoLearnSpells.h"
#include "../CombatSystem/PlayerBotAI.h"

// 禁用旧的战斗系统代码
#define DISABLE_OLD_COMBAT_SYSTEM
#include "../CombatSystem/BotCombatMovement.h"
#include "../MovementSystem/BotMovementManager.h"
#include "../Faker/Faker.h"
#include "../Faker/BotBehaviorEngine.h"
#include "../PlayerPatch.h"
#include "../CombatSystem/BotFieldAI/FieldRogueAI.h"
#include "../CombatSystem/BotGroupAI/GroupRogueAI.h"
#include "../CombatSystem/BotGroupAI/BotGroupAI.h"


// 外部函数声明
extern void AddFaker(int guid);
#include "Log.h"
#include "ObjectAccessor.h"
#include "MapMgr.h"
#include <chrono>
#include <thread>
#include "InstanceScript.h"
#include "Channel.h"
#include "ArenaTeamMgr.h"
#include "ArenaTeam.h"
#include "Config.h"
#include "GridNotifiers.h"
#include "GridNotifiersImpl.h"
#include "CellImpl.h"
#include "Creature.h"
#include <algorithm>
#include <cctype>
#include "InstanceSaveMgr.h"
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

void BotControlCommands::Initialize()
{
    LOG_INFO("server.loading", "初始化机器人控制命令系统...");
    LOG_INFO("server.loading", "聊天控制命令仅支持队伍聊天和私聊频道");
    InitializeChatCommands();
    InitializeWhisperCommands();

    // 启动定时检查器来测试系统
    LOG_INFO("server.loading", "启动聊天命令监听器...");

    // 清理战斗信息
    activeCombats.clear();
}

void BotControlCommands::InitializeChatCommands()
{
    LOG_INFO("server.loading", "初始化聊天控制命令...");

    // 基础控制命令
    _chatCommands["攻击"] = [this](Player* sender) { HandleAttackCommand(sender); };
    _chatCommands["坦克攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "tank"); };
    _chatCommands["近战攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "melee"); };
    _chatCommands["远程攻击"] = [this](Player* sender) { HandleAttackCommand(sender, "ranged"); };
    _chatCommands["停火"] = [this](Player* sender) { HandleStopAttackCommand(sender); };
    _chatCommands["近战停火"] = [this](Player* sender) { HandleStopAttackCommand(sender, "melee"); };
    _chatCommands["远程停火"] = [this](Player* sender) { HandleStopAttackCommand(sender, "ranged"); };

    // 位置与阵型命令
    _chatCommands["集合"] = [this](Player* sender) { HandleGatherCommand(sender); };
    _chatCommands["召唤全员"] = [this](Player* sender) { HandleSummonAllCommand(sender); };
    _chatCommands["散开"] = [this](Player* sender) { HandleSpreadCommand(sender); };
    _chatCommands["驱散"] = [this](Player* sender) { HandleDispelCommand(sender); };
    _chatCommands["三角阵型"] = [this](Player* sender) { HandleTriangleFormationCommand(sender); };
    _chatCommands["圆形阵型"] = [this](Player* sender) { HandleCircleFormationCommand(sender); };
    _chatCommands["远离人群"] = [this](Player* sender) { HandleAvoidCrowdCommand(sender); };

    // 机器人管理命令
    _chatCommands["onlinefriends"] = [this](Player* sender) { HandleOnlineFriendsCommand(sender); };



    LOG_INFO("server.loading", "已注册 {} 个聊天控制命令", _chatCommands.size());
    for (const auto& cmd : _chatCommands)
    {
        LOG_DEBUG("server.loading", "注册聊天命令: '{}'", cmd.first);
    }
}

void BotControlCommands::InitializeWhisperCommands()
{
    // 私聊命令
    _whisperCommands["退队伍"] = [this](Player* bot, Player* sender) { HandleLeaveGroupCommand(bot); };

    // 竞技场战队命令 - 使用正确的索引（参考mod-jbbot的实现）
    _whisperCommands["退22战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 0); }; // 2v2 = index 0
    _whisperCommands["退33战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 1); }; // 3v3 = index 1
    _whisperCommands["退55战队"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamByIndex(bot, 2); }; // 5v5 = index 2

    // 保留原有的按类型退出命令
    _whisperCommands["退2v2"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 2); };
    _whisperCommands["退3v3"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 3); };
    _whisperCommands["退5v5"] = [this](Player* bot, Player* sender) { HandleLeaveArenaTeamCommand(bot, 5); };

    _whisperCommands["退公会"] = [this](Player* bot, Player* sender) { HandleLeaveGuildCommand(bot); };

    // 攻击命令 - 集成CombatAI系统
    _whisperCommands["攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "all"); };
    _whisperCommands["坦克攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "tank"); };
    _whisperCommands["近战攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "melee"); };
    _whisperCommands["远程攻击"] = [this](Player* bot, Player* sender) { HandleAttackCommand(sender, "ranged"); };
    _whisperCommands["停止攻击"] = [this](Player* bot, Player* sender) { HandleStopAttackCommand(sender, "all"); };
    _whisperCommands["停止"] = [this](Player* bot, Player* sender) { HandleStopAttackCommand(sender, "all"); };

    // 位置控制命令
    _whisperCommands["集合"] = [this](Player* bot, Player* sender) { HandleGatherCommand(sender); };
    _whisperCommands["召唤全员"] = [this](Player* bot, Player* sender) { HandleSummonAllCommand(sender); };
    _whisperCommands["召唤"] = [this](Player* bot, Player* sender) { HandleSummonAllCommand(sender); };
    _whisperCommands["远离人群"] = [this](Player* bot, Player* sender) { HandleAvoidCrowdWhisperCommand(bot, sender); };
}

// 机器人管理命令实现
bool BotControlCommands::HandleOnlineGuildMemberCommand(Player* player)
{
    try
    {
        if (!player)
        {
            LOG_ERROR("server", "HandleOnlineGuildMemberCommand: player is null");
            return false;
        }

        Guild* guild = player->GetGuild();
        if (!guild)
        {
            ChatHandler(player->GetSession()).SendSysMessage("你必须在公会中才能使用此命令。");
            return false;
        }

        uint32 guildId = guild->GetId();
        std::string guildName = guild->GetName();

        LOG_INFO("server", "玩家 {} 请求上线公会 {} (ID: {}) 的成员", player->GetName(), guildName, guildId);

        // 正确的方法 - 查找真正的公会成员
        uint32 onlineCount = 0;
        uint32 maxCount = 5; // 限制最多上线5个

        LOG_INFO("server", "开始查找公会 {} (ID: {}) 的离线成员", guildName, guildId);

        // 直接从数据库查询公会成员，使用guild_member表
        QueryResult guildMemberResult = CharacterDatabase.Query(
            "SELECT gm.guid, c.name FROM guild_member gm "
            "JOIN characters c ON gm.guid = c.guid "
            "WHERE gm.guildid = {} AND c.online = 0 LIMIT {}",
            guildId, maxCount);

        if (!guildMemberResult)
        {
            // 如果guild_member表不存在，尝试其他方法
            LOG_INFO("server", "guild_member表查询失败，尝试alternative方法");

            // 遍历FakerMap，检查每个角色是否属于该公会
            for (auto it = FakerMap.begin(); it != FakerMap.end() && onlineCount < maxCount; ++it)
            {
                uint32 characterGuid = it->first;

                // 检查这个角色是否在线
                bool isOnline = false;
                for (auto session : FakerSessions)
                {
                    if (session && session->GetPlayer() &&
                        session->GetPlayer()->GetGUID().GetCounter() == characterGuid)
                    {
                        isOnline = true;
                        break;
                    }
                }

                if (!isOnline)
                {
                    // 检查角色是否属于该公会（通过已上线的公会成员推断）
                    QueryResult nameResult = CharacterDatabase.Query("SELECT name FROM characters WHERE guid = {}", characterGuid);
                    if (nameResult)
                    {
                        Field* fields = nameResult->Fetch();
                        std::string name = fields[0].Get<std::string>();

                        LOG_INFO("server", "尝试上线机器人: {} (GUID: {})", name, characterGuid);

                        try
                        {
                            AddFaker(characterGuid);
                            onlineCount++;
                            LOG_INFO("server", "成功上线机器人: {}", name);
                        }
                        catch (...)
                        {
                            LOG_ERROR("server", "上线机器人 {} 失败", name);
                        }
                    }
                }
            }
        }
        else
        {
            // 使用guild_member表的结果
            do
            {
                Field* fields = guildMemberResult->Fetch();
                uint32 memberGuid = fields[0].Get<uint32>();
                std::string memberName = fields[1].Get<std::string>();

                LOG_INFO("server", "找到公会离线成员: {} (GUID: {})", memberName, memberGuid);

                try
                {
                    AddFaker(memberGuid);
                    onlineCount++;
                    LOG_INFO("server", "成功上线公会成员: {}", memberName);
                }
                catch (...)
                {
                    LOG_ERROR("server", "上线公会成员 {} 失败", memberName);
                }

            } while (guildMemberResult->NextRow());
        }

        if (onlineCount == 0)
        {
            ChatHandler(player->GetSession()).SendSysMessage("没有找到可上线的公会机器人成员。");
        }
        else
        {
            ChatHandler(player->GetSession()).PSendSysMessage("已上线 {} 个机器人成员。", onlineCount);
        }

        return true;


    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "HandleOnlineGuildMemberCommand 异常: {}", e.what());
        ChatHandler(player->GetSession()).SendSysMessage("上线公会成员时发生错误。");
        return false;
    }
    catch (...)
    {
        LOG_ERROR("server", "HandleOnlineGuildMemberCommand 未知异常");
        ChatHandler(player->GetSession()).SendSysMessage("上线公会成员时发生未知错误。");
        return false;
    }
}

bool BotControlCommands::HandleOfflineAllBotCommand(Player* player)
{
    LOG_INFO("server", "玩家 {} 请求下线所有没在组队中的机器人", player->GetName());

    uint32 offlineCount = 0;
    std::vector<std::pair<uint32, std::string>> botsToOffline; // 收集需要下线的机器人GUID和名称

    //  修复：使用更安全的方式收集机器人信息
    try
    {
        // 第一步：安全地收集需要下线的机器人信息
        std::vector<WorldSession*> sessionsCopy = FakerSessions; // 创建副本避免迭代器失效

        for (auto session : sessionsCopy)
        {
            if (!session)
            {
                LOG_DEBUG("server", "跳过空的session");
                continue;
            }

            Player* bot = session->GetPlayer();
            if (!bot)
            {
                LOG_DEBUG("server", "跳过没有player的session");
                continue;
            }

            // 检查机器人是否在队伍中
            if (!bot->GetGroup())
            {
                uint32 botGuid = bot->GetGUID().GetCounter();
                std::string botName = bot->GetName();

                LOG_INFO("server", "标记下线没在组队中的机器人: {} (GUID: {})", botName, botGuid);
                botsToOffline.push_back(std::make_pair(botGuid, botName));
            }
            else
            {
                LOG_INFO("server", "跳过在队伍中的机器人: {}", bot->GetName());
            }
        }

        LOG_INFO("server", "总共标记了 {} 个机器人需要下线", botsToOffline.size());

        // 第二步：安全地下线机器人
        for (const auto& botInfo : botsToOffline)
        {
            uint32 botGuid = botInfo.first;
            const std::string& botName = botInfo.second;

            try
            {
                // 再次验证机器人是否仍然存在且没有队伍
                bool shouldOffline = false;

                // 重新检查当前的FakerSessions
                for (auto session : FakerSessions)
                {
                    if (!session || !session->GetPlayer())
                        continue;

                    Player* bot = session->GetPlayer();
                    if (bot->GetGUID().GetCounter() == botGuid)
                    {
                        // 再次确认机器人没有队伍
                        if (!bot->GetGroup())
                        {
                            shouldOffline = true;
                            LOG_INFO("server", "确认机器人 {} 仍需下线", botName);
                        }
                        else
                        {
                            LOG_INFO("server", "机器人 {} 现在有队伍了，跳过下线", botName);
                        }
                        break;
                    }
                }

                if (shouldOffline)
                {
                    LOG_INFO("server", "正在下线机器人: {} (GUID: {})", botName, botGuid);

                    //  使用更安全的下线方法
                    if (sFaker)
                    {
                        sFaker->Remove(botGuid);
                        offlineCount++;
                        LOG_INFO("server", "成功下线机器人: {}", botName);

                        // 添加小延迟避免过快操作
                        std::this_thread::sleep_for(std::chrono::milliseconds(10));
                    }
                    else
                    {
                        LOG_ERROR("server", "sFaker 为空，无法下线机器人 {}", botName);
                    }
                }
                else
                {
                    LOG_DEBUG("server", "机器人 {} 已不需要下线", botName);
                }
            }
            catch (const std::exception& e)
            {
                LOG_ERROR("server", "下线机器人 {} 时发生异常: {}", botName, e.what());
            }
            catch (...)
            {
                LOG_ERROR("server", "下线机器人 {} 时发生未知异常", botName);
            }
        }
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "HandleOfflineAllBotCommand 发生异常: {}", e.what());
        ChatHandler(player->GetSession()).SendSysMessage("下线机器人时发生错误，请查看服务器日志。");
        return false;
    }
    catch (...)
    {
        LOG_ERROR("server", "HandleOfflineAllBotCommand 发生未知异常");
        ChatHandler(player->GetSession()).SendSysMessage("下线机器人时发生未知错误。");
        return false;
    }

    // 返回结果
    if (offlineCount == 0)
    {
        ChatHandler(player->GetSession()).SendSysMessage("没有找到可下线的机器人。");
    }
    else
    {
        ChatHandler(player->GetSession()).PSendSysMessage("已成功下线 {} 个机器人。", offlineCount);
        LOG_INFO("server", "玩家 {} 成功下线了 {} 个机器人", player->GetName(), offlineCount);
    }

    return true;
}

bool BotControlCommands::HandleOnlineFriendsCommand(Player* player)
{
    try
    {
        if (!player)
        {
            LOG_ERROR("server", "HandleOnlineFriendsCommand: player is null");
            return false;
        }

        LOG_INFO("server", "玩家 {} 执行onlinefriends命令", player->GetName());

        uint32 playerGuid = player->GetGUID().GetCounter();
        uint32 onlineCount = 0;
        uint32 maxCount = 5; // 限制最多上线5个好友

        LOG_INFO("server", "开始查找玩家 {} (GUID: {}) 的离线好友", player->GetName(), playerGuid);

        // 从数据库查询好友列表中的离线机器人
        // 使用character_social表查询好友关系
        QueryResult friendResult = CharacterDatabase.Query(
            "SELECT cs.friend, c.name FROM character_social cs "
            "JOIN characters c ON cs.friend = c.guid "
            "WHERE cs.guid = {} AND cs.flags & 1 AND c.online = 0 LIMIT {}",
            playerGuid, maxCount);

        if (!friendResult)
        {
            LOG_INFO("server", "character_social表查询失败或没有离线好友，尝试alternative方法");

            // 备用方法：从FakerMap中查找可能的好友机器人
            for (auto it = FakerMap.begin(); it != FakerMap.end() && onlineCount < maxCount; ++it)
            {
                uint32 characterGuid = it->first;

                // 检查这个角色是否在线
                bool isOnline = false;
                for (auto session : FakerSessions)
                {
                    if (session && session->GetPlayer() &&
                        session->GetPlayer()->GetGUID().GetCounter() == characterGuid)
                    {
                        isOnline = true;
                        break;
                    }
                }

                if (!isOnline)
                {
                    // 检查是否是机器人
                    QueryResult nameResult = CharacterDatabase.Query("SELECT name FROM characters WHERE guid = {}", characterGuid);
                    if (nameResult)
                    {
                        Field* fields = nameResult->Fetch();
                        std::string name = fields[0].Get<std::string>();

                        LOG_INFO("server", "尝试上线机器人好友: {} (GUID: {})", name, characterGuid);

                        try
                        {
                            AddFaker(characterGuid);
                            onlineCount++;
                            LOG_INFO("server", "成功上线机器人好友: {}", name);
                        }
                        catch (...)
                        {
                            LOG_ERROR("server", "上线机器人好友 {} 失败", name);
                        }
                    }
                }
            }
        }
        else
        {
            // 使用character_social表的结果
            do
            {
                Field* fields = friendResult->Fetch();
                uint32 friendGuid = fields[0].Get<uint32>();
                std::string friendName = fields[1].Get<std::string>();

                LOG_INFO("server", "找到离线好友: {} (GUID: {})", friendName, friendGuid);

                try
                {
                    AddFaker(friendGuid);
                    onlineCount++;
                    LOG_INFO("server", "成功上线好友: {}", friendName);
                }
                catch (...)
                {
                    LOG_ERROR("server", "上线好友 {} 失败", friendName);
                }

            } while (friendResult->NextRow());
        }

        if (onlineCount == 0)
        {
            ChatHandler(player->GetSession()).SendSysMessage("没有找到可上线的好友机器人。");
        }
        else
        {
            ChatHandler(player->GetSession()).PSendSysMessage("已上线 {} 个好友机器人。", onlineCount);
        }

        return true;
    }
    catch (const std::exception& e)
    {
        LOG_ERROR("server", "HandleOnlineFriendsCommand 异常: {}", e.what());
        ChatHandler(player->GetSession()).SendSysMessage("上线好友时发生错误。");
        return false;
    }
    catch (...)
    {
        LOG_ERROR("server", "HandleOnlineFriendsCommand 未知异常");
        ChatHandler(player->GetSession()).SendSysMessage("上线好友时发生未知错误。");
        return false;
    }
}

bool BotControlCommands::HandleGroupFriendCommand(Player* player)
{
    if (!player)
    {
        LOG_ERROR("server", "HandleGroupFriendCommand: player 为空");
        return false;
    }

    std::vector<Player*> groupBots = GetGroupBots(player);

    if (groupBots.empty())
    {
        ChatHandler(player->GetSession()).SendSysMessage("队伍中没有机器人可以添加为好友。");
        return true;
    }

    uint32 addedCount = 0;
    uint32 skippedCount = 0;

    for (Player* bot : groupBots)
    {
        //  额外安全检查：确保不会添加玩家自己
        if (!bot || bot == player)
        {
            LOG_WARN("server", "HandleGroupFriendCommand: 跳过无效或自己的玩家");
            skippedCount++;
            continue;
        }

        //  确保是机器人
        if (!PlayerPatch::GetIsFaker(bot))
        {
            LOG_WARN("server", "HandleGroupFriendCommand: 跳过非机器人玩家 {}", bot->GetName());
            skippedCount++;
            continue;
        }

        //  检查是否已经是好友
        if (player->GetSocial()->HasFriend(bot->GetGUID()))
        {
            LOG_DEBUG("server", "HandleGroupFriendCommand: {} 已经是好友，跳过", bot->GetName());
            skippedCount++;
            continue;
        }

        // 添加为好友
        if (player->GetSocial()->AddToSocialList(bot->GetGUID(), SOCIAL_FLAG_FRIEND))
        {
            addedCount++;
            LOG_INFO("server", "HandleGroupFriendCommand: 成功将机器人 {} 添加为好友", bot->GetName());
        }
        else
        {
            LOG_WARN("server", "HandleGroupFriendCommand: 添加机器人 {} 为好友失败", bot->GetName());
            skippedCount++;
        }
    }

    if (addedCount > 0)
    {
        ChatHandler(player->GetSession()).PSendSysMessage("已将 {} 个队伍机器人添加为好友。", addedCount);
    }

    if (skippedCount > 0)
    {
        ChatHandler(player->GetSession()).PSendSysMessage("跳过了 {} 个无效或已存在的条目。", skippedCount);
    }

    return true;
}

bool BotControlCommands::HandleInviteFriendCommand(Player* player)
{
    if (!player->GetGroup())
    {
        ChatHandler(player->GetSession()).SendSysMessage("你必须在队伍中才能使用此命令。");
        return false;
    }

    std::vector<Player*> friendBots = GetFriendBots(player);
    Group* group = player->GetGroup();

    uint32 invitedCount = 0;
    for (Player* bot : friendBots)
    {
        if (PlayerPatch::GetIsFaker(bot) && !bot->GetGroup() && group->GetMembersCount() < 5)
        {
            group->AddMember(bot);
            invitedCount++;
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已邀请 {} 个好友机器人入队。", invitedCount);
    return true;
}

bool BotControlCommands::HandleAddClassBotCommand(Player* player, uint8 classId)
{
    if (classId < 1 || classId > 11 || classId == 10) // 10是无效职业
    {
        ChatHandler(player->GetSession()).SendSysMessage("无效的职业ID。有效范围：1-战士，2-圣骑，3-猎人，4-盗贼，5-牧师，6-死骑，7-萨满，8-法师，9-术士，11-德鲁伊");
        return false;
    }

    LOG_INFO("server", "玩家 {} 请求上线职业ID {} 的机器人", player->GetName(), classId);

    // 获取玩家阵营
    uint32 playerTeam = player->GetTeamId(); // 0=联盟, 1=部落
    std::string teamName = (playerTeam == 0) ? "联盟" : "部落";

    LOG_INFO("server", "玩家阵营: {} ({})", teamName, playerTeam);

    // 从数据库查找指定职业且同阵营的离线角色
    // 联盟种族: 1,3,4,7,11 (人类,矮人,暗夜精灵,侏儒,德莱尼)
    // 部落种族: 2,5,6,8,10 (兽人,亡灵,牛头人,巨魔,血精灵)
    std::string raceCondition;
    if (playerTeam == 0) // 联盟
    {
        raceCondition = "AND race IN (1,3,4,7,11)";
    }
    else // 部落
    {
        raceCondition = "AND race IN (2,5,6,8,10)";
    }

    std::string query = "SELECT guid, name, race FROM characters WHERE class = " + std::to_string(classId) +
                       " AND online = 0 " + raceCondition + " LIMIT 1";

    QueryResult result = CharacterDatabase.Query(query);

    if (!result)
    {
        ChatHandler(player->GetSession()).PSendSysMessage("没有找到职业ID为 {} 的 {} 阵营离线机器人。", classId, teamName);
        return false;
    }

    uint32 onlineCount = 0;
    std::vector<std::string> botNames;

    do
    {
        Field* fields = result->Fetch();
        uint32 guid = fields[0].Get<uint32>();
        std::string name = fields[1].Get<std::string>();
        uint8 race = fields[2].Get<uint8>();

        std::string raceName = GetRaceNameById(race);
        LOG_INFO("server", "尝试上线 {} 阵营职业ID {} 的机器人: {} ({}) (GUID: {})",
                 teamName, classId, name, raceName, guid);

        // 使用Faker系统上线机器人
        AddFaker(guid);
        botNames.push_back(name);
        onlineCount++;

    } while (result->NextRow());

    // 获取职业名称
    std::string className = GetClassNameById(classId);

    // 根据阵营设置颜色
    std::string colorCode = (playerTeam == 0) ? "|cff0080ff" : "|cffff0000"; // 联盟蓝色，部落红色
    std::string colorEnd = "|r";

    for (const std::string& botName : botNames)
    {
        // 创建带阵营颜色的可点击玩家名字链接
        ChatHandler(player->GetSession()).PSendSysMessage("{}|Hplayer:{}|h[{}]|h{} - 点击邀请入队",
            colorCode, botName.c_str(), botName.c_str(), colorEnd);
    }

    return true;
}

bool BotControlCommands::HandleResetDungeonCommand(Player* player)
{
    Group* group = player->GetGroup();
    if (!group)
    {
        ChatHandler(player->GetSession()).SendSysMessage("你必须在队伍中才能使用此命令。");
        return false;
    }

    LOG_INFO("server", "玩家 {} 请求重置队伍副本CD", player->GetName());

    uint32 resetCount = 0;
    for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
    {
        Player* member = itr->GetSource();
        if (member)
        {
            LOG_INFO("server", "重置队伍成员 {} 的副本CD", member->GetName());

            uint32 memberGuid = member->GetGUID().GetCounter();

            // 重置副本绑定 - 与instance unbind命令一致
            // 1. 清理角色副本绑定
            CharacterDatabase.Execute("DELETE FROM character_instance WHERE guid = {}", memberGuid);

            // 2. 重置每日副本次数
            CharacterDatabase.Execute("DELETE FROM character_instance_times WHERE guid = {}", memberGuid);

            // 3. 清理角色副本保存数据
            CharacterDatabase.Execute("UPDATE characters SET instance_id = 0, instance_mode_mask = 0 WHERE guid = {}", memberGuid);

            // 发送系统消息提示
            ChatHandler(member->GetSession()).SendSysMessage("你的副本CD已被重置。");

            resetCount++;
        }
    }

    ChatHandler(player->GetSession()).PSendSysMessage("已重置队伍中 {} 个成员的副本CD。", resetCount);
    return true;
}

// 聊天命令处理
bool BotControlCommands::HandleChatCommand(Player* sender, const std::string& message, uint32 chatType)
{
    LOG_INFO("server", "收到聊天命令: [{}] 来自玩家: {} 聊天类型: {}", message, sender->GetName(), chatType);

    auto it = _chatCommands.find(message);
    if (it != _chatCommands.end())
    {
        LOG_INFO("server", "执行聊天命令: [{}]", message);
        it->second(sender);
        ChatHandler(sender->GetSession()).SendSysMessage("执行命令: " + message);
        return true;
    }
    else
    {
        LOG_DEBUG("server", "未找到聊天命令: [{}]", message);
    }
    return false;
}

// GM命令处理（支持说话频道）
bool BotControlCommands::HandleGMCommand(Player* sender, const std::string& message)
{
    LOG_INFO("server", "收到说话频道GM命令: [{}] 来自玩家: {}", message, sender->GetName());

    // 检查是否是GM命令
    if (message == "onlineguildmember")
    {
        return HandleOnlineGuildMemberCommand(sender);
    }
    else if (message == "offlineallbot")
    {
        return HandleOfflineAllBotCommand(sender);
    }
    else if (message == "onlinefriends")
    {
        return HandleOnlineFriendsCommand(sender);
    }
    else if (message == "groupfriend")
    {
        return HandleGroupFriendCommand(sender);
    }
    else if (message == "invitefriend")
    {
        return HandleInviteFriendCommand(sender);
    }
    else if (message == "resetdungeon")
    {
        return HandleResetDungeonCommand(sender);
    }
    else if (message.find("addclassbot ") == 0)
    {
        // 解析职业ID
        std::string classIdStr = message.substr(12); // "addclassbot " 长度为12
        try
        {
            uint8 classId = std::stoi(classIdStr);
            return HandleAddClassBotCommand(sender, classId);
        }
        catch (...)
        {
            ChatHandler(sender->GetSession()).SendSysMessage("无效的职业ID，请使用1-11之间的数字");
            return false;
        }
    }

    return false;
}

// 私聊命令处理
bool BotControlCommands::HandleWhisperCommand(Player* bot, Player* sender, const std::string& message)
{
    if (!PlayerPatch::GetIsFaker(bot))
        return false;

    // 防止重复执行相同命令
    static std::unordered_map<std::string, uint32> lastCommandTime;
    std::string commandKey = std::to_string(bot->GetGUID().GetCounter()) + "_" +
                            std::to_string(sender->GetGUID().GetCounter()) + "_" + message;
    uint32 currentTime = getMSTime();

    if (lastCommandTime.count(commandKey) &&
        (currentTime - lastCommandTime[commandKey]) < 2000) // 2秒内不重复执行
    {
        LOG_DEBUG("server", "私聊命令重复执行，忽略: [{}] 对机器人: {}", message, bot->GetName());
        return true; // 返回true表示已处理，避免进一步处理
    }

    lastCommandTime[commandKey] = currentTime;

    LOG_INFO("server", "处理机器人 {} 的私聊命令: [{}] 来自玩家: {}",
        bot->GetName(), message, sender->GetName());

    auto it = _whisperCommands.find(message);
    if (it != _whisperCommands.end())
    {
        LOG_INFO("server", "执行私聊命令: [{}] 对机器人: {}", message, bot->GetName());
        it->second(bot, sender);
        return true;
    }
    else
    {
        LOG_INFO("server", "未找到私聊命令: [{}] 对机器人: {}", message, bot->GetName());
        return false;
    }
}

// 战斗控制命令实现
void BotControlCommands::HandleAttackCommand(Player* sender, const std::string& targetType)
{
    // 攻击命令触发时，解除PACIFIED（让被冻结机器人恢复行动）
    {
        std::vector<Player*> allBots = GetGroupBots(sender);
        for (Player* b : allBots)
            if (b->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
                b->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);
    }

    Unit* target = sender->GetSelectedUnit();
    if (!target)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("请先选择一个目标。");
        return;
    }

    std::vector<Player*> bots;

    if (targetType == "tank")
    {
        // 只让坦克职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            if (IsTankClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "melee")
    {
        // 只让近战职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            if (IsMeleeClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "ranged")
    {
        // 只让远程职业攻击
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            // GetGroupBots已经确保返回的都是机器人
            if (IsRangedClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else
    {
        // 所有机器人攻击
        bots = GetGroupBots(sender);
    }

    uint32 attackCount = 0;
    uint32 combatAICount = 0;

    for (Player* bot : bots)
    {
        // 强制清理所有可能的移动任务，防止跟随干扰
        BotMovementManager::instance().StopMovement(bot);
        BotMovementManager::instance().ClearMovementTasks(bot);

        // 清理核心MotionMaster的所有移动生成器
        bot->GetMotionMaster()->Clear();
        bot->StopMoving();

        // 设置战斗标记，防止Faker跟随逻辑干扰（使用UNIT_FLAG_IN_COMBAT确保IsInCombat()返回true）
        bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_IN_COMBAT);

        // 立即开始攻击
        MakeBotAttackTarget(bot, target);
        attackCount++;

        LOG_INFO("server", "命令机器人 {} 攻击目标 {} (已清理跟随任务并设置战斗标记)", bot->GetName(), target->GetName());
    }

    // 提供详细的反馈信息
    std::string typeDesc = "";
    if (targetType == "tank") typeDesc = "坦克";
    else if (targetType == "melee") typeDesc = "近战";
    else if (targetType == "ranged") typeDesc = "远程";
    else typeDesc = "所有";

    if (attackCount == 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "没有找到符合条件的{}机器人。", typeDesc);
        return;
    }

    if (combatAICount > 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人攻击目标 {}。\n"
            "智能战斗AI: {} 个 (自动移动+职业轮换)\n"
            "传统战斗: {} 个",
            attackCount, typeDesc, target->GetName(),
            combatAICount, attackCount - combatAICount);
    }
    else
    {
        ChatHandler(sender->GetSession()).PSendSysMessage(
            "已命令 {} 个{}机器人攻击目标 {} (传统战斗模式)。",
            attackCount, typeDesc, target->GetName());
    }
}

void BotControlCommands::HandleStopAttackCommand(Player* sender, const std::string& targetType)
{
    std::vector<Player*> bots;

    if (targetType == "melee")
    {
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            if (IsMeleeClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else if (targetType == "ranged")
    {
        std::vector<Player*> groupBots = GetGroupBots(sender);
        for (Player* bot : groupBots)
        {
            if (IsRangedClass(bot->getClass()))
                bots.push_back(bot);
        }
    }
    else
    {
        bots = GetGroupBots(sender);
    }

    const bool freezeHere = targetType.empty(); // 仅“停火”命令需要原地不动

    uint32 stopCount = 0;

    for (Player* bot : bots)
    {
        MakeBotStopAttack(bot);
        ++stopCount;
        LOG_INFO("server", "命令机器人 {} 停止攻击", bot->GetName());

        if (freezeHere)
        {
            // 停在原地：停止移动任务并设置为不可动作（用于屏蔽跟随）
            BotMovementManager::instance().StopMovement(bot);
            BotMovementManager::instance().ClearMovementTasks(bot);
            bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);
            LOG_DEBUG("server", "停火(冻结)：机器人 {} 将保持原地不动", bot->GetName());
        }
    }

    std::string typeDesc;
    if (targetType == "melee") typeDesc = "近战";
    else if (targetType == "ranged") typeDesc = "远程";
    else typeDesc = "所有";

    if (stopCount == 0)
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("没有找到符合条件的{}机器人。", typeDesc);
    }
    else
    {
        if (freezeHere)
            ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个{}机器人停火并原地不动。", stopCount, typeDesc);
        else
            ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个{}机器人停止攻击。", stopCount, typeDesc);
    }
}

// 位置与阵型命令实现
void BotControlCommands::HandleGatherCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 gatherCount = 0;
    for (Player* bot : bots)
    {
        // 集合前先解除冻结（PACIFIED）
        if (bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
            bot->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);

        // 随机分散在玩家身边0.5-5码范围，更真实
        BotMovementManager::instance().SpreadFromPosition(
            bot,
            sender->GetPositionX(),
            sender->GetPositionY(),
            0.5f, 5.0f);
        gatherCount++;
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人集合(随机分散0.5-5码)。", gatherCount);
}

void BotControlCommands::HandleSummonAllCommand(Player* sender)
{
    if (!sender)
        return;

    std::vector<Player*> bots = GetGroupBots(sender);
    if (bots.empty())
    {
        ChatHandler(sender->GetSession()).PSendSysMessage("队伍中没有机器人。");
        return;
    }

    // 获取玩家当前位置信息
    float x = sender->GetPositionX();
    float y = sender->GetPositionY();
    float z = sender->GetPositionZ();
    float o = sender->GetOrientation();
    uint32 mapId = sender->GetMapId();

    LOG_INFO("server", "HandleSummonAllCommand: 召唤全员到位置 ({:.1f}, {:.1f}, {:.1f}) 地图ID: {}",
             x, y, z, mapId);

    uint32 summonCount = 0;
    uint32 crossMapCount = 0;
    uint32 failedCount = 0;

    for (Player* bot : bots)
    {
        if (!bot || !PlayerPatch::GetIsFaker(bot))
        {
            failedCount++;
            continue;
        }

        // 召唤前先解除冻结（PACIFIED）
        if (bot->HasFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED))
            bot->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);


        // 检查机器人当前位置
        uint32 botMapId = bot->GetMapId();

        LOG_INFO("server", "召唤机器人 {} 从地图 {} 到地图 {}",
                 bot->GetName(), botMapId, mapId);

        // 计算随机位置（在玩家周围3码范围内）
        float angle = frand(0, 2 * M_PI);
        float distance = frand(1.0f, 3.0f);
        float targetX = x + cos(angle) * distance;
        float targetY = y + sin(angle) * distance;
        float targetZ = z;

        // 强制传送机器人（支持跨地图）
        bool success = MakeBotTeleportTo(bot, targetX, targetY, targetZ, mapId);

        if (success)
        {
            summonCount++;
            if (botMapId != mapId)
            {
                crossMapCount++;
            }
        }
        else
        {
            failedCount++;
        }
    }

    // 提供详细的反馈信息
    std::string message = "召唤完成：" + std::to_string(summonCount) + " 个机器人已传送到身边";

    if (crossMapCount > 0)
    {
        message += "（其中 " + std::to_string(crossMapCount) + " 个跨地图传送）";
    }

    if (failedCount > 0)
    {
        message += "，" + std::to_string(failedCount) + " 个传送失败";
    }

    ChatHandler(sender->GetSession()).PSendSysMessage(message);

    LOG_INFO("server", "HandleSummonAllCommand: 召唤结果 - 成功: {}, 跨地图: {}, 失败: {}",
             summonCount, crossMapCount, failedCount);
}



void BotControlCommands::HandleSpreadCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 spreadCount = 0;
    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人
        BotMovementManager::instance().SpreadFromPosition(bot, sender->GetPositionX(), sender->GetPositionY(), 5.0f, 10.0f);
        spreadCount++;
        LOG_INFO("server", "命令机器人 {} 使用MovementSystem散开 (5-10码范围)", bot->GetName());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人散开 (5-10码范围)。", spreadCount);
}

void BotControlCommands::HandleDispelCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    uint32 dispelCount = 0;
    for (Player* bot : bots)
    {
        // GetGroupBots已经确保返回的都是机器人
        // 这里可以添加驱散逻辑，比如使用特定法术
        // 暂时只是发送消息
        dispelCount++;
        LOG_INFO("server", "命令机器人 {} 进行驱散", bot->GetName());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人进行驱散。", dispelCount);
}

void BotControlCommands::HandleTriangleFormationCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    if (bots.size() < 2)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("附近机器人数量不足，无法组成三角阵型。");
        return;
    }

    float baseX = sender->GetPositionX();
    float baseY = sender->GetPositionY();
    float baseZ = sender->GetPositionZ();

    uint32 formationCount = 0;
    for (size_t i = 0; i < bots.size() && i < 6; ++i)
    {
        // GetGroupBots已经确保返回的都是机器人
        // 使用MovementSystem的三角阵型方法
        BotMovementManager::instance().MoveToTriangleFormation(bots[i], baseX, baseY, baseZ, i);
        formationCount++;
        LOG_INFO("server", "命令机器人 {} 使用MovementSystem移动到三角阵型位置{}", bots[i]->GetName(), i);
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人组成三角阵型。", formationCount);
}

void BotControlCommands::HandleCircleFormationCommand(Player* sender)
{
    std::vector<Player*> bots = GetGroupBots(sender);

    if (bots.size() < 3)
    {
        ChatHandler(sender->GetSession()).SendSysMessage("附近机器人数量不足，无法组成圆形阵型。");
        return;
    }

    float baseX = sender->GetPositionX();
    float baseY = sender->GetPositionY();
    float baseZ = sender->GetPositionZ();
    float radius = 10.0f;

    uint32 formationCount = 0;
    for (size_t i = 0; i < bots.size(); ++i)
    {
        // GetGroupBots已经确保返回的都是机器人
        // 使用MovementSystem的圆形阵型方法
        BotMovementManager::instance().MoveToCircleFormation(bots[i], baseX, baseY, baseZ, radius, i, bots.size());
        formationCount++;
        LOG_INFO("server", "命令机器人 {} 使用MovementSystem移动到圆形阵型位置{}/{}", bots[i]->GetName(), i + 1, bots.size());
    }

    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人组成圆形阵型。", formationCount);
}

void BotControlCommands::HandleAvoidCrowdCommand(Player* sender)
{
    LOG_INFO("server", "执行远离人群命令，玩家: {}", sender->GetName());
    std::vector<Player*> bots = GetGroupBots(sender);

    LOG_INFO("server", "获取到机器人数量: {}", bots.size());

    uint32 avoidCount = 0;
    for (Player* bot : bots)
    {
        LOG_INFO("server", "处理机器人: {}", bot->GetName());

        // bots列表中的都应该是机器人，直接执行命令
        // 让机器人远离人群，移动到较远的位置
        float angle = frand(0, 2 * M_PI);
        float distance = frand(20.0f, 40.0f);
        float x = sender->GetPositionX() + cos(angle) * distance;
        float y = sender->GetPositionY() + sin(angle) * distance;
        float z = sender->GetPositionZ();

        LOG_INFO("server", "命令机器人 {} 移动到位置 ({}, {}, {})", bot->GetName(), x, y, z);
        MakeBotMoveTo(bot, x, y, z);
        avoidCount++;
    }

    LOG_INFO("server", "远离人群命令完成，影响机器人数量: {}", avoidCount);
    ChatHandler(sender->GetSession()).PSendSysMessage("已命令 {} 个机器人远离人群。", avoidCount);
}

// 私聊命令处理实现
void BotControlCommands::HandleLeaveGroupCommand(Player* bot)
{
    if (!bot->GetGroup())
        return;

    Group* group = bot->GetGroup();
    group->RemoveMember(bot->GetGUID());

    LOG_INFO("server", "机器人 {} 收到私聊命令，已退出队伍", bot->GetName());
}

void BotControlCommands::HandleAvoidCrowdWhisperCommand(Player* bot, Player* sender)
{
    if (!bot || !sender)
        return;

    // 只有接收到私聊的机器人才执行命令
    LOG_INFO("server", "机器人 {} 收到来自 {} 的私聊'远离人群'命令", bot->GetName(), sender->GetName());

    // 计算远离位置
    float avoidDistance = 15.0f; // 远离距离
    float angle = frand(0, 2 * M_PI); // 随机角度

    float x = bot->GetPositionX() + avoidDistance * std::cos(angle);
    float y = bot->GetPositionY() + avoidDistance * std::sin(angle);
    float z = bot->GetPositionZ();

    // 移动到远离位置
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MovePoint(0, x, y, z);

    LOG_INFO("server", "机器人 {} 执行远离人群命令，移动到位置 ({:.1f}, {:.1f}, {:.1f})",
             bot->GetName(), x, y, z);
}

void BotControlCommands::HandleLeaveArenaTeamCommand(Player* bot, uint8 teamType)
{
    LOG_INFO("server", "机器人 {} 尝试退出 {}v{} 竞技场战队", bot->GetName(), teamType, teamType);

    uint32 arenaTeamId = 0;

    // 调试：显示机器人当前的所有竞技场战队信息
    uint32 team2v2 = bot->GetArenaTeamId(ARENA_TEAM_2v2);
    uint32 team3v3 = bot->GetArenaTeamId(ARENA_TEAM_3v3);
    uint32 team5v5 = bot->GetArenaTeamId(ARENA_TEAM_5v5);
    LOG_INFO("server", "机器人 {} 当前竞技场战队: 2v2={}, 3v3={}, 5v5={}",
        bot->GetName(), team2v2, team3v3, team5v5);

    switch (teamType)
    {
        case 2:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_2v2);
            LOG_INFO("server", "尝试退出2v2战队，ID: {}", arenaTeamId);
            break;
        case 3:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_3v3);
            LOG_INFO("server", "尝试退出3v3战队，ID: {}", arenaTeamId);
            break;
        case 5:
            arenaTeamId = bot->GetArenaTeamId(ARENA_TEAM_5v5);
            LOG_INFO("server", "尝试退出5v5战队，ID: {}", arenaTeamId);
            break;
        default:
            LOG_ERROR("server", "无效的竞技场战队类型: {}", teamType);
            return;
    }

    if (arenaTeamId == 0)
    {
        LOG_INFO("server", "机器人 {} 没有加入 {}v{} 竞技场战队", bot->GetName(), teamType, teamType);
        return;
    }

    ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
    if (arenaTeam)
    {
        LOG_INFO("server", "找到竞技场战队: {} (ID: {})", arenaTeam->GetName(), arenaTeamId);
        arenaTeam->DelMember(bot->GetGUID(), true);
        LOG_INFO("server", "机器人 {} 成功退出 {}v{} 竞技场战队: {}",
            bot->GetName(), teamType, teamType, arenaTeam->GetName());
    }
    else
    {
        LOG_ERROR("server", "找不到竞技场战队 ID: {}", arenaTeamId);
    }
}

void BotControlCommands::HandleLeaveArenaTeamByName(Player* bot, const std::string& teamName)
{
    LOG_INFO("server", "机器人 {} 尝试按名称退出竞技场战队: [{}]", bot->GetName(), teamName);

    // 检查所有竞技场战队类型
    std::vector<std::pair<uint8, ArenaTeamTypes>> teamTypes = {
        {2, ARENA_TEAM_2v2},
        {3, ARENA_TEAM_3v3},
        {5, ARENA_TEAM_5v5}
    };

    bool foundTeam = false;
    for (const auto& teamTypePair : teamTypes)
    {
        uint32 arenaTeamId = bot->GetArenaTeamId(teamTypePair.second);
        if (arenaTeamId != 0)
        {
            ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
            if (arenaTeam)
            {
                std::string currentTeamName = arenaTeam->GetName();
                LOG_INFO("server", "检查战队: {} (ID: {}) vs 目标: {}",
                    currentTeamName, arenaTeamId, teamName);

                // 检查战队名称是否匹配（支持部分匹配）
                if (currentTeamName.find(teamName) != std::string::npos)
                {
                    arenaTeam->DelMember(bot->GetGUID(), true);
                    LOG_INFO("server", "机器人 {} 成功退出竞技场战队: {} ({}v{})",
                        bot->GetName(), currentTeamName, teamTypePair.first, teamTypePair.first);
                    foundTeam = true;
                    break;
                }
            }
        }
    }

    if (!foundTeam)
    {
        LOG_INFO("server", "机器人 {} 没有找到名称包含 [{}] 的竞技场战队", bot->GetName(), teamName);

        // 显示机器人当前所有战队信息
        LOG_INFO("server", "机器人 {} 当前竞技场战队信息:", bot->GetName());
        for (const auto& teamTypePair : teamTypes)
        {
            uint32 arenaTeamId = bot->GetArenaTeamId(teamTypePair.second);
            if (arenaTeamId != 0)
            {
                ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
                if (arenaTeam)
                {
                    LOG_INFO("server", "  {}v{}: {} (ID: {})",
                        teamTypePair.first, teamTypePair.first, arenaTeam->GetName(), arenaTeamId);
                }
            }
        }
    }
}

void BotControlCommands::HandleLeaveArenaTeamByIndex(Player* bot, uint8 teamIndex)
{
    // 使用正确的方法（参考mod-jbbot的实现）
    LOG_INFO("server", "机器人 {} 尝试退出竞技场战队，索引: {}", bot->GetName(), teamIndex);

    // 直接使用GetArenaTeamId(index)方法
    uint32 arenaTeamId = bot->GetArenaTeamId(teamIndex);

    if (arenaTeamId == 0)
    {
        std::string teamTypeName;
        switch (teamIndex)
        {
            case 0: teamTypeName = "2v2"; break;
            case 1: teamTypeName = "3v3"; break;
            case 2: teamTypeName = "5v5"; break;
            default: teamTypeName = "未知"; break;
        }
        LOG_INFO("server", "机器人 {} 没有加入 {} 竞技场战队", bot->GetName(), teamTypeName);
        return;
    }

    ArenaTeam* arenaTeam = sArenaTeamMgr->GetArenaTeamById(arenaTeamId);
    if (arenaTeam)
    {
        std::string teamName = arenaTeam->GetName();
        std::string teamTypeName;
        switch (teamIndex)
        {
            case 0: teamTypeName = "2v2"; break;
            case 1: teamTypeName = "3v3"; break;
            case 2: teamTypeName = "5v5"; break;
            default: teamTypeName = "未知"; break;
        }

        // 退出竞技场战队
        arenaTeam->DelMember(bot->GetGUID(), true);
        LOG_INFO("server", "机器人 {} 成功退出 {} 竞技场战队: {} (ID: {})",
            bot->GetName(), teamTypeName, teamName, arenaTeamId);
    }
    else
    {
        LOG_ERROR("server", "找不到竞技场战队 ID: {}", arenaTeamId);
    }
}

void BotControlCommands::HandleLeaveGuildCommand(Player* bot)
{
    LOG_INFO("server", "机器人 {} 尝试退出公会", bot->GetName());

    if (!bot->GetGuild())
    {
        LOG_INFO("server", "机器人 {} 没有加入公会", bot->GetName());
        return;
    }

    Guild* guild = bot->GetGuild();
    std::string guildName = guild->GetName();
    guild->DeleteMember(bot->GetGUID(), false, false);

    LOG_INFO("server", "机器人 {} 成功退出公会: {}", bot->GetName(), guildName);
}

// 辅助函数实现
std::vector<Player*> BotControlCommands::GetNearbyBots(Player* player, float range)
{
    std::vector<Player*> bots;

    LOG_INFO("server", "开始搜索附近机器人，范围: {} 码", range);

    std::list<Player*> players;
    Acore::AnyPlayerInObjectRangeCheck checker(player, range);
    Acore::PlayerListSearcher<Acore::AnyPlayerInObjectRangeCheck> searcher(player, players, checker);
    Cell::VisitWorldObjects(player, searcher, range);

    LOG_INFO("server", "找到附近玩家数量: {}", players.size());

    for (Player* nearbyPlayer : players)
    {
        // 排除发起命令的玩家自己
        if (nearbyPlayer == player)
        {
            LOG_INFO("server", "跳过命令发起者: {}", nearbyPlayer->GetName());
            continue;
        }

        LOG_INFO("server", "检查玩家: {} 是否为机器人", nearbyPlayer->GetName());

        bool isFaker = PlayerPatch::GetIsFaker(nearbyPlayer);
        bool isInFakerMap = FakerMap.find(nearbyPlayer->GetGUID().GetCounter()) != FakerMap.end();
        bool isInFakerSessions = false;

        // 检查是否在FakerSessions中
        for (auto session : FakerSessions)
        {
            if (session && session->GetPlayer() &&
                session->GetPlayer()->GetGUID() == nearbyPlayer->GetGUID())
            {
                isInFakerSessions = true;
                break;
            }
        }

        // 检查名字是否包含机器人特征
        std::string playerName = nearbyPlayer->GetName();
        bool hasRobotName = (playerName.find("bot") != std::string::npos ||
                            playerName.find("机器人") != std::string::npos ||
                            playerName.find("rndbot") != std::string::npos );

        LOG_INFO("server", "玩家 {} 检测结果: IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={}",
            nearbyPlayer->GetName(), isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

        // 额外检查：如果玩家在FakerMap中但不在FakerSessions中，可能是真实玩家被误标记
        if (isInFakerMap && !isInFakerSessions && !hasRobotName)
        {
            LOG_INFO("server", "玩家 {} 可能被误标记为机器人，跳过", nearbyPlayer->GetName());
            continue;
        }

        // 多重检测：需要至少两种方式确认是机器人，或者有明显的机器人名字特征
        if ((isFaker && (isInFakerMap || isInFakerSessions)) ||
            (isInFakerMap && isInFakerSessions) ||
            hasRobotName)
        {
            bots.push_back(nearbyPlayer);
            LOG_INFO("server", "确认机器人: {} (通过多重检测)", nearbyPlayer->GetName());

            // 如果通过其他方式检测到是机器人，但PlayerPatch没有标记，则补充标记
            if (!isFaker)
            {
                PlayerPatch::SetIsFaker(nearbyPlayer, true);
                LOG_INFO("server", "补充标记玩家 {} 为机器人", nearbyPlayer->GetName());
            }
        }
        else
        {
            LOG_INFO("server", "非机器人: {}", nearbyPlayer->GetName());
        }
    }

    LOG_INFO("server", "最终找到机器人数量: {}", bots.size());
    return bots;
}

std::vector<Player*> BotControlCommands::GetGroupBots(Player* player)
{
    std::vector<Player*> bots;

    Group* group = player->GetGroup();
    if (!group)
    {
        LOG_INFO("server", "玩家 {} 不在队伍中", player->GetName());
        return bots;
    }

    LOG_INFO("server", "开始搜索队伍中的机器人，队伍成员数: {}", group->GetMembersCount());

    for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
    {
        Player* member = itr->GetSource();
        if (!member || member == player)
            continue;

        LOG_INFO("server", "检查队伍成员: {} 是否为机器人", member->GetName());

        bool isFaker = PlayerPatch::GetIsFaker(member);
        bool isInFakerMap = FakerMap.find(member->GetGUID().GetCounter()) != FakerMap.end();
        bool isInFakerSessions = false;

        // 检查是否在FakerSessions中
        for (auto session : FakerSessions)
        {
            if (session && session->GetPlayer() &&
                session->GetPlayer()->GetGUID() == member->GetGUID())
            {
                isInFakerSessions = true;
                break;
            }
        }

        // 检查名字是否包含机器人特征
        std::string playerName = member->GetName();
        bool hasRobotName = (playerName.find("bot") != std::string::npos ||
                            playerName.find("机器人") != std::string::npos ||
                            playerName.find("rndbot") != std::string::npos );

        LOG_INFO("server", "队伍成员 {} 检测结果: IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={}",
            member->GetName(), isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

        // 强力检测：检查是否为真实玩家
        bool isRealPlayer = false;

        // 检查是否有真实的客户端连接（非机器人特征）
        // if (member->GetSession())
        // {
        //     // 检查IP地址是否为本地（机器人通常使用本地连接）
        //     std::string sessionIP = member->GetSession()->GetRemoteAddress();
        //     if (sessionIP != "127.0.0.1" && sessionIP != "localhost" && sessionIP != "::1")
        //     {
        //         isRealPlayer = true;
        //     }
        // }

        // 如果不是真实玩家，或者有任何机器人特征，就认为是机器人
        if (!isRealPlayer || isFaker || isInFakerMap || isInFakerSessions || hasRobotName)
        {
            bots.push_back(member);
            LOG_INFO("server", "确认队伍机器人: {} (检测方式: IsRealPlayer={}, IsFaker={}, InFakerMap={}, InFakerSessions={}, HasRobotName={})",
                member->GetName(), isRealPlayer, isFaker, isInFakerMap, isInFakerSessions, hasRobotName);

            // 确保机器人被正确标记
            if (!isFaker)
            {
                PlayerPatch::SetIsFaker(member, true);
                LOG_INFO("server", "补充标记队伍成员 {} 为机器人", member->GetName());
            }

            // 确保机器人在FakerMap中
            if (!isInFakerMap)
            {
                FakerMap[member->GetGUID().GetCounter()] = true;
                LOG_INFO("server", "将队伍成员 {} 添加到FakerMap", member->GetName());
            }
        }
        else
        {
            LOG_INFO("server", "队伍成员 {} 是真实玩家", member->GetName());
        }
    }

    LOG_INFO("server", "最终找到队伍机器人数量: {}", bots.size());
    return bots;
}

std::string BotControlCommands::GetClassNameById(uint8 classId)
{
    switch (classId)
    {
        case 1: return "战士";
        case 2: return "圣骑士";
        case 3: return "猎人";
        case 4: return "盗贼";
        case 5: return "牧师";
        case 6: return "死亡骑士";
        case 7: return "萨满";
        case 8: return "法师";
        case 9: return "术士";
        case 11: return "德鲁伊";
        default: return "未知职业";
    }
}

std::string BotControlCommands::GetRaceNameById(uint8 raceId)
{
    switch (raceId)
    {
        // 联盟种族
        case 1: return "人类";
        case 3: return "矮人";
        case 4: return "暗夜精灵";
        case 7: return "侏儒";
        case 11: return "德莱尼";

        // 部落种族
        case 2: return "兽人";
        case 5: return "亡灵";
        case 6: return "牛头人";
        case 8: return "巨魔";
        case 10: return "血精灵";

        default: return "未知种族";
    }
}

bool BotControlCommands::IsPluginMessage(const std::string& message)
{
    // 检查是否为空消息
    if (message.empty())
        return true;

    // 常见插件消息特征
    static const std::vector<std::string> pluginKeywords = {
        "ezCollections",
        "GTFO_v",
        "LibGroupTalents",
        "DBMv4-Ver",
        "BWVQ3",
        "RAL_SELECT",
        "RAL_SELECTREPLY",
        "Absorbs_",
        "SpecializedAbsorbs_",
        "questie",
        "CAIO",
        "AIO",
        "^1^S",
        "msgVer",
        "msgId",
        "!ver",
        "VERSION:",
        "HELLO",
        "Hi!",
        "startcheck"
    };

    // 检查消息是否包含插件关键词
    for (const std::string& keyword : pluginKeywords)
    {
        if (message.find(keyword) != std::string::npos)
        {
            return true;
        }
    }

    // 检查是否包含特殊字符组合（插件通信常用）
    if (message.find("\t") != std::string::npos ||  // Tab字符
        message.find("{{") != std::string::npos ||   // 双大括号
        message.find("}}") != std::string::npos ||   // 双大括号
        message.find("^T^") != std::string::npos ||  // 特殊分隔符
        message.find("^N") != std::string::npos ||   // 特殊分隔符
        message.find("^S") != std::string::npos)     // 特殊分隔符
    {
        return true;
    }

    // 检查是否为纯数字或特殊格式
    if (message.length() > 10 &&
        (message.find_first_not_of("0123456789") == std::string::npos ||
         message.find("%") != std::string::npos))
    {
        return true;
    }

    return false;
}

std::vector<Player*> BotControlCommands::GetGuildBots(Player* player)
{
    std::vector<Player*> bots;

    Guild* guild = player->GetGuild();
    if (!guild)
        return bots;

    // 简化实现：从附近的机器人中筛选公会成员
    std::vector<Player*> nearbyBots = GetNearbyBots(player, 200.0f);

    for (Player* bot : nearbyBots)
    {
        if (bot->GetGuildId() == guild->GetId())
        {
            bots.push_back(bot);
        }
    }

    return bots;
}

std::vector<Player*> BotControlCommands::GetFriendBots(Player* player)
{
    std::vector<Player*> bots;

    // 简化实现：从附近的机器人中筛选，而不是从好友列表
    // 这样避免了访问私有成员的问题
    std::vector<Player*> nearbyBots = GetNearbyBots(player, 200.0f);

    PlayerSocial* social = player->GetSocial();
    if (!social)
        return nearbyBots; // 如果没有社交系统，返回附近所有机器人

    for (Player* bot : nearbyBots)
    {
        if (social->HasFriend(bot->GetGUID()))
        {
            bots.push_back(bot);
        }
    }

    return bots;
}

std::vector<Player*> BotControlCommands::GetBotsByClass(uint8 classId, uint32 maxCount)
{
    std::vector<Player*> bots;

    // 从FakePlayersVec中查找指定职业的机器人
    uint32 count = 0;
    for (const auto& fakePlayer : FakePlayersVec)
    {
        if (count >= maxCount)
            break;

        if (fakePlayer.class_ == classId)
        {
            Player* bot = ObjectAccessor::FindPlayerByName(fakePlayer.pname);
            if (bot && PlayerPatch::GetIsFaker(bot))
            {
                bots.push_back(bot);
                count++;
            }
        }
    }

    return bots;
}

bool BotControlCommands::IsMeleeClass(uint8 classId)
{
    return classId == CLASS_WARRIOR || classId == CLASS_PALADIN ||
           classId == CLASS_ROGUE || classId == CLASS_DEATH_KNIGHT;
}

bool BotControlCommands::IsRangedClass(uint8 classId)
{
    return classId == CLASS_HUNTER || classId == CLASS_PRIEST ||
           classId == CLASS_SHAMAN || classId == CLASS_MAGE ||
           classId == CLASS_WARLOCK || classId == CLASS_DRUID;
}

bool BotControlCommands::IsTankClass(uint8 classId)
{
    return classId == CLASS_WARRIOR || classId == CLASS_PALADIN || classId == CLASS_DEATH_KNIGHT;
}

void BotControlCommands::SendOpcodeToBot(Player* bot, uint16 opcode, WorldPacket& packet)
{
    if (!bot || !bot->GetSession() || !PlayerPatch::GetIsFaker(bot))
        return;

    // 简化实现：直接发送数据包到客户端
    bot->GetSession()->SendPacket(&packet);
}

void BotControlCommands::MakeBotAttackTarget(Player* bot, Unit* target)
{
    if (!bot || !target || !PlayerPatch::GetIsFaker(bot))
        return;

    LOG_INFO("server", "MakeBotAttackTarget: 机器人 {} 攻击目标 {} (增强攻击逻辑)", bot->GetName(), target->GetName());

    // 停止当前所有行为
    bot->GetMotionMaster()->Clear();
    bot->InterruptNonMeleeSpells(false);
    bot->AttackStop();

    // 设置目标并开始攻击
    bot->SetTarget(target->GetGUID());
    bot->SetFacingToObject(target);

    // 强制开始攻击
    bool attackResult = bot->Attack(target, true);
    LOG_INFO("server", "MakeBotAttackTarget: Attack()调用结果: {}", attackResult ? "成功" : "失败");

    // 启动战斗更新定时器，确保机器人会移动到攻击范围
    StartCombatUpdateTimer(bot, target);

    // 立即检查距离并开始移动
    float distance = bot->GetDistance(target);
    uint8 classId = bot->getClass();
    bool isMeleeClass = IsMeleeClass(classId);

    LOG_INFO("server", "MakeBotAttackTarget: 机器人 {} 距离目标 {:.1f}码，职业: {}, 近战: {}",
             bot->GetName(), distance, classId, isMeleeClass);

    if (isMeleeClass && distance > 5.0f)
    {
        // 近战职业立即开始移动到目标
        LOG_INFO("server", "MakeBotAttackTarget: 近战机器人 {} 开始移动到目标", bot->GetName());
        BotMovementManager::instance().ChaseTarget(bot, target, 3.0f);
    }
    else if (!isMeleeClass && distance > 25.0f)
    {
        // 远程职业移动到合适距离
        LOG_INFO("server", "MakeBotAttackTarget: 远程机器人 {} 开始接近目标", bot->GetName());
        BotMovementManager::instance().ApproachTarget(bot, target, 20.0f);
    }

    LOG_INFO("server", "MakeBotAttackTarget: 机器人 {} 攻击设置完成", bot->GetName());
}


void BotControlCommands::StartCombatUpdateTimer(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    uint64 botGuid = bot->GetGUID().GetCounter();
    ObjectGuid targetGuid = target->GetGUID();

    // 存储战斗信息
    CombatInfo info;
    info.botGuid = botGuid;
    info.targetGuid = targetGuid;
    info.lastUpdateTime = getMSTime();
    info.isActive = true;

    activeCombats[botGuid] = info;

    LOG_INFO("server", "StartCombatUpdateTimer: 为机器人 {} 启动战斗更新定时器", bot->GetName());
}

void BotControlCommands::UpdateCombatMovement(Player* bot, Unit* target)
{
    if (!bot || !target)
        return;

    // 启用战斗移动逻辑，确保机器人能正确移动到攻击范围
    LOG_DEBUG("server", "UpdateCombatMovement: 处理机器人 {} 的战斗移动", bot->GetName());

    // 启用战斗移动逻辑 - 确保机器人能移动到攻击范围
    // 检查目标是否还有效
    if (!target->IsAlive() || !bot->IsValidAttackTarget(target))
    {
        LOG_INFO("server", "UpdateCombatMovement: 目标 {} 已无效，停止战斗", target->GetName());
        MakeBotStopAttack(bot);
        return;
    }

    // 使用MovementSystem统一计算距离和攻击范围
    float distance = BotMovementManager::instance().GetDistanceToTarget(bot, target);
    uint8 classId = bot->getClass();
    bool isMeleeClass = IsMeleeClass(classId);

    if (isMeleeClass)
    {
        // 近战职业：使用MovementSystem的攻击范围判定
        bool inRange = BotMovementManager::instance().IsInAttackRange(bot, target);

        if (!inRange)
        {
            LOG_DEBUG("server", "UpdateCombatMovement: 近战机器人 {} 不在攻击范围内，使用MovementSystem追击 (当前距离: {:.1f})",
                     bot->GetName(), distance);

            BotMovementManager::instance().ChaseTarget(bot, target, 5.0f);
        }

        // 确保面向目标
        if (!bot->HasInArc(M_PI / 6, target)) // 30度角度容差
        {
            bot->SetFacingToObject(target);
        }
    }
    else
    {
        // 远程职业：使用MovementSystem保持距离
        float optimalRange = 20.0f;

        if (distance > optimalRange + 10.0f)
        {
            LOG_DEBUG("server", "UpdateCombatMovement: 远程机器人 {} 使用MovementSystem接近目标 (距离: {:.1f})",
                     bot->GetName(), distance);

            BotMovementManager::instance().ApproachTarget(bot, target, optimalRange);
        }
        else if (distance < 8.0f)
        {
            LOG_DEBUG("server", "UpdateCombatMovement: 远程机器人 {} 使用MovementSystem后退 (距离: {:.1f})",
                     bot->GetName(), distance);

            BotMovementManager::instance().RetreatFromTarget(bot, target, 12.0f);
        }
    }
}

void BotControlCommands::MakeBotStopAttack(Player* bot)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    uint64 botGuid = bot->GetGUID().GetCounter();

    LOG_INFO("server", "MakeBotStopAttack: 停止机器人 {} 的攻击 (使用CombatSystem)", bot->GetName());

    // 使用新的CombatSystem停止攻击
    auto ai = PlayerBotAIFactory::CreateAI(bot);
    if (ai)
    {
        // 停止战斗
        ai->StopCombat();

        LOG_INFO("server", "MakeBotStopAttack: 成功使用CombatSystem停止机器人 {} 的攻击", bot->GetName());

        // 从活跃战斗列表中移除
        uint64 botGuid = bot->GetGUID().GetCounter();
        activeCombats.erase(botGuid);

        return;
    }

    // 如果CombatSystem不可用，使用基础停止逻辑作为回退
    LOG_WARN("server", "MakeBotStopAttack: CombatSystem不可用，使用基础停止逻辑");

    // 基础停止攻击逻辑
    bot->AttackStop();
    bot->InterruptNonMeleeSpells(false);
    bot->SetTarget(ObjectGuid::Empty);
    bot->GetMotionMaster()->Clear();
    bot->GetMotionMaster()->MoveIdle();

    LOG_INFO("server", "MakeBotStopAttack: 使用基础逻辑停止机器人 {} 的攻击", bot->GetName());
}

void BotControlCommands::UpdateActiveCombats()
{
    uint32 currentTime = getMSTime();
    std::vector<uint64> toRemove;

    for (auto& pair : activeCombats)
    {
        uint64 botGuid = pair.first;
        CombatInfo& info = pair.second;

        if (!info.isActive)
        {
            toRemove.push_back(botGuid);
            continue;
        }

        // 每1秒更新一次战斗移动
        if (currentTime - info.lastUpdateTime < 1000)
            continue;

        info.lastUpdateTime = currentTime;

        // 获取机器人和目标
        Player* bot = ObjectAccessor::FindPlayerByLowGUID(botGuid);
        if (!bot || !bot->IsInWorld() || !PlayerPatch::GetIsFaker(bot))
        {
            toRemove.push_back(botGuid);
            continue;
        }

        Unit* target = nullptr;

        // 使用ObjectGuid查找目标
        if (info.targetGuid.IsPlayer())
        {
            target = ObjectAccessor::FindPlayer(info.targetGuid);
        }
        else if (info.targetGuid.IsCreatureOrVehicle())
        {
            target = ObjectAccessor::GetCreature(*bot, info.targetGuid);
        }

        if (!target || !target->IsAlive() || !bot->IsValidAttackTarget(target))
        {
            LOG_INFO("server", "UpdateActiveCombats: 机器人 {} 的目标已无效，停止战斗", bot->GetName());
            MakeBotStopAttack(bot);
            toRemove.push_back(botGuid);
            continue;
        }

        // 战斗状态管理交给战斗系统处理，这里不再干预
        // 移除重复的战斗状态检查，避免与战斗系统冲突

        // 启用移动控制逻辑，确保机器人会移动到攻击范围
        if (true)
        {
            // 以下代码仅用于没有战斗AI的机器人（向后兼容）
            uint8 classId = bot->getClass();
            bool isMeleeClass = (classId == CLASS_WARRIOR || classId == CLASS_PALADIN ||
                               classId == CLASS_ROGUE || classId == CLASS_DEATH_KNIGHT);

            // 检查机器人是否已经移动到攻击范围内 - 使用MovementSystem统一计算
            float currentDistance = BotMovementManager::instance().GetDistanceToTarget(bot, target);
            bool inAttackRange = BotMovementManager::instance().IsInAttackRange(bot, target);
            LOG_DEBUG("server", "UpdateActiveCombats: 机器人 {} 当前距离目标 {:.1f}码，在攻击范围内: {}", bot->GetName(), currentDistance, inAttackRange);

            if (isMeleeClass && inAttackRange)
            {
                LOG_INFO("server", "UpdateActiveCombats: 近战机器人 {} 在攻击范围内，强制执行攻击", bot->GetName());

                // ✅ 新增：盗贼使用专门的战斗AI，根据场景选择合适的AI
                if (classId == CLASS_ROGUE)
                {
                    LOG_INFO("server", "UpdateActiveCombats: 盗贼机器人 {} 使用专门战斗AI", bot->GetName());

                    try
                    {
                        // 检查是否已经有持久的盗贼AI实例
                        static std::unordered_map<uint64, PlayerBotAI*> rogueAIInstances;
                        uint64 botGuid = bot->GetGUID().GetCounter();

                        PlayerBotAI* rogueAI = nullptr;
                        auto it = rogueAIInstances.find(botGuid);
                        if (it != rogueAIInstances.end())
                        {
                            rogueAI = it->second;
                            LOG_DEBUG("server", "UpdateActiveCombats: 使用现有的盗贼AI实例");
                        }
                        else
                        {
                            // 直接判断场景，不调用私有方法
                            bool isGroupScenario = false;

                            // 检查是否在组队
                            if (bot->GetGroup())
                            {
                                isGroupScenario = true;
                                LOG_INFO("server", "UpdateActiveCombats: 检测到盗贼机器人 {} 在组队中", bot->GetName());
                            }
                            // 检查是否在战场
                            else if (bot->GetBattleground())
                            {
                                isGroupScenario = false; // 战场使用野外AI
                                LOG_INFO("server", "UpdateActiveCombats: 检测到盗贼机器人 {} 在战场中", bot->GetName());
                            }
                            // 检查是否在决斗
                            else if (bot->duel && bot->duel->State != DUEL_STATE_COMPLETED && bot->duel->Opponent)
                            {
                                isGroupScenario = false; // 决斗使用野外AI
                                LOG_INFO("server", "UpdateActiveCombats: 检测到盗贼机器人 {} 在决斗中", bot->GetName());
                            }
                            else
                            {
                                isGroupScenario = false; // 默认野外场景
                                LOG_INFO("server", "UpdateActiveCombats: 盗贼机器人 {} 使用野外场景", bot->GetName());
                            }

                            if (isGroupScenario)
                            {
                                // 组队场景，使用GroupRogueAI
                                rogueAI = BotGroupAI::CreateBotGroupAIByPlayerClass(bot);
                                LOG_INFO("server", "UpdateActiveCombats: 为盗贼机器人 {} 创建GroupRogueAI实例", bot->GetName());
                            }
                            else
                            {
                                // 野外场景，使用FieldRogueAI
                                rogueAI = BotFieldAI::CreateBotFieldAIByPlayerClass(bot);
                                LOG_INFO("server", "UpdateActiveCombats: 为盗贼机器人 {} 创建FieldRogueAI实例", bot->GetName());
                            }

                            if (rogueAI)
                            {
                                rogueAIInstances[botGuid] = rogueAI;
                            }
                        }

                        if (rogueAI)
                        {
                            // 直接调用战斗系统，让战斗系统自己管理战斗状态
                            if (GroupRogueAI* groupRogue = dynamic_cast<GroupRogueAI*>(rogueAI))
                            {
                                groupRogue->ProcessMeleeSpell(target);
                                LOG_INFO("server", "UpdateActiveCombats: 盗贼机器人 {} GroupRogueAI战斗逻辑执行完成", bot->GetName());
                            }
                            else if (FieldRogueAI* fieldRogue = dynamic_cast<FieldRogueAI*>(rogueAI))
                            {
                                fieldRogue->ProcessMeleeSpell(target);
                                LOG_INFO("server", "UpdateActiveCombats: 盗贼机器人 {} FieldRogueAI战斗逻辑执行完成", bot->GetName());
                            }

                            return; // 盗贼使用专门AI，不执行传统逻辑
                        }
                        else
                        {
                            LOG_ERROR("server", "UpdateActiveCombats: 盗贼机器人 {} 未能创建专门AI，回退到传统逻辑", bot->GetName());
                        }
                    }
                    catch (const std::exception& e)
                    {
                        LOG_ERROR("server", "UpdateActiveCombats: 盗贼机器人 {} AI执行异常: {}，回退到传统逻辑", bot->GetName(), e.what());
                        // 异常时回退到传统攻击逻辑
                    }
                    catch (...)
                    {
                        LOG_ERROR("server", "UpdateActiveCombats: 盗贼机器人 {} AI执行未知异常，回退到传统逻辑", bot->GetName());
                        // 异常时回退到传统攻击逻辑
                    }
                }
                else
                {
                    // 其他职业也使用战斗系统，不再有传统攻击逻辑
                    LOG_INFO("server", "UpdateActiveCombats: 机器人 {} (职业: {}) 使用战斗系统", bot->GetName(), classId);

                    // 创建通用AI实例并调用DoUniversalAttack
                    PlayerBotAI* botAI = BotGroupAI::CreateBotGroupAIByPlayerClass(bot);
                    if (botAI)
                    {
                        botAI->DoUniversalAttack(target);
                        LOG_INFO("server", "UpdateActiveCombats: 机器人 {} 战斗系统执行完成", bot->GetName());
                    }
                    else
                    {
                        LOG_ERROR("server", "UpdateActiveCombats: 机器人 {} 无法创建AI实例", bot->GetName());
                    }
                }
            }
            else if (isMeleeClass)
            {
                LOG_INFO("server", "UpdateActiveCombats: 近战机器人 {} 距离过远({:.1f}码)，继续移动", bot->GetName(), currentDistance);
                // 使用MovementSystem确保机器人继续移动到目标
                if (bot->GetMotionMaster()->GetCurrentMovementGeneratorType() != CHASE_MOTION_TYPE)
                {
                    BotMovementManager::instance().ChaseTarget(bot, target, 3.0f);
                }
            }

            // 更新战斗移动（仅用于没有战斗AI的机器人）
            UpdateCombatMovement(bot, target);
        }
        else
        {
            LOG_DEBUG("server", "UpdateActiveCombats: 机器人 {} 有战斗AI，跳过旧的移动控制系统", bot->GetName());
        }
    }

    // 清理无效的战斗信息
    for (uint64 guid : toRemove)
    {
        activeCombats.erase(guid);
    }
}

void BotControlCommands::MakeBotMoveTo(Player* bot, float x, float y, float z)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用MovementSystem的移动方法
    BotMovementManager::instance().MoveToPosition(bot, x, y, z);

    LOG_DEBUG("server", "命令机器人 {} 使用MovementSystem移动到位置 ({}, {}, {})", bot->GetName(), x, y, z);
}

bool BotControlCommands::MakeBotTeleportTo(Player* bot, float x, float y, float z, uint32 mapId)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return false;

    LOG_INFO("server", "MakeBotTeleportTo: 开始传送机器人 {} 到位置 ({:.1f}, {:.1f}, {:.1f}) 地图: {}",
             bot->GetName(), x, y, z, mapId);

    try
    {
        // 停止机器人当前的所有行为
        bot->GetMotionMaster()->Clear();
        bot->InterruptNonMeleeSpells(false);
        bot->AttackStop();

        // 如果机器人在战斗中，强制脱离战斗
        if (bot->IsInCombat())
        {
            bot->CombatStop();
            LOG_INFO("server", "MakeBotTeleportTo: 机器人 {} 强制脱离战斗", bot->GetName());
        }

        // 执行传送
        bool success = bot->TeleportTo(mapId, x, y, z, bot->GetOrientation());

        if (success)
        {
            LOG_INFO("server", "MakeBotTeleportTo: 机器人 {} 传送成功", bot->GetName());

            // 传送后的清理工作
            bot->SetTarget(ObjectGuid::Empty);

            return true;
        }
        else
        {
            LOG_WARN("server", "MakeBotTeleportTo: 机器人 {} 传送失败 - TeleportTo返回false", bot->GetName());
            return false;
        }
    }
    catch (...)
    {
        LOG_ERROR("server", "MakeBotTeleportTo: 机器人 {} 传送时发生异常", bot->GetName());
        return false;
    }
}

// 重载方法：简化版本（同地图传送）
void BotControlCommands::MakeBotTeleportTo(Player* bot, float x, float y, float z)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用增强版本，传送到当前地图
    MakeBotTeleportTo(bot, x, y, z, bot->GetMapId());
}

void BotControlCommands::MakeBotFollowPlayer(Player* bot, Player* target, float distance)
{
    if (!bot || !target || !PlayerPatch::GetIsFaker(bot))
        return;

    // 使用MovementSystem的跟随功能
    BotMovementManager::instance().FollowTarget(bot, target, distance);

    LOG_DEBUG("server", "机器人 {} 使用MovementSystem跟随玩家 {} (距离: {:.1f})",
              bot->GetName(), target->GetName(), distance);
}

void BotControlCommands::SetBotGroupFollowMode(Player* bot, bool enable)
{
    if (!bot || !PlayerPatch::GetIsFaker(bot))
        return;

    if (enable)
    {
        // 停止所有移动和行为，开启队伍跟随模式
        bot->GetMotionMaster()->Clear();
        bot->GetMotionMaster()->MoveIdle();

        // 停止攻击
        bot->AttackStop();
        bot->InterruptNonMeleeSpells(false);

        // 清除目标
        bot->SetTarget(ObjectGuid::Empty);

        // 强制设置机器人为IDLE模式，确保BotBehaviorEngine不会生成随机行为
        if (sBotBehaviorEngine)
        {
            sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
        }

        // 在机器人身上设置一个标记，表示正在跟随模式
        bot->SetFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED); // 使用和平标记

        /*LOG_INFO("server", "机器人 {} 开启队伍跟随模式，停止所有行为，设置IDLE模式", bot->GetName());*/
    }
    else
    {
        // 恢复随机移动
        bot->GetMotionMaster()->Clear();

        // 移除和平标记
        bot->RemoveFlag(UNIT_FIELD_FLAGS, UNIT_FLAG_PACIFIED);

        // 恢复探索模式
        if (sBotBehaviorEngine)
        {
            sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_EXPLORING);
        }

        BotMovementManager::instance().MoveToRandomPosition(bot, 10.0f);
        /*LOG_INFO("server", "机器人 {} 恢复随机移动模式，设置EXPLORING模式", bot->GetName());*/
    }
}

void BotControlCommands::UpdateBotGroupFollow()
{
    // ✅ 注释掉组队跟随逻辑 - 避免干扰战斗系统
    LOG_DEBUG("server", "BotControlCommands::UpdateBotGroupFollow 已禁用 - 避免干扰战斗系统");
    return;

    /*
    // 遍历所有在线机器人，检查其队伍状态
    for (auto session : FakerSessions)
    {
        if (!session || !session->GetPlayer())
            continue;

        Player* bot = session->GetPlayer();
        Group* group = bot->GetGroup();

        if (group)
        {
            // 检查机器人是否在战斗中，如果在战斗中则不干扰
            bool inCombat = bot->IsInCombat();
            bool hasTarget = bot->GetTarget() && bot->GetSelectedUnit();
            bool isAttacking = bot->GetVictim() != nullptr;

            //  重新集成BotCombatAI检查 (旧系统，已禁用)
#ifndef DISABLE_OLD_COMBAT_SYSTEM
            bool usingCombatAI = false;
            auto combatAI = sBotCombatAIMgr->GetCombatAI(bot);
            if (combatAI)
            {
                usingCombatAI = combatAI->IsInCombat() || combatAI->HasTarget();
            }
#else
            bool usingCombatAI = false;
#endif

            // 特殊检查：是否正在攻击训练假人
            bool attackingTrainingDummy = false;
            if (hasTarget)
            {
                Unit* target = bot->GetSelectedUnit();
                if (target && target->ToCreature())
                {
                    Creature* creature = target->ToCreature();
                    std::string name = creature->GetName();
                    std::transform(name.begin(), name.end(), name.begin(), ::tolower);
                    if (name.find("training") != std::string::npos ||
                        name.find("target") != std::string::npos ||
                        name.find("dummy") != std::string::npos)
                    {
                        attackingTrainingDummy = true;
                    }
                }
            }

            // 增强战斗状态检查：添加更多条件
            bool hasAttackState = bot->HasUnitState(UNIT_STATE_MELEE_ATTACKING);
            bool isCasting = bot->HasUnitState(UNIT_STATE_CASTING);
            bool isMovingToTarget = false;

            // 检查是否正在移动到攻击目标 (旧系统，已禁用)
#ifndef DISABLE_OLD_COMBAT_SYSTEM
            if (combatAI)
            {
                auto movement = combatAI->GetMovement();
                if (movement)
                {
                    isMovingToTarget = movement->IsMovingToTarget();
                }
            }
#endif

            // 如果机器人在任何战斗相关状态中，跳过跟随逻辑
            if (inCombat || hasTarget || usingCombatAI || isAttacking || attackingTrainingDummy ||
                hasAttackState || isCasting || isMovingToTarget)
            {
                LOG_DEBUG("server", "机器人 {} 在战斗中，跳过跟随逻辑 (战斗:{}, 目标:{}, AI:{}, 攻击:{}, 训练假人:{}, 攻击状态:{}, 施法:{}, 移动到目标:{})",
                         bot->GetName().c_str(), inCombat, hasTarget, usingCombatAI, isAttacking,
                         attackingTrainingDummy, hasAttackState, isCasting, isMovingToTarget);
                continue;
            }

            // 机器人在队伍中且不在战斗，执行跟随逻辑
            SetBotGroupFollowMode(bot, true);

            // 设置机器人为空闲模式（仅在非战斗时）
            if (sBotBehaviorEngine)
            {
                sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_IDLE);
            }

            // 找到队长并跟随
            Player* leader = ObjectAccessor::FindPlayer(group->GetLeaderGUID());
            if (leader && leader != bot && !PlayerPatch::GetIsFaker(leader))
            {
                // 跟随真实玩家队长
                float distance = bot->GetDistance(leader);
                if (distance > 8.0f) // 距离超过8码时跟随
                {
                    // 开始跟随 - 保持在5码距离
                    MakeBotFollowPlayer(bot, leader, 5.0f);

                    LOG_DEBUG("server", "机器人 {} 跟随队长 {} (距离: {:.1f})",
                        bot->GetName(), leader->GetName(), distance);
                }
                else if (distance < 3.0f)
                {
                    // 距离太近时停止移动
                    bot->GetMotionMaster()->Clear();
                    bot->GetMotionMaster()->MoveIdle();
                }
                else if (distance >= 3.0f && distance <= 8.0f)
                {
                    // 在理想距离范围内，保持待机
                    if (bot->GetMotionMaster()->GetCurrentMovementGeneratorType() != IDLE_MOTION_TYPE)
                    {
                        bot->GetMotionMaster()->Clear();
                        bot->GetMotionMaster()->MoveIdle();
                    }
                }

                // 每次更新都强制检查和停止随机行为
                LOG_DEBUG("server", "机器人 {} 在队伍中，强制保持跟随状态", bot->GetName());
            }
        }
        else
        {
            // 机器人不在队伍中，恢复随机移动
            SetBotGroupFollowMode(bot, false);

            // 恢复探索模式
            if (sBotBehaviorEngine)
            {
                sBotBehaviorEngine->SetBehaviorMode(bot, BotBehaviorEngine::BEHAVIOR_EXPLORING);
            }
        }
    }
    */
}

// 聊天脚本实现
void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg)
{
    LOG_INFO("server", "OnPlayerChat(基础): 玩家 {} 说: '{}' 类型: {}", player->GetName(), msg, type);

    // 处理说话频道中的GM命令
    if (type == CHAT_MSG_SAY)
    {
        LOG_INFO("server", "处理说话频道GM命令");
        sBotControlCommands->HandleGMCommand(player, msg);
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Player* receiver)
{
    // 过滤插件通信消息
    if (sBotControlCommands->IsPluginMessage(msg))
    {
        return; // 忽略插件通信
    }

    std::string receiverName = receiver ? receiver->GetName() : "无";
    LOG_INFO("server", "OnPlayerChat(私聊): 玩家 {} 对 {} 说: [{}] 类型: {}",
        player->GetName(), receiverName, msg, type);

    // 处理私聊 - 支持机器人控制命令和机器人私聊命令
    if (type == CHAT_MSG_WHISPER && receiver)
    {
        // 如果接收者是机器人，处理机器人私聊命令
        if (PlayerPatch::GetIsFaker(receiver))
        {
            LOG_INFO("server", "处理机器人私聊命令");
            sBotControlCommands->HandleWhisperCommand(receiver, player, msg);
        }
        // 如果发送者想通过私聊控制机器人，也支持控制命令
        else
        {
            LOG_INFO("server", "处理私聊控制命令");
            sBotControlCommands->HandleChatCommand(player, msg, type);
        }
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Group* group)
{
    // 增强的插件消息过滤
    if (sBotControlCommands->IsPluginMessage(msg))
    {
        return; // 忽略插件通信
    }

    LOG_INFO("server", "OnPlayerChat(队伍): 玩家 {} 在队伍中说: '{}' 类型: {} 队伍: {}",
        player->GetName(), msg, type, group ? "有" : "无");

    // 处理队伍聊天和团队聊天 - 添加类型51支持
    if (type == CHAT_MSG_PARTY || type == CHAT_MSG_RAID || type == 51)
    {
        LOG_INFO("server", "处理队伍聊天控制命令: '{}'", msg);
        bool handled = sBotControlCommands->HandleChatCommand(player, msg, type);
        LOG_INFO("server", "命令处理结果: {}", handled ? "成功" : "未找到命令");
    }
    else
    {
        LOG_INFO("server", "忽略非队伍聊天消息，类型: {}", type);
    }
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Guild* guild)
{
    // 不处理公会聊天
    return;
}

void BotControlChatScript::OnPlayerChat(Player* player, uint32 type, uint32 lang, std::string& msg, Channel* channel)
{
    // 不处理频道聊天
    return;
}

// GM命令脚本实现
Acore::ChatCommands::ChatCommandTable BotControlCommandScript::GetCommands() const
{
    using namespace Acore::ChatCommands;

    static ChatCommandTable botControlCommandTable =
    {
        { "onlineguildmember", HandleOnlineGuildMemberCommand, SEC_PLAYER, Console::No },
        { "offlineallbot", HandleOfflineAllBotCommand, SEC_PLAYER, Console::No },
        { "onlinefriends", HandleOnlineFriendsCommand, SEC_PLAYER, Console::No },
        { "groupfriend", HandleGroupFriendCommand, SEC_PLAYER, Console::No },
        { "invitefriend", HandleInviteFriendCommand, SEC_PLAYER, Console::No },
        { "addclassbot", HandleAddClassBotCommand, SEC_PLAYER, Console::No },
        { "resetdungeon", HandleResetDungeonCommand, SEC_PLAYER, Console::No },
        { "test", HandleTestCommand, SEC_PLAYER, Console::No },
    };

    static ChatCommandTable commandTable =
    {
        { "bot", botControlCommandTable },
    };

    return commandTable;
}

bool BotControlCommandScript::HandleOnlineGuildMemberCommand(ChatHandler* handler, const char* args)
{
    LOG_INFO("server", "执行GM命令: onlineguildmember");
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOnlineGuildMemberCommand(player);
}

bool BotControlCommandScript::HandleOfflineAllBotCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOfflineAllBotCommand(player);
}

bool BotControlCommandScript::HandleOnlineFriendsCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleOnlineFriendsCommand(player);
}

bool BotControlCommandScript::HandleGroupFriendCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleGroupFriendCommand(player);
}

bool BotControlCommandScript::HandleInviteFriendCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleInviteFriendCommand(player);
}

bool BotControlCommandScript::HandleAddClassBotCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    if (!args || !*args)
    {
        handler->SendSysMessage("用法: .bot addclass [职业ID]");
        handler->SendSysMessage("职业ID: 1-战士，2-圣骑，3-猎人，4-盗贼，5-牧师，6-死骑，7-萨满，8-法师，9-术士，11-德鲁伊");
        return false;
    }

    uint8 classId = atoi(args);
    return sBotControlCommands->HandleAddClassBotCommand(player, classId);
}

bool BotControlCommandScript::HandleResetDungeonCommand(ChatHandler* handler, const char* args)
{
    Player* player = handler->GetSession()->GetPlayer();
    if (!player)
        return false;

    return sBotControlCommands->HandleResetDungeonCommand(player);
}

bool BotControlCommandScript::HandleTestCommand(ChatHandler* handler, const char* args)
{
    LOG_INFO("server", "执行GM测试命令: test");
    handler->SendSysMessage("机器人控制GM命令系统正常工作！");
    return true;
}

// 世界脚本用于初始化系统
class BotControlWorldScript : public WorldScript
{
public:
    BotControlWorldScript() : WorldScript("BotControlWorldScript") {}

    void OnStartup() override
    {
        sBotControlCommands->Initialize();
        LOG_INFO("server.loading", "机器人控制命令系统已初始化");
    }

    void OnUpdate(uint32 diff) override
    {
        static uint32 updateTimer = 0;
        static uint32 combatUpdateTimer = 0;

        updateTimer += diff;
        combatUpdateTimer += diff;

        // ✅ 注释掉机器人跟随状态更新 - 避免干扰战斗系统
        if (updateTimer >= 1000)
        {
            // sBotControlCommands->UpdateBotGroupFollow(); // 已禁用
            updateTimer = 0;
        }

        // 每500ms更新一次战斗状态（确保近战机器人能及时跟随目标）
        if (combatUpdateTimer >= 500)
        {
            sBotControlCommands->UpdateActiveCombats();
            combatUpdateTimer = 0;
        }
    }
};







// 脚本注册函数
void AddSC_BotControlCommands()
{
    new BotControlWorldScript();
    new BotControlChatScript();
    new BotControlCommandScript();
}