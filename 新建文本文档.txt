.月干 测试 命令

.月干 测试 <actionType> [creatureId] [x] [y] [z] [role]

参数说明
<actionType>: 必填，动作类型编号，例如 8 表示拉怪到指定位置
[creatureId]: 可选，目标怪物ID，如果不填则使用当前选中的目标
[x] [y] [z]: 可选，目标坐标，如果不填则使用玩家当前位置
[role]: 可选，角色类型，0表示任何坦克，1表示主坦，2-4表示副坦

使用示例
1. 拉当前选中的怪物到玩家位置:
   .月干 测试 8

2. 拉指定ID的怪物到玩家位置:
   .月干 测试 8 10184
这会让坦克机器人拉ID为10184的怪物(奥妮克希亚)到玩家当前位置

3. 拉当前选中的怪物到指定位置:
   .月干 测试 8 0 -8632.45 1033.85 41.32
这会让坦克机器人拉当前选中的怪物到坐标(-8632.45, 1033.85, 41.32)

4. 指定主坦克拉怪:
   .月干 测试 8 10184 20.38 -178.82 -85.18 1
这会让主坦克拉ID为10184的怪物到指定坐标

5. 让任何坦克拉怪:
   .月干 测试 8 10184 -8632.45 1033.85 41.32 0

其他常用动作类型
1: 躲避AOE
3: 集火目标
4: 移动到指定位置(边跑边打)
5: 远离目标
6: 执行驱散
7: 换嘲讽
9: 分配坦克目标
11: 治疗跟随
19: 立即移动到指定位置
例如，让治疗跟随主坦克:
.月干 测试 11 0 0 0 0 1

.月干 测试 4 0 2075.69 -4648.27 50.75989 2000 0

物品兑换系统

默认关闭需要命令开启
使用"兑换开启"命令开启兑换系统
使用"兑换关闭"命令关闭兑换系统
使用"兑换"命令可让机器人自行执行兑换
机器人兑换物品会自动检测自身天赋,按照数据库表 bot_exchange_item的设置兑换相应的物品,如果天赋不匹配,可以手动设置机器人天赋
使用命令"设置天赋" 0,1,2 对应第一页,第二页,第三页天赋
使用命令"清除天赋"



在BotTacticalAI.h中添加了TacticalMoveTo方法的声明，它是MovementActions.cpp中MoveTo方法的修改版本
在BotTacticalAI.cpp中实现了TacticalMoveTo方法，基于MovementActions.cpp中的MoveTo方法，但适配到BotTacticalAI系统中
优化了MoveToPositionSafely方法，使用TacticalMoveTo进行移动
修改了HandleMovePositionSimple方法，使用TacticalMoveTo进行移动
修改了HandleMovePositionInstant方法，使用TacticalMoveTo进行移动
修改了HandleMoveToCreature方法，使用TacticalMoveTo进行移动
这些修改使BotTacticalAI系统能够使用MovementActions中的MoveTo功能，但避免了直接依赖和可能的冲突。
我们保留了MoveTo方法的核心功能，包括:
处理障碍物和高度差异
支持载具移动
计算适当的移动路径
处理各种移动状态


RoleHelper类的角色判定机制
RoleHelper类通过分析玩家的职业(Class)和天赋树(Talent Tree)数据来判断角色的职责类型：
坦克判定(IsTank)：检查战士的防护天赋、圣骑士的防护天赋、死亡骑士的鲜血天赋或德鲁伊的野性天赋(熊形态)是否是主要天赋
治疗判定(IsHealer)：检查牧师的神圣天赋、圣骑士的神圣天赋、萨满的恢复天赋或德鲁伊的恢复天赋是否是主要天赋
DPS判定：分为近战DPS(IsMeleeDPS)、远程DPS(IsRangedDPS)、物理DPS(IsPhysicalDPS)和魔法DPS(IsMagicDPS)

按照如下顺序选择主坦克： 
1. 优先使用团队标记：将带有团队标记的坦克优先考虑为主坦克，
标记优先级为：星形(0) > 大饼(1) > 菱形(2) > 三角(3) > 月亮(4) > 方块(5) > 十字(6) > 骷髅(7)  
2. 职业优先级：如果没有标记，则按照死亡骑士 = 圣骑士 > 德鲁伊 > 战士的顺序选择  
3. 天赋点数：同样职业之间，比较坦克天赋点数，差异超过5点时优先选择天赋点数更多的  
4. 装备评分：天赋点数接近时，选择装备等级更高的


邮购系统:
普通聊天频道输入‘ 购买/buy/mailorder/邮购/邮购系统 +物品名称/物品ID/物品链接 +数量’

ITEM_SUBCLASS_ARMOR_XXX常量用于匹配物品模板中的SubClass属性，这是游戏内部定义的物品子类型

LootRollItemType、LootRollArmorType和LootRollWeaponType这些枚举是您自定义的分类系统，用于在军火库系统中对物品进行分类。

数据库中的armorType字段应该对应LootRollArmorType枚举中的值（0-4），而不是ITEM_SUBCLASS_ARMOR_XXX常量。

同样，数据库中的weaponType字段应该对应LootRollWeaponType枚举中的值（0-7），而不是物品模板中的SubClass。

数据库中的itemType字段应该对应LootRollItemType枚举中的值（0-8）。




==================================




.ygbot talent list "列出可用的预设天赋"

.ygbot talent apply <天赋名称> "应用预设天赋"

.ygbot talent reset "重置天赋点"

.ygbot talent link  "应用天赋链接"

.ygbot talent debug  "显示所有预设天赋信息"


.glyph debug          - 显示所有预设雕文信息
.glyph preset <专精索引>  # 应用预设雕文（自动添加雕文）
.glyph clear             # 清除所有雕文
.glyph list              # 列出玩家当前雕文
.glyph use <物品ID> [槽位编号] - 为机器人应用雕文到指定槽位
    - 示例: .glyph apply 43412 0
    - 槽位 0、3、5: 主要雕文槽
    - 槽位 1、2、4: 次要雕文槽



.上线所有小弟 - 上线玩家账号下的所有其他角色
.下线所有小弟 - 下线玩家账号下的所有其他角色 
.上线小弟 <位置(0-9)> - 上线玩家账号下指定位置的角色
.下线小弟 <位置(0-9)> - 下线玩家账号下指定位置的角色  .ygbot logoutallbots - 英文命令
.小弟列表 - 列出玩家账号下的所有角色


机器人管理命令 普通聊天频道输入
onlineguildmember - 上线玩家的公会成员
offlineallbot - 下线所有没有队伍机器人
onlinefriends - 上线玩家好友列表里的所有好友
groupfriend - 加队伍机器人为好友
invitefriend - 邀请所有好友入队
addclassbot [职业ID] - 上线特定职业的机器人（1-战士，2-圣骑，3-猎人，4-盗贼，5-牧师，6-死骑，7-萨满，8-法师，9-术士，11-德鲁伊）
特殊功能命令
resetdungeon - 重置队伍中所有人的副本CD，必须在队伍内才能使用
武器技能 - 更新机器人的武器技能至满级


机器人私聊/队伍聊天的控制命令清单
基础控制命令
攻击 - 命令机器人攻击目标
坦克攻击 - 命令坦克角色攻击目标
近战攻击 - 命令近战职业攻击目标
远程攻击 - 命令远程职业攻击目标
停火 - 命令所有机器人停止攻击
近战停火 - 命令近战职业停止攻击
远程停火 - 命令远程职业停止攻击
位置与阵型命令
散开 - 命令机器人散开站位
驱散 - 命令机器人进行驱散
三角阵型 - 机器人以三角形阵型排列
圆形阵型 - 机器人以圆形阵型排列
远离人群 - 命令机器人远离人群
集合 - 命令所有机器人集合到玩家身边
召唤全员 - 传送所有机器人到玩家位置

机器人私聊命令
当私聊接收到‘退队伍’，退出队伍。
当私聊接收到‘退22战队’，退出竞技场2V2战队。
当私聊接收到’退33战队‘，退出竞技场3V3战队。
当私聊接收到‘退55战队’，退出竞技场5V5战队。
当私聊接收到‘退公会’，退出公会。



===========

.ygbot 寻路导航
.ygbot 记录路径 - 记录玩家当前位置的坐标为路径点
.ygbot 清除路径 [地图ID] 确认 - 清除指定地图的所有路径点数据，需要确认
.ygbot 寻路导航 调试 - 调试模式，查询当前地图的路径点数据，帮助诊断问题
.ygbot 自动记录 [间隔秒数] - 启动自动记录路径点功能，可指定记录间隔，停止记录在命令后面的秒数输入0






setAndGetSmartStatus - 开关机器人走位
startSellGoldMode - 开启拍卖模式
endSellGoldMode - 拍卖模式下，买买买
passSellGoldMode - 拍卖模式下，放弃竞价
setAndGetSmartFindPathStatus - 副本自动寻路开关
查看背包 - 查看机器人背包内容
重置天赋 [0/1/2] - 重置机器人天赋（0、1、2分别对应三个天赋树）
系统命令
.pbot acclogin [角色名] - 登录特定机器人
.pbot acclogout [角色名] - 登出特定机器人
.pbot accloginall - 登录所有机器人
.pbot acclogout - 登出所有机器人
.pbot pvpequip [等级] [天赋] - 为机器人装备PVP装备
聊天命令
/bot 或 /bot roster - 显示/隐藏机器人面板
这些命令可以通过聊天框输入，或者通过插件界面中的按钮点击来执行。插件界面分为几个主要面板：主面板（ActionPanel）、工会操作面板（PbotPanel）、其他操作面板（OtherPanel）和机器人列表面板（BotRoster）。
机器人命令主要通过聊天频道发送，包括普通说话（SAY）、小队（PARTY）和密语（WHISPER）等方式，具体取决于命令的性质和目标机器人。


===========================================================

📁 Faker/
BotEventSystem.h          # 事件系统核心定义
BotEventSystem.cpp        # 事件系统核心实现
BotEventHandlers.h        # 事件处理器定义
BotEventHandlers.cpp      # 事件处理器实现
BotOpcodeHandler.h        # 操作码处理器定义
BotOpcodeHandler.cpp      # 操作码处理器实现
README_EventSystem.md     # 详细使用文档
EventSystem_Summary.md    # 实现总结


📁 FakePlayers/
├── ✅ SmartPlayerManager.h          # 智能玩家管理器头文件
├── ✅ SmartPlayerManager.cpp        # 核心功能实现
├── ✅ SmartPlayerEventListener.cpp  # 事件监听器
├── ✅ SmartBehaviorConfig.h         # 配置管理头文件
├── ✅ SmartBehaviorConfig.cpp       # 配置管理实现
================================================

Faker (事件系统)：

✅ 事件分发 - 接收死亡事件，调用DeathSystem
✅ 系统协调 - 协调各系统更新顺序
✅ 更新管理 - 优先更新DeathSystem

BotDeathHandler (DeathSystem)：

✅ 完整死亡流程 - 已有完整实现
✅ 灵魂释放 - HandleCorpseRelease()
✅ 墓地移动 - HandleGhostMovement()
✅ 1分钟等待 - 60秒跑尸时间
✅ 5%/95%复活 - 灵魂医者/跑尸体

FakePlayers (行为驱动) → Faker (事件系统) → DeathSystem & MovementSystem


MovementSystem/
├── BotMovementConstants.h          # 移动系统常量定义
├── BotTerrainValidator.h           # 地形验证器头文件  
├── BotTerrainValidator.cpp         # 地形验证器实现
├── BotMovementManager.h            # 移动管理器头文件
├── BotMovementManager.cpp          # 移动管理器实现
├── BotMovementTasks.h              # 移动任务头文件
├── BotMovementTasks.cpp            # 移动任务实现
├── MovementSystemScript.h          # 脚本系统集成头文件
├── MovementSystemScript.cpp        # 脚本系统集成实现
├── CMakeLists.txt                  # 编译配置
├── MovementSystemConfig.conf       # 配置文件模板
├── README.md                       # 使用说明
└── ARCHITECTURE.md                 # 架构文档



CombatSystem/
├── Core/                   # 战斗基类
│   ├── BotCombatAI.h/cpp   # 战斗AI基类
│   ├── CombatAction.h/cpp  # 战斗动作基类
│   └── CombatState.h       # 战斗状态定义
├── Scenarios/              # 场景特化
│   ├── FieldCombat/        # 野外战斗
│   ├── GroupCombat/        # 团队战斗
│   └── DuelCombat/         # 决斗战斗
├── Classes/                # 职业特化
│   ├── WarriorCombat/      # 战士战斗
│   ├── PaladinCombat/      # 圣骑士战斗
│   └── ...
└── Utils/                  # 工具模块
    ├── CombatSpells.h/cpp  # 技能管理（集成TalentSystem）
    ├── CombatMovement.h/cpp # 移动管理（集成MovementSystem）
    └── CombatTarget.h/cpp  # 目标管理


=======================================================

第1周：架构重构 1. 清理数据库技能系统残留代码 2. 统一AI决策架构 3. 完善战斗状态管理  
第2-3周：盗贼策略完善 1. 实现完整的盗贼战斗逻辑 2. 连击点和能量管理 3. 潜行和爆发循环  
第4-5周：其他职业策略 1. 法师战斗策略 2. 治疗职业策略 3. 坦克职业策略  
第6-7周：智能决策系统 1. 战术决策引擎 2. 动态策略调整 3. 性能监控  
第8周：团队协作和优化 1. 团队协作系统 2. 场景特化AI 3. 性能优化和测试

就按你上面的建议迁移计划进行：对齐接口，移植各职业的战斗循环，调用我们已有的BotClassSpells与通用TryCastSpell”，保证逻辑等价而结构适配。增加通用施法函数 TryCastSpell（参考mod-pbot-keji，但适配我们PlayerBotAI/m_player） 
逐步补齐基础查询方法（如 RangeEnemyListByTargetIsMe/RangeEnemyListByNonAura 等），先实现最小可用版本，避免职业逻辑空转.先完整移植盗贼机器人的组队AI，因为我们现在的盗贼机器人技能循环逻辑不行。要确保每个职业AI尽量复用我们已有的 Bot{Class}Spells；源逻辑中调用的技能链会映射到我们BotClassSpells里的字段

======================================================

id`  id
  `myclass` 职业1zs 2qs 3lr 4dz 5ms 6dk 7sm 8fs 9ss 11xd， 数字匹配正确的职业
  `talentType` 天赋 0代表第一页天赋,1代表第二页天赋,2代表第三页天赋
  `pose` 姿态
  `spell` 技能id
  `auraSpell` 光环技能id，有的话就使用spell的技能
  `indexNum` 优先级

  `conditionType` 
  `conditionType2`
  `conditionType3`
  `conditionType4`
  `conditionType5`
conditionType 0-5 为条件类型: 
//    0:普通, 无条件就释放 
//    1 : 敌人的hp > xx 
//    2:敌人的hp<xx 
//    3 : 我的hp>xx 
//    4 : 我的hp < xx 
//    5 : 我的能量>xx 
//    6 : 我的能量 < xx
//    7 : 敌人在施法 
//    8 : 随机百分比(概率)
//    9 : 检测到了n码以内, 有多少敌人目标看着我.(dps OT了,或者坦克拉怪)  
//    10 : 附近多少（range）码内的，有多少敌人。如果人数>=conditionValue,就释放某技能
//    11：附近多少码（range）内，没有aura的值是conditionValue的敌人。>=2数量,就开始使用spell的技能
//    12: 目标敌人/自己，是否有值为auraSpell的aura，如果目标没有aura，那么就对目标使用spell。如果我没有aura，就对自己使用spell
//    13: 目标敌人/自己，是否有值为auraSpell的aura，如果目标有aura，那么就使用spell。如果我有aura，就会自己使用spell
//    14: 自己是否有值为auraSpell的aura，如果自己有aura，那么就对目标使用spell。跟27同一组。另外，如果设置conditionType2为28，则一直会重复该技能，不会跳到下一个技能。
//    15：给坦克释放群嘲的。附近10码内，如果有2个以上的怪没有看向坦克，那么坦克就释放群体嘲讽
//    16: 如果怪物的debuff层数，debuff的技能id是auraSpell，小于等于conditionValue，那么就对怪物使用spell技能。
//    17: 如果怪物的debuff层数，debuff的技能id是auraSpell，大于等于conditionValue，那么就对怪物使用spell技能。
//    18: 如果我的buff层数，buff的技能id是auraSpell，小于等于conditionValue，那么就对怪物使用spell技能。
//    19: 如果我的buff层数，buff的技能id是auraSpell，大于等于conditionValue，那么就对怪物使用spell技能。（类似增强萨满的5层漩涡，瞬发闪电箭）
//    20: 如果我选择的目标，他也选中了我。这时候就释放spell技能
//    21: 监控目标的debuff的持续时间，如果小于等于conditionValue，那么就使用spell技能
//    22: 盗贼使用,我的连击点数>=xx,则使用spell技能
//    23: 盗贼使用,我的连击点数<=xx,则使用spell技能
//    24: dk使用,判断目标是否受到了感染，如果是的话，就使用spell技能
//    25: dk使用,判断目标是否受到了感染，如果没有的话，就使用spell技能
//    26: dk使用,判断目标没有受到感染的数量，如果数量大于等于2个，就使用spell技能（用于传染疾病）
//    27: 自己是否有值为auraSpell的aura，如果自己没有aura，那么就对目标使用spell。跟14同一组
//    28：本次技能如果释放不成功，那么就重复尝试该技能，直到成功为止。
//    29：敌人目标附近多少（range）码内，有>=conditionValue数量的怪，就释放spell技能。
//    30：敌人目标是否<=（range）码, 在这个射程内就释放spell技能。
//    31: conditionValue为技能ID,如果玩家有这个技能,就释放spell
//    32: conditionValue为技能ID,如果玩家没有这个技能,就释放spell
//    33: 检测敌人数量是否<=2,是的话, 就取消auraSpell光环. 比如取消猎人的乱射
//    34: 检测范围内(rangeVal码)的敌人,如果带有auraSpell的debuff的数量, 是小于等于conditionValue,那么就对敌人释放spell技能. 比如术士,战斗时候保持3个敌人身上的腐蚀术.注意,该项在conditionValue,rangeVal有效,不要写到conditionValue2,conditionValue3的字段里.
//    35: 对非当前目标的一个敌人,释放spell技能(dot技能). 比如术士在攻击目标A,同时会给目标B一个浩劫buff.注意,该项在auraSpell,conditionValue,rangeVal有效,不要写到conditionValue2,conditionValue3的字段里.
//    36: 如果有buff的队友数量大于等于conditionValue,那么释放spell攻击目标.  
//    37: 如果有buff的队友数量小于等于conditionValue,那么释放spell治疗队友.  
//    38: 如果技能ID是conditionValue的技能处于CD中,那么就释放spell.  比如A技能CD了,就用B技能过渡.


  `conditionValue` 
  `conditionValue2`
  `conditionValue3`
  `conditionValue4`
  `conditionValue5`
// conditionValue 0-5 为条件的值

  `rangeVal` 
  `rangeVal2`
  `rangeVal3` 
  `rangeVal4` 
  `rangeVal5` 
//    rangeVal 0-5 为检测的范围
 
备注: `conditionType`,`conditionValue`,`rangeVal`为一个条件判定组,`conditionType2`,`conditionValue2`,`rangeVal2`为一个条件判定组,`conditionType3`,`conditionValue3`,`rangeVal3`为一个条件判定组,``conditionType4`,`conditionValue4`,`rangeVal4`为一个条件判定组,``conditionType5`,`conditionValue5`,`rangeVal5`为一个条件判定组,

  `minLevel` 最低等级
  `maxLevel` 最高等级
  `isMe` spell的释法是对自己还是对敌人的. 0 : 敌人  1 : 自己
  `isAoe` 是否是aoe技能
  `talk` 机器人说话的文本
  `remark` 注释
  `auraSpellFixed` 0：使用自身等级的aura spell，1：使用固定值的aura spell（比如战士的破甲）
  `checkCaster` 检测施法者是否为我,跟auraSpell有关.0:不检测 1:检测. 比如破甲效果一个人打就行,那么checkCaster为0. 比如2个战士都想打撕裂,那么checkCaster为1.
  `unitFlag` 0:通用，1：针对小怪，2：针对boss
  `healthFlag` 0:默认值 1:直接治疗 1:预读打断的治疗,比如目标hp>=95%,会取消治疗,小于则会继续读完治疗
  `isAllHeal` 0:单体治疗  1:群疗

===============================================



 已禁用的移动功能
1. 随机移动逻辑
✅ BotBehaviorEngine.cpp: 注释掉所有行为引擎的移动逻辑
SimulatePlayerBehavior - 40%概率随机移动
ExecuteIdleBehavior - 空闲状态随机移动
ExecuteExploringBehavior - 探索模式移动
✅ Faker.cpp: 注释掉登录后的随机移动
机器人登录后不再自动触发随机移动
✅ BotEventHandlers.cpp: 注释掉事件驱动的移动
TriggerRandomMovement - 随机移动触发
MoveToRandomLocation - 随机位置移动
2. 组队跟随逻辑
✅ BotControlCommands.cpp: 注释掉组队跟随系统
UpdateBotGroupFollow - 整个组队跟随方法
OnUpdate - 跟随状态更新调用

请使用Claude 3.7 Sonnet模型进行修复构建