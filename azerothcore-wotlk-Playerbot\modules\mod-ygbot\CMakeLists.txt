# mod-ygbot CMakeLists.txt

# 添加实际存在的脚本文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/FakePlayers/BotControlCommands.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/FakePlayers/FakePlayers.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/FakePlayers/BotAutoLearnSpells.cpp")

# Faker 相关文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/Faker.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotOpcodeHandler.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotInteractionOpcodeHandler.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotDuelPlayerScript.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotEventSystem.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotEventHandlers.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/Faker/BotBehaviorEngine.cpp")

# 天赋系统文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/YGbotTalentManager.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/YGbotTalentCommand.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/TalentAIIntegration.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/YGbotGlyphManager.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/YGbotGlyphCommands.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/TalentSystem/BotAutoTalentGlyph.cpp")

# 死亡系统文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/DeathSystem/BotDeathHandler.cpp")

# 移动系统文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/MovementSystem/BotTerrainValidator.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/MovementSystem/BotMovementManager.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/MovementSystem/BotMovementTasks.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/MovementSystem/MovementSystemScript.cpp")

# 战斗系统文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotCombatAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotSpellManager.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotTargetManager.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotCombatMovement.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotCombatStrategy.cpp")

# 场景AI实现文件
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotFieldAI/BotFieldAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotFieldAI/FieldMageAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotFieldAI/FieldPaladinAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotFieldAI/FieldWarriorAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotGroupAI/BotGroupAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotDuelAI/BotDuelAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotBGAI/BotBGAI.cpp")
AC_ADD_SCRIPT("${CMAKE_CURRENT_LIST_DIR}/src/CombatSystem/BotArenaAI/BotArenaAI.cpp")

# 添加脚本加载器
AC_ADD_SCRIPT_LOADER("mod_ygbot" "${CMAKE_CURRENT_LIST_DIR}/src/loader.h")

# 添加配置文件
AC_ADD_CONFIG_FILE("${CMAKE_CURRENT_LIST_DIR}/conf/mod_ygbot.conf.dist")
