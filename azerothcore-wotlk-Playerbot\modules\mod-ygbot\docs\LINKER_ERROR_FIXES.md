# 链接错误修复

## 问题描述

编译成功但链接时出现了4个无法解析的外部符号错误，这些都是在BotSpellManager类中声明但未实现的私有方法。

## 错误列表

### 1. CheckSpellPrerequisites
```
无法解析的外部符号 "private: bool __cdecl BotSpellManager::CheckSpellPrerequisites(struct BotSpellInfo const &,class Unit *)const"
```

### 2. SelectBestSpell
```
无法解析的外部符号 "private: unsigned int __cdecl BotSpellManager::SelectBestSpell(enum BotCombatState,class Unit *)const"
```

### 3. LogDebug
```
无法解析的外部符号 "private: void __cdecl BotSpellManager::LogDebug(class std::basic_string<char,struct std::char_traits<char>,class std::allocator<char> > const &)const"
```

### 4. UpdateSpellStats
```
无法解析的外部符号 "private: void __cdecl BotSpellManager::UpdateSpellStats(unsigned int,bool)"
```

## 解决方案

在 `BotSpellManager.cpp` 中添加了所有缺失的方法实现：

### 1. CheckSpellPrerequisites 实现
```cpp
bool BotSpellManager::CheckSpellPrerequisites(const BotSpellInfo& spellInfo, Unit* target) const
{
    // 检查技能是否学会
    if (!m_bot->HasSpell(spellInfo.spellId))
        return false;
    
    // 检查冷却时间
    if (m_bot->HasSpellCooldown(spellInfo.spellId))
        return false;
    
    // 检查法力消耗
    if (spellInfo.manaCost > 0 && m_bot->GetPower(POWER_MANA) < spellInfo.manaCost)
        return false;
    
    // 检查目标要求和距离
    // ...
    
    return true;
}
```

### 2. SelectBestSpell 实现
```cpp
uint32 BotSpellManager::SelectBestSpell(BotCombatState combatState, Unit* target) const
{
    // 根据战斗状态选择合适的技能类型
    std::vector<uint32> availableSpells;
    
    switch (combatState)
    {
        case BotCombatState::FIGHTING:
            availableSpells = GetAvailableDamageSpells(target);
            break;
        case BotCombatState::HEALING:
            availableSpells = GetAvailableHealSpells(target);
            break;
        // ...
    }
    
    // 选择优先级最高的技能
    return bestSpell;
}
```

### 3. UpdateSpellStats 实现
```cpp
void BotSpellManager::UpdateSpellStats(uint32 spellId, bool success)
{
    auto it = m_spells.find(spellId);
    if (it != m_spells.end())
    {
        BotSpellInfo& spellInfo = it->second;
        spellInfo.useCount++;
        spellInfo.lastUsed = static_cast<uint32>(time(nullptr) * 1000);
        
        if (success)
        {
            spellInfo.successCount++;
        }
        
        // 更新冷却时间
        // ...
    }
}
```

### 4. LogDebug 实现
```cpp
void BotSpellManager::LogDebug(const std::string& message) const
{
    if (m_debugMode && m_bot)
    {
        LOG_DEBUG("server", "BotSpellManager[{}]: {}", m_bot->GetName(), message);
    }
}
```

## 额外添加的方法

在实现SelectBestSpell时发现还需要一些查询方法，也一并实现了：

### 查询接口实现
```cpp
std::vector<uint32> GetAvailableDamageSpells(Unit* target = nullptr) const;
std::vector<uint32> GetAvailableHealSpells(Unit* target = nullptr) const;
std::vector<uint32> GetAvailableBuffSpells(Unit* target = nullptr) const;
std::vector<uint32> GetAvailableCCSpells(Unit* target = nullptr) const;
bool HasEmergencySpells() const;
float GetManaCostRate() const;
```

## 实现特点

### 1. CheckSpellPrerequisites
- 检查技能是否学会
- 检查冷却时间
- 检查资源消耗（法力值）
- 检查目标要求
- 检查施法距离

### 2. SelectBestSpell
- 根据战斗状态选择技能类型
- 按优先级排序选择最佳技能
- 支持伤害、治疗、增益等不同类型

### 3. UpdateSpellStats
- 更新技能使用统计
- 记录成功/失败次数
- 更新最后使用时间
- 更新冷却时间信息

### 4. 查询方法
- 按技能类型过滤可用技能
- 检查技能前置条件
- 支持目标参数

## 设计考虑

### 性能优化
- 使用引用避免不必要的拷贝
- 合理的条件检查顺序
- 避免重复计算

### 错误处理
- 空指针检查
- 边界条件处理
- 异常情况的默认返回值

### 扩展性
- 模块化的技能类型检查
- 可配置的优先级系统
- 灵活的统计信息收集

## 验证清单

- [x] 添加了所有缺失的私有方法实现
- [x] 实现了相关的查询接口方法
- [x] 添加了适当的错误检查
- [x] 保持了与头文件声明的一致性
- [x] 使用了正确的参数类型和返回类型

## 预期结果

修复后应该解决所有链接错误：
- ✅ LNK2019 CheckSpellPrerequisites 无法解析
- ✅ LNK2019 SelectBestSpell 无法解析  
- ✅ LNK2019 LogDebug 无法解析
- ✅ LNK2019 UpdateSpellStats 无法解析
- ✅ LNK1120 4个无法解析的外部命令

现在BotSpellManager应该能够成功链接并正常工作。
