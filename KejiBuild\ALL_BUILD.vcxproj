﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8F1E685C-2E64-315B-9FB9-DA833E8BA422}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/keji/azerothcore-pbot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/keji/azerothcore-pbot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/keji/azerothcore-pbot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/keji/azerothcore-pbot/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SD:/keji/azerothcore-pbot -BD:/keji/KejiBuild --check-stamp-file D:/keji/KejiBuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceRuns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-C.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeCXXCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeRCCompiler.cmake;D:\keji\KejiBuild\CMakeFiles\3.31.3\CMakeSystem.cmake;D:\keji\azerothcore-pbot\conf\dist\config.cmake;D:\keji\azerothcore-pbot\deps\acore\cmake-utils\utils.cmake;D:\keji\azerothcore-pbot\src\cmake\ac_macros.cmake;D:\keji\azerothcore-pbot\src\cmake\compiler\msvc\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\genrev.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\AutoCollect.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\CheckPlatform.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigInstall.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureApplications.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureBaseTargets.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureModules.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureScripts.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\ConfigureTools.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\EnsureVersion.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindGit.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindMySQL.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\FindPCHSupport.cmake;D:\keji\azerothcore-pbot\src\cmake\macros\GroupSources.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\after_platform.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\platform\win\settings.cmake;D:\keji\azerothcore-pbot\src\cmake\showoptions.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\keji\KejiBuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\keji\KejiBuild\ZERO_CHECK.vcxproj">
      <Project>{2B34230A-489D-329E-A203-4300066A20FA}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Detour\Detour.vcxproj">
      <Project>{7762352D-F6A8-3C78-B40C-F6D9EC6696BB}</Project>
      <Name>Detour</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\recastnavigation\Recast\Recast.vcxproj">
      <Project>{093B2F5E-F2B0-3957-9C82-D27C95134D48}</Project>
      <Name>Recast</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\argon2\argon2.vcxproj">
      <Project>{0193C8EB-A537-3520-85BF-0E144631F6EE}</Project>
      <Name>argon2</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\apps\authserver.vcxproj">
      <Project>{4D5A4186-9584-3D4A-B5DD-9E16F212171F}</Project>
      <Name>authserver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\common\common.vcxproj">
      <Project>{C1B3B1F1-588C-3E0B-8C28-A2834C66B5BA}</Project>
      <Name>common</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\database\database.vcxproj">
      <Project>{91634308-AE96-3E40-B8FC-644394DD71B3}</Project>
      <Name>database</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\fmt\fmt.vcxproj">
      <Project>{7360C5EE-63A8-3467-9347-9D01A58DD42F}</Project>
      <Name>fmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\g3dlite\g3dlib.vcxproj">
      <Project>{81F9C921-10D9-36E3-88FE-F780EF060AE7}</Project>
      <Name>g3dlib</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\game\game.vcxproj">
      <Project>{32663C99-0698-35F6-82F9-57D2B46880A2}</Project>
      <Name>game</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\gsoap\gsoap.vcxproj">
      <Project>{86CE857F-1FBF-3B37-A37A-5CDB7D36B2A4}</Project>
      <Name>gsoap</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\modules\modules.vcxproj">
      <Project>{00F9C9A8-D330-3B91-8E11-D7911F4153DB}</Project>
      <Name>modules</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\genrev\revision.h.vcxproj">
      <Project>{001DE7BF-7FB1-3018-AAEA-7D06F0FC7440}</Project>
      <Name>revision.h</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\scripts\scripts.vcxproj">
      <Project>{4D7D21A0-0841-3F61-899D-42DA6DF22DA0}</Project>
      <Name>scripts</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\SFMT\sfmt.vcxproj">
      <Project>{125ADA47-4D19-3387-AB3D-C8276D002DDC}</Project>
      <Name>sfmt</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\shared\shared.vcxproj">
      <Project>{5792A934-83A4-3A5A-B22B-41461D3B9094}</Project>
      <Name>shared</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\src\server\apps\worldserver.vcxproj">
      <Project>{34C3F4A2-CDCC-3518-A976-66A176E510A7}</Project>
      <Name>worldserver</Name>
    </ProjectReference>
    <ProjectReference Include="D:\keji\KejiBuild\deps\zlib\zlib.vcxproj">
      <Project>{792C559D-6BD0-3B63-9273-7B424A678DC9}</Project>
      <Name>zlib</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>