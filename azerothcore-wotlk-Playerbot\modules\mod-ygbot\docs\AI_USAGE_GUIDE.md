# 分层AI系统使用指南

## 快速开始

### 1. 系统初始化
在服务器启动时，AI系统会自动初始化：
```cpp
// 在适当的地方调用（通常在模块加载时）
BotAIInitializer::InitializeAISystem();
```

### 2. 创建机器人AI
当创建机器人战斗AI时，系统会自动选择合适的AI：
```cpp
// 在BotCombatAI::Initialize()中自动调用
m_aiManager = sBotAIMgr->CreateAIForBot(m_bot, this);
```

### 3. 验证AI工作状态
检查日志输出确认AI系统正常工作：
```
BotAIInitializer: 分层AI系统初始化完成
BotCombatAI: 机器人 哀木替 分层AI系统初始化成功
WorldPvEAI: 为机器人 哀木替 创建了 4 个AI层级
WarriorAI: 为战士 哀木替 创建了 3 个AI层级，特化: 0
```

## 当前支持的功能

### 场景支持
- ✅ **野外PvE**: 完全实现，包括威胁分析、目标选择、战术决策
- 🚧 **战场**: 计划中
- 🚧 **竞技场**: 计划中
- 🚧 **决斗**: 计划中
- 🚧 **副本**: 计划中
- 🚧 **团队副本**: 计划中

### 职业支持
- ✅ **战士**: 完全实现
  - ✅ 武器战: 致死打击轮换
  - ✅ 狂暴战: 嗜血+旋风斩轮换
  - ✅ 防护战: 盾牌猛击轮换
- 🚧 **其他职业**: 计划中

## 战士AI使用说明

### 武器战 (Arms Warrior)
**自动检测条件**: 单手武器 + 非盾牌副手

**技能优先级**:
1. **斩杀** (目标血量 ≤ 20%) - 最高优先级
2. **致死打击** - 主要输出技能
3. **压制** - 目标闪避后可用
4. **撕裂** - DoT效果，目标无此效果时施放
5. **英勇打击** - 基础攻击技能

**战术特点**:
- 单目标高伤害输出
- 注重爆发和控制
- 适合野外PvE和PvP

### 狂暴战 (Fury Warrior)
**自动检测条件**: 双持武器（主手+副手都是武器）

**技能优先级**:
1. **嗜血** - 主要输出+自我治疗
2. **旋风斩** - 群体伤害
3. **斩杀** (目标血量 ≤ 20%) - 终结技能

**战术特点**:
- 持续高输出
- 群体作战能力
- 自我恢复能力

### 防护战 (Protection Warrior)
**自动检测条件**: 装备盾牌

**技能优先级**:
1. **盾牌猛击** - 高威胁+伤害
2. **复仇** - 反击技能
3. **毁灭打击** - 威胁+减甲效果

**战术特点**:
- 坦克定位
- 威胁生成
- 生存能力强

## AI决策示例

### 野外PvE战斗流程

#### 1. 战略层决策
```
分析威胁等级 → 选择主要目标 → 确定交战策略
```

#### 2. 战术层决策
```
管理怒气值 → 选择战斗姿态 → 使用增益技能
```

#### 3. 操作层决策
```
执行技能轮换 → 控制移动 → 切换目标
```

#### 4. 反应层决策
```
处理低血量 → 打断施法 → 使用紧急技能
```

### 实际战斗日志示例
```
WorldPvEStrategicAI: 分析威胁等级 - 威胁等级较低，可以主动交战
WorldPvEStrategicAI: 选择主要目标 - 切换到优先级更高的目标: 老杂斑野猪
WarriorOperationalAI: 战士 哀木替 使用技能 47486 攻击 老杂斑野猪
WarriorOperationalAI: 战士 哀木替 使用技能 47465 攻击 老杂斑野猪
```

## 配置选项

### 启用/禁用新AI系统
在`BotCombatAI.h`中修改：
```cpp
bool m_useNewAI = true;  // true=使用新AI，false=使用传统AI
```

### 调试模式
在`BotCombatAI.h`中修改：
```cpp
bool m_debugMode = true;  // 启用详细调试日志
```

### 层级优先级调整
在各AI层级类中修改`GetPriority()`方法：
```cpp
uint32 GetPriority() const override { 
    return 1200;  // 数值越高优先级越高
}
```

## 故障排除

### 常见问题

#### 1. AI系统未初始化
**症状**: 机器人使用传统AI，日志显示"分层AI系统初始化失败"
**解决**: 确保在服务器启动时调用了`BotAIInitializer::InitializeAISystem()`

#### 2. 机器人不使用技能
**症状**: 机器人只进行普通攻击，不使用职业技能
**检查**:
- 机器人是否学会了相关技能
- 机器人是否有足够的怒气/法力
- 技能是否在冷却中

#### 3. AI决策冲突
**症状**: 机器人行为不一致，频繁切换动作
**解决**: 检查各AI层级的优先级设置，确保决策权重合理

### 调试方法

#### 1. 启用详细日志
```cpp
m_debugMode = true;
```

#### 2. 检查AI层级状态
```cpp
// 在BotCombatAI::Update()中添加
LOG_DEBUG("server", "AI层级数量: {}", m_aiManager ? m_aiManager->GetLayerCount() : 0);
```

#### 3. 监控决策过程
```cpp
// 在AILayerManager::ExecuteBestDecision()中添加
LOG_DEBUG("server", "可用决策数量: {}, 最佳决策: {}", decisions.size(), bestDecision.reason);
```

## 性能监控

### 关键指标
- **AI更新频率**: 各层级的更新间隔
- **决策生成时间**: 从分析到决策的耗时
- **决策执行成功率**: 成功执行的决策比例
- **内存使用**: AI对象的内存占用

### 性能优化建议
1. **合理设置更新频率**: 避免过于频繁的更新
2. **优化决策算法**: 减少复杂计算
3. **及时清理资源**: 避免内存泄漏
4. **使用对象池**: 重用AI对象

## 扩展开发

### 添加新职业AI
1. 创建职业AI头文件和实现文件
2. 定义职业特有的技能ID
3. 实现技能轮换逻辑
4. 在`BotAIInitializer`中注册

### 添加新场景AI
1. 创建场景AI头文件和实现文件
2. 实现场景检测逻辑
3. 定义场景特有的AI层级
4. 在`BotAIInitializer`中注册

### 自定义AI层级
1. 继承`IAILayer`接口
2. 实现决策生成和执行逻辑
3. 设置合适的优先级和更新频率
4. 在相应的AI中添加该层级

## 最佳实践

### 1. AI设计原则
- **单一职责**: 每个AI层级专注于特定任务
- **松耦合**: 层级之间通过决策系统通信
- **可扩展**: 易于添加新的AI层级和逻辑

### 2. 性能考虑
- **避免过度计算**: 缓存计算结果
- **合理的更新频率**: 根据重要性设置更新间隔
- **异常处理**: 确保AI异常不影响游戏稳定性

### 3. 调试技巧
- **分层调试**: 逐层检查AI决策
- **日志记录**: 记录关键决策过程
- **性能监控**: 监控AI系统性能指标

## 总结

分层AI系统为机器人提供了智能的战斗决策能力，当前已实现野外PvE场景和战士职业的完整支持。通过合理配置和使用，可以显著提升机器人的战斗表现。

随着系统的不断完善，将支持更多职业和场景，为玩家提供更好的机器人体验。
