﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\authserver.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Main.cpp">
      <Filter>authserver</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Authentication\AuthCodes.cpp">
      <Filter>authserver\Authentication</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Server\AuthSession.cpp">
      <Filter>authserver\Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\common\Debugging\WheatyExceptionReport.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\resource.h">
      <Filter>authserver</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Authentication\AuthCodes.h">
      <Filter>authserver\Authentication</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Server\AuthSession.h">
      <Filter>authserver\Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\Server\AuthSocketMgr.h">
      <Filter>authserver\Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\common\Debugging\WheatyExceptionReport.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\authserver.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\authserver.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\authserver.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\apps\CMakeFiles\authserver.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\apps\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="D:\keji\azerothcore-pbot\src\server\apps\authserver\authserver.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{EC811719-652E-3DD8-9E1B-41B59DBB03FE}</UniqueIdentifier>
    </Filter>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{90DBB2E8-B7B8-3A5B-B6BF-533AA6796052}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{13BB9762-CF46-3603-980C-A0F8777D13B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="authserver">
      <UniqueIdentifier>{8360C851-DFA0-321D-82FC-E3899D6117C2}</UniqueIdentifier>
    </Filter>
    <Filter Include="authserver\Authentication">
      <UniqueIdentifier>{8E06C4CF-50D7-3D63-AE1C-FA843B900F66}</UniqueIdentifier>
    </Filter>
    <Filter Include="authserver\Server">
      <UniqueIdentifier>{0664446B-94DA-3765-94BA-64C66853AB3F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
