﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\cmake_pch.cxx">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAI.cpp">
      <Filter>AI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIRegistry.cpp">
      <Filter>AI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAISelector.cpp">
      <Filter>AI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\enuminfo_CreatureAI.cpp">
      <Filter>AI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\CombatAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GameObjectAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GuardAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PassiveAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PetAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\ReactorAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\TotemAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\UnitAI.cpp">
      <Filter>AI\CoreAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedCreature.cpp">
      <Filter>AI\ScriptedAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedEscortAI.cpp">
      <Filter>AI\ScriptedAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedFollowerAI.cpp">
      <Filter>AI\ScriptedAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedGossip.cpp">
      <Filter>AI\ScriptedAI</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartAI.cpp">
      <Filter>AI\SmartScripts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScript.cpp">
      <Filter>AI\SmartScripts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScriptMgr.cpp">
      <Filter>AI\SmartScripts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Accounts\AccountMgr.cpp">
      <Filter>Accounts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Achievements\AchievementMgr.cpp">
      <Filter>Achievements</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Addons\AddonMgr.cpp">
      <Filter>Addons</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator\ArenaSpectator.cpp">
      <Filter>ArenaSpectator</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseMgr.cpp">
      <Filter>AuctionHouse</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseSearcher.cpp">
      <Filter>AuctionHouse</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Autobroadcast\AutobroadcastMgr.cpp">
      <Filter>Autobroadcast</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Battlefield.cpp">
      <Filter>Battlefield</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldHandler.cpp">
      <Filter>Battlefield</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldMgr.cpp">
      <Filter>Battlefield</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones\BattlefieldWG.cpp">
      <Filter>Battlefield\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Arena.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeam.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeamMgr.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Battleground.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundMgr.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundQueue.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundSpamProtect.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundUtils.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\enuminfo_ArenaTeam.cpp">
      <Filter>Battlegrounds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonMgr.cpp">
      <Filter>Battlegrounds\ArenaSeason</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonRewardsDistributor.cpp">
      <Filter>Battlegrounds\ArenaSeason</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAB.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAV.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundBE.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundDS.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundEY.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundIC.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundNA.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRL.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRV.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundSA.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundWS.cpp">
      <Filter>Battlegrounds\Zones</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Cache\CharacterCache.cpp">
      <Filter>Cache</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Cache\WhoListCacheMgr.cpp">
      <Filter>Cache</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Calendar\CalendarMgr.cpp">
      <Filter>Calendar</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Chat.cpp">
      <Filter>Chat</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\HyperlinkTags.cpp">
      <Filter>Chat</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Hyperlinks.cpp">
      <Filter>Chat</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\Channel.cpp">
      <Filter>Chat\Channels</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\ChannelMgr.cpp">
      <Filter>Chat\Channels</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\enuminfo_Channel.cpp">
      <Filter>Chat\Channels</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommand.cpp">
      <Filter>Chat\ChatCommands</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandArgs.cpp">
      <Filter>Chat\ChatCommands</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandHelpers.cpp">
      <Filter>Chat\ChatCommands</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandTags.cpp">
      <Filter>Chat\ChatCommands</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Combat\HostileRefMgr.cpp">
      <Filter>Combat</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Combat\ThreatMgr.cpp">
      <Filter>Combat</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\ConditionMgr.cpp">
      <Filter>Conditions</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\DisableMgr.cpp">
      <Filter>Conditions</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\DBCStores.cpp">
      <Filter>DataStores</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Stores.cpp">
      <Filter>DataStores</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFG.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGGroupData.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGMgr.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGPlayerData.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGQueue.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGScripts.cpp">
      <Filter>DungeonFinding</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse\Corpse.cpp">
      <Filter>Entities\Corpse</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\Creature.cpp">
      <Filter>Entities\Creature</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureGroups.cpp">
      <Filter>Entities\Creature</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\GossipDef.cpp">
      <Filter>Entities\Creature</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\TemporarySummon.cpp">
      <Filter>Entities\Creature</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\enuminfo_CreatureData.cpp">
      <Filter>Entities\Creature</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject\DynamicObject.cpp">
      <Filter>Entities\DynamicObject</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObject.cpp">
      <Filter>Entities\GameObject</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Item.cpp">
      <Filter>Entities\Item</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemEnchantmentMgr.cpp">
      <Filter>Entities\Item</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\enuminfo_Item.cpp">
      <Filter>Entities\Item</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container\Bag.cpp">
      <Filter>Entities\Item\Container</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Object.cpp">
      <Filter>Entities\Object</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectGuid.cpp">
      <Filter>Entities\Object</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectPosSelector.cpp">
      <Filter>Entities\Object</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Position.cpp">
      <Filter>Entities\Object</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateData.cpp">
      <Filter>Entities\Object\Updates</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFieldFlags.cpp">
      <Filter>Entities\Object\Updates</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\Pet.cpp">
      <Filter>Entities\Pet</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\CinematicMgr.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\KillRewarder.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\Player.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerGossip.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerMisc.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerQuest.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerSettings.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerStorage.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerTaxi.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerUpdates.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\SocialMgr.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\TradeData.cpp">
      <Filter>Entities\Player</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Totem\Totem.cpp">
      <Filter>Entities\Totem</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Transport\Transport.cpp">
      <Filter>Entities\Transport</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\CharmInfo.cpp">
      <Filter>Entities\Unit</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\StatSystem.cpp">
      <Filter>Entities\Unit</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\Unit.cpp">
      <Filter>Entities\Unit</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\enuminfo_Unit.cpp">
      <Filter>Entities\Unit</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\Vehicle.cpp">
      <Filter>Entities\Vehicle</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Events\GameEventMgr.cpp">
      <Filter>Events</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectAccessor.cpp">
      <Filter>Globals</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectMgr.cpp">
      <Filter>Globals</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Globals\WorldGlobals.cpp">
      <Filter>Globals</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridObjectLoader.cpp">
      <Filter>Grids</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainData.cpp">
      <Filter>Grids</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainLoader.cpp">
      <Filter>Grids</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGridManager.cpp">
      <Filter>Grids</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiers.cpp">
      <Filter>Grids\Notifiers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\Group.cpp">
      <Filter>Groups</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupMgr.cpp">
      <Filter>Groups</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupReference.cpp">
      <Filter>Groups</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\Guild.cpp">
      <Filter>Guilds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\GuildMgr.cpp">
      <Filter>Guilds</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AddonHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ArenaTeamHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AuctionHouseHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AuthHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\BankHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\BattleGroundHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CalendarHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ChannelHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CharacterHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ChatHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\CombatHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\DuelHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\GroupHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\GuildHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ItemHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\LFGHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\LootHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MailHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MiscHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\MovementHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\NPCHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\PetHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\PetitionsHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\QueryHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\QuestHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\ReferAFriendHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\SkillHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\Socialhandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\SpellHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TaxiHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TicketHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\TradeHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\VehicleHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\VoiceChatHandler.cpp">
      <Filter>Handlers</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceSaveMgr.cpp">
      <Filter>Instances</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceScript.cpp">
      <Filter>Instances</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootItemStorage.cpp">
      <Filter>Loot</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootMgr.cpp">
      <Filter>Loot</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Mails\Mail.cpp">
      <Filter>Mails</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Mails\ServerMailMgr.cpp">
      <Filter>Mails</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaBoundary.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\Map.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapInstanced.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapMgr.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapUpdater.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Maps\TransportMgr.cpp">
      <Filter>Maps</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\BanMgr.cpp">
      <Filter>Misc</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\DynamicVisibility.cpp">
      <Filter>Misc</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Misc\GameGraveyard.cpp">
      <Filter>Misc</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Formulas.cpp">
      <Filter>Miscellaneous</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Modules\ModuleMgr.cpp">
      <Filter>Modules</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Motd\MotdMgr.cpp">
      <Filter>Motd</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerReference.cpp">
      <Filter>Movement</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MotionMaster.cpp">
      <Filter>Movement</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerator.cpp">
      <Filter>Movement</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\ConfusedMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\EscortMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\FleeingMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\HomeMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\IdleMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PathGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PointMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\RandomMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\TargetedMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\WaypointMovementGenerator.cpp">
      <Filter>Movement\MovementGenerators</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSpline.cpp">
      <Filter>Movement\Spline</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInit.cpp">
      <Filter>Movement\Spline</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementPacketBuilder.cpp">
      <Filter>Movement\Spline</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementUtil.cpp">
      <Filter>Movement\Spline</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\Spline.cpp">
      <Filter>Movement\Spline</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints\WaypointMgr.cpp">
      <Filter>Movement\Waypoints</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvP.cpp">
      <Filter>OutdoorPvP</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvPMgr.cpp">
      <Filter>OutdoorPvP</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Petitions\PetitionMgr.cpp">
      <Filter>Petitions</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Pools\PoolMgr.cpp">
      <Filter>Pools</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Quests\QuestDef.cpp">
      <Filter>Quests</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Quests\enuminfo_QuestDef.cpp">
      <Filter>Quests</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Reputation\ReputationMgr.cpp">
      <Filter>Reputation</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\MapScripts.cpp">
      <Filter>Scripting</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgr.cpp">
      <Filter>Scripting</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObject.cpp">
      <Filter>Scripting</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptSystem.cpp">
      <Filter>Scripting</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AccountScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementCriteriaScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllBattlegroundScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCommandScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCreatureScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllGameObjectScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllItemScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllMapScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllSpellScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AreaTriggerScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaTeamScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AuctionHouseScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundMapScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CommandScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ConditionScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CreatureScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DatabaseScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DynamicObjectScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ElunaScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\FormulaScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameEventScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameObjectScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GlobalScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GroupScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GuildScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\InstanceMapScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\LootScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MailScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MiscScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ModuleScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MovementHandlerScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\OutdoorPvPScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PetScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerbotsScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ServerScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\SpellScriptLoader.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TicketScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TransportScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\UnitScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\VehicleScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WeatherScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldMapScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldObjectScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldScript.cpp">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packet.cpp">
      <Filter>Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSession.cpp">
      <Filter>Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSessionMgr.cpp">
      <Filter>Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocket.cpp">
      <Filter>Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocketMgr.cpp">
      <Filter>Server</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\BankPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CharacterPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\ChatPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatLogPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\GuildPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\LFGPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\MiscPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PacketUtilities.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PetPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\TotemPackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\WorldStatePackets.cpp">
      <Filter>Server\Packets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\Opcodes.cpp">
      <Filter>Server\Protocol</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\PacketLog.cpp">
      <Filter>Server\Protocol</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillDiscovery.cpp">
      <Filter>Skills</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillExtraItems.cpp">
      <Filter>Skills</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Spell.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellEffects.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfo.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfoCorrections.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellMgr.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellScript.cpp">
      <Filter>Spells</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraEffects.cpp">
      <Filter>Spells\Auras</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuras.cpp">
      <Filter>Spells\Auras</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Texts\ChatTextBuilder.cpp">
      <Filter>Texts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Texts\CreatureTextMgr.cpp">
      <Filter>Texts</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tickets\TicketMgr.cpp">
      <Filter>Tickets</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Time\GameTime.cpp">
      <Filter>Time</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Time\UpdateTime.cpp">
      <Filter>Time</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tools\CharacterDatabaseCleaner.cpp">
      <Filter>Tools</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Tools\PlayerDump.cpp">
      <Filter>Tools</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Warden.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenCheckMgr.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenMac.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenPayloadMgr.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenWin.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Warden\enuminfo_WardenCheckMgr.cpp">
      <Filter>Warden</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Weather\Weather.cpp">
      <Filter>Weather</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\Weather\WeatherMgr.cpp">
      <Filter>Weather</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\World.cpp">
      <Filter>World</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldConfig.cpp">
      <Filter>World</Filter>
    </ClCompile>
    <ClCompile Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldState.cpp">
      <Filter>World</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAI.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIFactory.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIImpl.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAIRegistry.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CreatureAISelector.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\GameObjectAIFactory.h">
      <Filter>AI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\CombatAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GameObjectAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\GuardAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PassiveAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\PetAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\ReactorAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\TotemAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\CoreAI\UnitAI.h">
      <Filter>AI\CoreAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedCreature.h">
      <Filter>AI\ScriptedAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedEscortAI.h">
      <Filter>AI\ScriptedAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedFollowerAI.h">
      <Filter>AI\ScriptedAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\ScriptedAI\ScriptedGossip.h">
      <Filter>AI\ScriptedAI</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartAI.h">
      <Filter>AI\SmartScripts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScript.h">
      <Filter>AI\SmartScripts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AI\SmartScripts\SmartScriptMgr.h">
      <Filter>AI\SmartScripts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Accounts\AccountMgr.h">
      <Filter>Accounts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Achievements\AchievementMgr.h">
      <Filter>Achievements</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Addons\AddonMgr.h">
      <Filter>Addons</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\ArenaSpectator\ArenaSpectator.h">
      <Filter>ArenaSpectator</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseMgr.h">
      <Filter>AuctionHouse</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\AuctionHouse\AuctionHouseSearcher.h">
      <Filter>AuctionHouse</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Autobroadcast\AutobroadcastMgr.h">
      <Filter>Autobroadcast</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Battlefield.h">
      <Filter>Battlefield</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\BattlefieldMgr.h">
      <Filter>Battlefield</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlefield\Zones\BattlefieldWG.h">
      <Filter>Battlefield\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Arena.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaScore.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeam.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaTeamMgr.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Battleground.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundMgr.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundQueue.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundScore.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundSpamProtect.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\BattlegroundUtils.h">
      <Filter>Battlegrounds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonMgr.h">
      <Filter>Battlegrounds\ArenaSeason</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaSeasonRewardsDistributor.h">
      <Filter>Battlegrounds\ArenaSeason</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\ArenaSeason\ArenaTeamFilter.h">
      <Filter>Battlegrounds\ArenaSeason</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAB.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundAV.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundBE.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundDS.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundEY.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundIC.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundNA.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRL.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundRV.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundSA.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Battlegrounds\Zones\BattlegroundWS.h">
      <Filter>Battlegrounds\Zones</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Cache\CharacterCache.h">
      <Filter>Cache</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Cache\WhoListCacheMgr.h">
      <Filter>Cache</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Calendar\CalendarMgr.h">
      <Filter>Calendar</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Chat.h">
      <Filter>Chat</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Hyperlinks.h">
      <Filter>Chat</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\Channel.h">
      <Filter>Chat\Channels</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\Channels\ChannelMgr.h">
      <Filter>Chat\Channels</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommand.h">
      <Filter>Chat\ChatCommands</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandArgs.h">
      <Filter>Chat\ChatCommands</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandHelpers.h">
      <Filter>Chat\ChatCommands</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Chat\ChatCommands\ChatCommandTags.h">
      <Filter>Chat\ChatCommands</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\HostileRefMgr.h">
      <Filter>Combat</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\ThreatMgr.h">
      <Filter>Combat</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Combat\UnitEvents.h">
      <Filter>Combat</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\ConditionMgr.h">
      <Filter>Conditions</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Conditions\DisableMgr.h">
      <Filter>Conditions</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\DBCStores.h">
      <Filter>DataStores</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Stores.h">
      <Filter>DataStores</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DataStores\M2Structure.h">
      <Filter>DataStores</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFG.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGGroupData.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGMgr.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGPlayerData.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGQueue.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\DungeonFinding\LFGScripts.h">
      <Filter>DungeonFinding</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Corpse\Corpse.h">
      <Filter>Entities\Corpse</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\Creature.h">
      <Filter>Entities\Creature</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureData.h">
      <Filter>Entities\Creature</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\CreatureGroups.h">
      <Filter>Entities\Creature</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\GossipDef.h">
      <Filter>Entities\Creature</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Creature\TemporarySummon.h">
      <Filter>Entities\Creature</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\DynamicObject\DynamicObject.h">
      <Filter>Entities\DynamicObject</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObject.h">
      <Filter>Entities\GameObject</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\GameObject\GameObjectData.h">
      <Filter>Entities\GameObject</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Item.h">
      <Filter>Entities\Item</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemEnchantmentMgr.h">
      <Filter>Entities\Item</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\ItemTemplate.h">
      <Filter>Entities\Item</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Item\Container\Bag.h">
      <Filter>Entities\Item\Container</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Object.h">
      <Filter>Entities\Object</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectDefines.h">
      <Filter>Entities\Object</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectGuid.h">
      <Filter>Entities\Object</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\ObjectPosSelector.h">
      <Filter>Entities\Object</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Position.h">
      <Filter>Entities\Object</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateData.h">
      <Filter>Entities\Object\Updates</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFieldFlags.h">
      <Filter>Entities\Object\Updates</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateFields.h">
      <Filter>Entities\Object\Updates</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Object\Updates\UpdateMask.h">
      <Filter>Entities\Object\Updates</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\Pet.h">
      <Filter>Entities\Pet</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Pet\PetDefines.h">
      <Filter>Entities\Pet</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\CinematicMgr.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\KillRewarder.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\Player.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerSettings.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\PlayerTaxi.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\SocialMgr.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Player\TradeData.h">
      <Filter>Entities\Player</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Totem\Totem.h">
      <Filter>Entities\Totem</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Transport\Transport.h">
      <Filter>Entities\Transport</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\CharmInfo.h">
      <Filter>Entities\Unit</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\Unit.h">
      <Filter>Entities\Unit</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\UnitDefines.h">
      <Filter>Entities\Unit</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Unit\UnitUtils.h">
      <Filter>Entities\Unit</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\Vehicle.h">
      <Filter>Entities\Vehicle</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Entities\Vehicle\VehicleDefines.h">
      <Filter>Entities\Vehicle</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Events\GameEventMgr.h">
      <Filter>Events</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectAccessor.h">
      <Filter>Globals</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\ObjectMgr.h">
      <Filter>Globals</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Globals\WorldGlobals.h">
      <Filter>Globals</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridCell.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridDefines.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridObjectLoader.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridRefMgr.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridReference.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainData.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\GridTerrainLoader.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGrid.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\MapGridManager.h">
      <Filter>Grids</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Cells\Cell.h">
      <Filter>Grids\Cells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Cells\CellImpl.h">
      <Filter>Grids\Cells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiers.h">
      <Filter>Grids\Notifiers</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Grids\Notifiers\GridNotifiersImpl.h">
      <Filter>Grids\Notifiers</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\Group.h">
      <Filter>Groups</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupMgr.h">
      <Filter>Groups</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupRefMgr.h">
      <Filter>Groups</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Groups\GroupReference.h">
      <Filter>Groups</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\Guild.h">
      <Filter>Guilds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Guilds\GuildMgr.h">
      <Filter>Guilds</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\AddonHandler.h">
      <Filter>Handlers</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Handlers\NPCHandler.h">
      <Filter>Handlers</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceSaveMgr.h">
      <Filter>Instances</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Instances\InstanceScript.h">
      <Filter>Instances</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootItemStorage.h">
      <Filter>Loot</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Loot\LootMgr.h">
      <Filter>Loot</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Mails\Mail.h">
      <Filter>Mails</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Mails\ServerMailMgr.h">
      <Filter>Mails</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaBoundary.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\AreaDefines.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\Map.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapInstanced.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapMgr.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapRefMgr.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapReference.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\MapUpdater.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\TransportMgr.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Maps\ZoneScript.h">
      <Filter>Maps</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\BanMgr.h">
      <Filter>Misc</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\DynamicVisibility.h">
      <Filter>Misc</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Misc\GameGraveyard.h">
      <Filter>Misc</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Formulas.h">
      <Filter>Miscellaneous</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Miscellaneous\Language.h">
      <Filter>Miscellaneous</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Modules\ModuleMgr.h">
      <Filter>Modules</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Motd\MotdMgr.h">
      <Filter>Motd</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerRefMgr.h">
      <Filter>Movement</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\FollowerReference.h">
      <Filter>Movement</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MotionMaster.h">
      <Filter>Movement</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerator.h">
      <Filter>Movement</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\ConfusedMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\EscortMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\FleeingMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\HomeMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\IdleMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PathGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\PointMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\RandomMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\TargetedMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\MovementGenerators\WaypointMovementGenerator.h">
      <Filter>Movement\MovementGenerators</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSpline.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineFlag.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInit.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MoveSplineInitArgs.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementPacketBuilder.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\MovementTypedefs.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\Spline.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Spline\SplineImpl.h">
      <Filter>Movement\Spline</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Movement\Waypoints\WaypointMgr.h">
      <Filter>Movement\Waypoints</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvP.h">
      <Filter>OutdoorPvP</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\OutdoorPvP\OutdoorPvPMgr.h">
      <Filter>OutdoorPvP</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Petitions\PetitionMgr.h">
      <Filter>Petitions</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Pools\PoolMgr.h">
      <Filter>Pools</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Quests\QuestDef.h">
      <Filter>Quests</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Reputation\ReputationMgr.h">
      <Filter>Reputation</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgr.h">
      <Filter>Scripting</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptMgrMacros.h">
      <Filter>Scripting</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObject.h">
      <Filter>Scripting</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptObjectFwd.h">
      <Filter>Scripting</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptSystem.h">
      <Filter>Scripting</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AccountScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementCriteriaScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AchievementScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllBattlegroundScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCommandScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllCreatureScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllGameObjectScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllItemScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllMapScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllScriptsObjects.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AllSpellScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AreaTriggerScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ArenaTeamScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\AuctionHouseScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundMapScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\BattlegroundScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CommandScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ConditionScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\CreatureScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DatabaseScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\DynamicObjectScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ElunaScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\FormulaScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameEventScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GameObjectScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GlobalScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GroupScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\GuildScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\InstanceMapScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ItemScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\LootScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MailScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MiscScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ModuleScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\MovementHandlerScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\OutdoorPvPScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PetScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\PlayerScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\ServerScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\SpellScriptLoader.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TicketScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\TransportScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\UnitScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\VehicleScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WeatherScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldMapScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldObjectScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Scripting\ScriptDefines\WorldScript.h">
      <Filter>Scripting\ScriptDefines</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packet.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldPacket.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSession.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSessionMgr.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocket.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\WorldSocketMgr.h">
      <Filter>Server</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\AllPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\BankPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CharacterPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\ChatPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatLogPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\CombatPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\GuildPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\LFGPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\MiscPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PacketUtilities.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\PetPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\TotemPackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Packets\WorldStatePackets.h">
      <Filter>Server\Packets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\Opcodes.h">
      <Filter>Server\Protocol</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\PacketLog.h">
      <Filter>Server\Protocol</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Server\Protocol\ServerPktHeader.h">
      <Filter>Server\Protocol</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillDiscovery.h">
      <Filter>Skills</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Skills\SkillExtraItems.h">
      <Filter>Skills</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Spell.h">
      <Filter>Spells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellDefines.h">
      <Filter>Spells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellInfo.h">
      <Filter>Spells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellMgr.h">
      <Filter>Spells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\SpellScript.h">
      <Filter>Spells</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraDefines.h">
      <Filter>Spells\Auras</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuraEffects.h">
      <Filter>Spells\Auras</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Spells\Auras\SpellAuras.h">
      <Filter>Spells\Auras</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Texts\ChatTextBuilder.h">
      <Filter>Texts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Texts\CreatureTextMgr.h">
      <Filter>Texts</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tickets\TicketMgr.h">
      <Filter>Tickets</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Time\GameTime.h">
      <Filter>Time</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Time\UpdateTime.h">
      <Filter>Time</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tools\CharacterDatabaseCleaner.h">
      <Filter>Tools</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Tools\PlayerDump.h">
      <Filter>Tools</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Warden.h">
      <Filter>Warden</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenCheckMgr.h">
      <Filter>Warden</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenMac.h">
      <Filter>Warden</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenPayloadMgr.h">
      <Filter>Warden</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\WardenWin.h">
      <Filter>Warden</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Modules\WardenModuleMac.h">
      <Filter>Warden\Modules</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Warden\Modules\WardenModuleWin.h">
      <Filter>Warden\Modules</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Weather\Weather.h">
      <Filter>Weather</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\Weather\WeatherMgr.h">
      <Filter>Weather</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\IWorld.h">
      <Filter>World</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\World.h">
      <Filter>World</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldConfig.h">
      <Filter>World</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldState.h">
      <Filter>World</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\azerothcore-pbot\src\server\game\World\WorldStateDefines.h">
      <Filter>World</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\Debug\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\Release\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\MinSizeRel\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
    <ClInclude Include="D:\keji\KejiBuild\src\server\game\CMakeFiles\game.dir\RelWithDebInfo\cmake_pch.hxx">
      <Filter>Precompile Header File</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\keji\azerothcore-pbot\src\server\game\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="AI">
      <UniqueIdentifier>{E4A4D659-321E-3349-B4B2-************}</UniqueIdentifier>
    </Filter>
    <Filter Include="AI\CoreAI">
      <UniqueIdentifier>{C749E0CB-9198-37AB-A543-46E40914EF5B}</UniqueIdentifier>
    </Filter>
    <Filter Include="AI\ScriptedAI">
      <UniqueIdentifier>{554CB523-92B7-3F5B-B08D-3E4EDF1C2580}</UniqueIdentifier>
    </Filter>
    <Filter Include="AI\SmartScripts">
      <UniqueIdentifier>{61B400FD-C287-3EA8-9451-416A70A659BF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Accounts">
      <UniqueIdentifier>{E5EB192C-EFE3-3DFB-8577-93FD8C49CF8E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Achievements">
      <UniqueIdentifier>{BC0307A2-3812-3DF3-8E9E-B15C05D07C32}</UniqueIdentifier>
    </Filter>
    <Filter Include="Addons">
      <UniqueIdentifier>{398140C7-5C3A-3E7B-A263-624322D195F5}</UniqueIdentifier>
    </Filter>
    <Filter Include="ArenaSpectator">
      <UniqueIdentifier>{89B63F22-0C1F-3206-BA6C-0BE4FDCA4951}</UniqueIdentifier>
    </Filter>
    <Filter Include="AuctionHouse">
      <UniqueIdentifier>{6B43368C-5C2F-3BA6-94A0-616C77BE7B60}</UniqueIdentifier>
    </Filter>
    <Filter Include="Autobroadcast">
      <UniqueIdentifier>{830CB2A0-37ED-3AB7-9F30-349747036B16}</UniqueIdentifier>
    </Filter>
    <Filter Include="Battlefield">
      <UniqueIdentifier>{0440BCF6-1605-3604-8578-D4B2D313DFFB}</UniqueIdentifier>
    </Filter>
    <Filter Include="Battlefield\Zones">
      <UniqueIdentifier>{E2B99096-ADA2-3BA3-BE5F-5C4B0C83EF6F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Battlegrounds">
      <UniqueIdentifier>{EBAF5CD4-9F05-343D-8BE7-218C0FC35E33}</UniqueIdentifier>
    </Filter>
    <Filter Include="Battlegrounds\ArenaSeason">
      <UniqueIdentifier>{21C72730-AAE5-3999-A075-E24501E7DB29}</UniqueIdentifier>
    </Filter>
    <Filter Include="Battlegrounds\Zones">
      <UniqueIdentifier>{F3C8BA24-339B-3640-9EA4-02969D119654}</UniqueIdentifier>
    </Filter>
    <Filter Include="Cache">
      <UniqueIdentifier>{DED64FBF-8B87-3AC1-9BD6-FDF9C6F7FAAF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Calendar">
      <UniqueIdentifier>{4F87FA4B-51A5-345A-A01C-D497FC7040D6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Chat">
      <UniqueIdentifier>{C2455656-70C2-3982-9194-2D03B84F6552}</UniqueIdentifier>
    </Filter>
    <Filter Include="Chat\Channels">
      <UniqueIdentifier>{FC52F5A3-DEBE-3A22-BA94-0976BE56400D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Chat\ChatCommands">
      <UniqueIdentifier>{E2B98EA2-948F-3A5E-BFD5-805D2C819F84}</UniqueIdentifier>
    </Filter>
    <Filter Include="Combat">
      <UniqueIdentifier>{82B9CDA6-297D-3524-9EE8-A0B000A76F8A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Conditions">
      <UniqueIdentifier>{6ECF28B1-78AB-35BD-B4B5-1895372D5734}</UniqueIdentifier>
    </Filter>
    <Filter Include="DataStores">
      <UniqueIdentifier>{04EC1F01-CF0A-3346-89CA-65C2767375B7}</UniqueIdentifier>
    </Filter>
    <Filter Include="DungeonFinding">
      <UniqueIdentifier>{8A51901E-651F-33CA-B5B8-640E4606B98C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities">
      <UniqueIdentifier>{3C917B5F-34A9-3EA3-A1AB-986D0E29A938}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Corpse">
      <UniqueIdentifier>{B8F8FE75-96AF-33F1-A886-913056F07D29}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Creature">
      <UniqueIdentifier>{25E6CFC8-A87E-34FF-96A3-E30D5694FD80}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\DynamicObject">
      <UniqueIdentifier>{A7FE85BC-D8C7-35F2-BC09-172113C75A1E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\GameObject">
      <UniqueIdentifier>{271486F0-9D6A-3E62-8558-D57132463BB5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Item">
      <UniqueIdentifier>{60330F90-F107-33A1-9ECF-073949E896A6}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Item\Container">
      <UniqueIdentifier>{53642379-AAAE-359C-8D95-BC142B298095}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Object">
      <UniqueIdentifier>{DA1F4740-EEA2-3DB4-BD45-616E2F96FD27}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Object\Updates">
      <UniqueIdentifier>{CCC3F2A1-F1FB-39AB-9C7D-CAB9DA034000}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Pet">
      <UniqueIdentifier>{C6CF37AD-5FAC-3353-B19F-E07DA3179526}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Player">
      <UniqueIdentifier>{C30124A3-20DB-3F1C-B859-B27BFE43F412}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Totem">
      <UniqueIdentifier>{9274ED1C-1430-39BE-BFA1-15C37AC3A388}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Transport">
      <UniqueIdentifier>{E97368EC-7FB3-3E53-8443-F324E5893EB9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Unit">
      <UniqueIdentifier>{956534EB-FC0F-3382-9ABC-5F3F95DD395A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Entities\Vehicle">
      <UniqueIdentifier>{5F2DE4FB-16D2-3AA4-8C4D-CFADCDC114C3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Events">
      <UniqueIdentifier>{16CD9C36-19DE-3827-8EB0-248B92ABC62A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Globals">
      <UniqueIdentifier>{E1F5B0E7-E5D0-333B-9534-FDD8B02754C8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grids">
      <UniqueIdentifier>{5C4A8E99-F8C2-3DAB-8B1B-E40511FF8F45}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grids\Cells">
      <UniqueIdentifier>{F4715AEA-9B97-30B6-B6FF-52CD6FF65CF2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Grids\Notifiers">
      <UniqueIdentifier>{646DB1A5-FA07-3C22-B4A5-0E5A97821FC3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Groups">
      <UniqueIdentifier>{********-19E2-36ED-B9DC-375A162DB7F1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Guilds">
      <UniqueIdentifier>{A64813E9-AB98-3340-AC98-2D7F4FA69930}</UniqueIdentifier>
    </Filter>
    <Filter Include="Handlers">
      <UniqueIdentifier>{8AADD498-AA52-39A5-B71A-724D64F4E02F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Instances">
      <UniqueIdentifier>{037A02E4-F0A7-3894-A88E-17AB1450E0A3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Loot">
      <UniqueIdentifier>{273AC49E-89B9-3049-8CF1-06BF54C80B44}</UniqueIdentifier>
    </Filter>
    <Filter Include="Mails">
      <UniqueIdentifier>{7933F8AB-4A14-39A7-BC82-E00464E05514}</UniqueIdentifier>
    </Filter>
    <Filter Include="Maps">
      <UniqueIdentifier>{6296D179-88B6-3304-B41A-DE94E0DF24EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Misc">
      <UniqueIdentifier>{C6A6BCC9-C7C1-38EE-82DC-A78E0549B523}</UniqueIdentifier>
    </Filter>
    <Filter Include="Miscellaneous">
      <UniqueIdentifier>{3B3C5713-1DF7-3E51-AC40-15C23B61230E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Modules">
      <UniqueIdentifier>{F65D89E7-F940-3B4E-AD6A-DAECF184943A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Motd">
      <UniqueIdentifier>{CB051560-1336-3F47-80DE-9781DC63F15A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Movement">
      <UniqueIdentifier>{2E9EBE35-3D84-3311-B0CF-67D80C39BED2}</UniqueIdentifier>
    </Filter>
    <Filter Include="Movement\MovementGenerators">
      <UniqueIdentifier>{D6B2CEA8-8732-3344-AED1-74CEAFF36EF3}</UniqueIdentifier>
    </Filter>
    <Filter Include="Movement\Spline">
      <UniqueIdentifier>{52A585BA-52A7-3951-8192-46B48877019E}</UniqueIdentifier>
    </Filter>
    <Filter Include="Movement\Waypoints">
      <UniqueIdentifier>{7F4EF129-C6E9-3E3D-930E-DC92A3EF1F0E}</UniqueIdentifier>
    </Filter>
    <Filter Include="OutdoorPvP">
      <UniqueIdentifier>{F949FC83-8168-3E0E-A00B-BA867A605D0D}</UniqueIdentifier>
    </Filter>
    <Filter Include="Petitions">
      <UniqueIdentifier>{4AB18B59-15FA-3F58-92B2-8C016AC0E5D9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Pools">
      <UniqueIdentifier>{D7E4ACC9-7A97-3B29-83EC-34390B5E9BE9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Precompile Header File">
      <UniqueIdentifier>{90DBB2E8-B7B8-3A5B-B6BF-533AA6796052}</UniqueIdentifier>
    </Filter>
    <Filter Include="Quests">
      <UniqueIdentifier>{BE0A7D1C-BCE1-3433-B17F-43CF3D90A044}</UniqueIdentifier>
    </Filter>
    <Filter Include="Reputation">
      <UniqueIdentifier>{59D51C68-5119-35FA-9BCB-8F20C895C4CF}</UniqueIdentifier>
    </Filter>
    <Filter Include="Scripting">
      <UniqueIdentifier>{24CEA571-5A5C-37E7-9CC4-9C35DD71D12B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Scripting\ScriptDefines">
      <UniqueIdentifier>{D3532E58-E91E-3CCD-972F-D1A4E6C705A0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Server">
      <UniqueIdentifier>{5C910097-3D6A-3149-90F1-BD7CE9004329}</UniqueIdentifier>
    </Filter>
    <Filter Include="Server\Packets">
      <UniqueIdentifier>{AB21C1D3-A6B5-3420-8665-82BBBA103551}</UniqueIdentifier>
    </Filter>
    <Filter Include="Server\Protocol">
      <UniqueIdentifier>{887AF19A-6CCB-3C91-BCBC-6E79BA7C977F}</UniqueIdentifier>
    </Filter>
    <Filter Include="Skills">
      <UniqueIdentifier>{6AAEEC77-C9A4-30B9-AC29-7E561F5A38E7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{13BB9762-CF46-3603-980C-A0F8777D13B0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Spells">
      <UniqueIdentifier>{E5AD1C67-1A3D-33C2-9928-24EDF5CD374A}</UniqueIdentifier>
    </Filter>
    <Filter Include="Spells\Auras">
      <UniqueIdentifier>{BB0D0CCE-AD86-3936-82B9-D207CC33E07C}</UniqueIdentifier>
    </Filter>
    <Filter Include="Texts">
      <UniqueIdentifier>{DBA4C232-ACB9-37B4-B900-4B455B8BDB15}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tickets">
      <UniqueIdentifier>{808446D7-7C54-3776-8438-CCBB0CC18CDC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Time">
      <UniqueIdentifier>{685ACAB4-EA77-3491-9DEF-740C0A2B9023}</UniqueIdentifier>
    </Filter>
    <Filter Include="Tools">
      <UniqueIdentifier>{8E06D9F7-4675-3BDD-8DCA-BECA3ABAFF51}</UniqueIdentifier>
    </Filter>
    <Filter Include="Warden">
      <UniqueIdentifier>{494227EB-4177-3139-87A6-2D2DEB52E41B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Warden\Modules">
      <UniqueIdentifier>{EDC469EB-BA17-31B3-84EC-8FB25E96FB96}</UniqueIdentifier>
    </Filter>
    <Filter Include="Weather">
      <UniqueIdentifier>{8ED5373A-AF18-3140-9EC0-5CD7B7BC8181}</UniqueIdentifier>
    </Filter>
    <Filter Include="World">
      <UniqueIdentifier>{C08FC07C-7C6B-39B8-BF8D-81CCA4BD1344}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
