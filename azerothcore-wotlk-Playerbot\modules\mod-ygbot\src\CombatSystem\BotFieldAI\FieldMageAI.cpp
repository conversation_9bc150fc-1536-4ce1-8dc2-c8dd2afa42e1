#include "FieldMageAI.h"
#include "Log.h"

FieldMageAI::FieldMageAI(Player* player) 
    : BotFieldAI(player), BotMageSpells(), m_BotTalentType(0)
{
    ResetBotAI();
}

void FieldMageAI::UpdateBotAI(uint32 diff)
{
    // 调用基类的更新逻辑
    BotFieldAI::UpdateBotAI(diff);
}

void FieldMageAI::ResetBotAI()
{
    BotFieldAI::ResetBotAI();
    UpdateTalentType();
    InitializeSpells(m_player);
}

void FieldMageAI::UpdateTalentType()
{
    // 使用TalentSystem的天赋检测
    m_BotTalentType = YGbotTalentDetector::DetectPrimaryTalentSpec(m_player);
    
    LOG_DEBUG("server", "FieldMageAI: {} 天赋类型检测结果: {} (0=奥术, 1=火焰, 2=冰霜)", 
              m_player->GetName(), m_BotTalentType);
}

void FieldMageAI::ProcessMeleeSpell(Unit* pTarget)
{
    if (!pTarget)
        return;

    // 法师是远程职业，近战时使用基础攻击
    LOG_DEBUG("server", "FieldMageAI: {} 在近战范围，使用基础攻击", m_player->GetName());
}

void FieldMageAI::ProcessRangeSpell(Unit* pTarget)
{
    if (!pTarget)
        return;

    // 根据天赋类型使用不同技能
    if (m_BotTalentType == 0) // 奥术天赋
    {
        ProcessArcaneCombat(pTarget);
    }
    else if (m_BotTalentType == 1) // 火焰天赋
    {
        ProcessFireCombat(pTarget);
    }
    else // 冰霜天赋
    {
        ProcessFrostCombat(pTarget);
    }
}

void FieldMageAI::ProcessHealthSpell(Unit* pTarget)
{
    // 法师没有治疗技能，使用法力护盾等防护技能
    if (PlayerBotAI::NeedHeal(50.0f))
    {
        // TODO: 使用法力护盾、冰甲术等防护技能
    }
}

bool FieldMageAI::ProcessNormalSpell()
{
    // 处理非战斗技能（传送门、变形术等）
    return false;
}

void FieldMageAI::UpEnergy()
{
    // 法师使用法力值，检查是否需要回蓝
    if (PlayerBotAI::NeedMana(30.0f))
    {
        // TODO: 使用唤醒、法力宝石等回蓝技能
    }
}

void FieldMageAI::ClearMechanicAura()
{
    // 清除负面效果
    // TODO: 实现冰箱、闪现等技能
}

bool FieldMageAI::NeedFlee()
{
    // 法师血量低或法力不足时需要逃跑
    return PlayerBotAI::NeedHeal(20.0f) || PlayerBotAI::NeedMana(10.0f);
}

void FieldMageAI::ProcessFlee()
{
    if (NeedFlee())
    {
        // TODO: 实现闪现、冰箱等逃跑技能
    }
}

void FieldMageAI::ProcessArcaneCombat(Unit* target)
{
    if (!target)
        return;

    // 奥术法师的战斗逻辑
    // TODO: 实现奥术飞弹、奥术爆炸等技能
    LOG_DEBUG("server", "FieldMageAI: {} 使用奥术法师战斗逻辑", m_player->GetName());
}

void FieldMageAI::ProcessFireCombat(Unit* target)
{
    if (!target)
        return;

    // 火焰法师的战斗逻辑
    // TODO: 实现火球术、炎爆术等技能
    LOG_DEBUG("server", "FieldMageAI: {} 使用火焰法师战斗逻辑", m_player->GetName());
}

void FieldMageAI::ProcessFrostCombat(Unit* target)
{
    if (!target)
        return;

    // 冰霜法师的战斗逻辑
    // TODO: 实现寒冰箭、暴风雪等技能
    LOG_DEBUG("server", "FieldMageAI: {} 使用冰霜法师战斗逻辑", m_player->GetName());
}
