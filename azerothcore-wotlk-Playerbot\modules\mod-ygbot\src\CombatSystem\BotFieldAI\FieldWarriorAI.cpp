#include "FieldWarriorAI.h"
#include "Log.h"

FieldWarriorAI::FieldWarriorAI(Player* player) 
    : BotFieldAI(player), BotWarriorSpells(), m_BotTalentType(0)
{
    ResetBotAI();
}

void FieldWarriorAI::UpdateBotAI(uint32 diff)
{
    // 调用基类的更新逻辑
    BotFieldAI::UpdateBotAI(diff);
}

void FieldWarriorAI::ResetBotAI()
{
    BotFieldAI::ResetBotAI();
    UpdateTalentType();
    InitializeSpells(m_player);
}

void FieldWarriorAI::UpdateTalentType()
{
    // 使用TalentSystem的天赋检测
    m_BotTalentType = YGbotTalentDetector::DetectPrimaryTalentSpec(m_player);
    
    LOG_DEBUG("server", "FieldWarriorAI: {} 天赋类型检测结果: {} (0=武器, 1=狂暴, 2=防护)", 
              m_player->GetName(), m_BotTalentType);
}

void FieldWarriorAI::ProcessMeleeSpell(Unit* pTarget)
{
    if (!pTarget)
        return;

    // 根据天赋类型使用不同技能
    if (m_BotTalentType == 2) // 防护天赋
    {
        ProcessTankCombat(pTarget);
    }
    else // 武器或狂暴天赋
    {
        ProcessDPSCombat(pTarget);
    }
}

void FieldWarriorAI::ProcessRangeSpell(Unit* pTarget)
{
    if (!pTarget)
        return;

    // 战士没有远程技能，使用冲锋等技能
    // TODO: 实现冲锋、投掷等远程技能
}

void FieldWarriorAI::ProcessHealthSpell(Unit* pTarget)
{
    // 战士的自我治疗技能
    if (PlayerBotAI::NeedHeal(30.0f))
    {
        // TODO: 使用战士的治疗技能（如绷带、药水等）
    }
}

bool FieldWarriorAI::ProcessNormalSpell()
{
    // 处理非战斗技能
    return false;
}

void FieldWarriorAI::UpEnergy()
{
    // 战士使用怒气，不需要特殊处理
}

void FieldWarriorAI::ClearMechanicAura()
{
    // 清除负面效果
    // TODO: 实现狂暴之怒等技能
}

bool FieldWarriorAI::NeedFlee()
{
    // 战士通常不需要逃跑，除非血量极低
    return PlayerBotAI::NeedHeal(10.0f);
}

void FieldWarriorAI::ProcessFlee()
{
    if (NeedFlee())
    {
        // TODO: 实现逃跑逻辑
    }
}

void FieldWarriorAI::ProcessTankCombat(Unit* target)
{
    if (!target)
        return;

    // 防护战士的战斗逻辑
    // TODO: 实现盾击、复仇等技能
    LOG_DEBUG("server", "FieldWarriorAI: {} 使用防护战士战斗逻辑", m_player->GetName());
}

void FieldWarriorAI::ProcessDPSCombat(Unit* target)
{
    if (!target)
        return;

    // DPS战士的战斗逻辑
    // TODO: 实现致死打击、顺劈斩等技能
    LOG_DEBUG("server", "FieldWarriorAI: {} 使用DPS战士战斗逻辑", m_player->GetName());
}
