
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:26 (project)"
    message: |
      The system is: Windows - 10.0.19045 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:17銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> D:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.90
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        D:/keji/build/CMakeFiles/3.31.3/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:21銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdC.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TC /FC /errorReport:queue CMakeCCompilerId.c
        CMakeCCompilerId.c
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdC.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdC.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdC.lib" /MACHINE:X64 Debug\\CMakeCCompilerId.obj
        CompilerIdC.vcxproj -> D:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdC\\CompilerIdC.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_C_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_C_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdC.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdC.tlog\\CompilerIdC.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\3.31.3\\CompilerIdC\\CompilerIdC.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:02.82
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.exe"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CompilerIdC.vcxproj"
      
      The C compiler identification is MSVC, found in:
        D:/keji/build/CMakeFiles/3.31.3/CompilerIdC/CompilerIdC.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-sn10v3"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-sn10v3"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-sn10v3'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_8e59d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:25銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sn10v3\\cmTC_8e59d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_8e59d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sn10v3\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_8e59d.dir\\Debug\\cmTC_8e59d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_8e59d.dir\\Debug\\cmTC_8e59d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_8e59d.dir\\Debug\\cmTC_8e59d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8e59d.dir\\Debug\\\\" /Fd"cmTC_8e59d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_8e59d.dir\\Debug\\\\" /Fd"cmTC_8e59d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sn10v3\\Debug\\cmTC_8e59d.exe" /INCREMENTAL /ILK:"cmTC_8e59d.dir\\Debug\\cmTC_8e59d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-sn10v3/Debug/cmTC_8e59d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-sn10v3/Debug/cmTC_8e59d.lib" /MACHINE:X64  /machine:x64 cmTC_8e59d.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_8e59d.vcxproj -> D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sn10v3\\Debug\\cmTC_8e59d.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_8e59d.dir\\Debug\\cmTC_8e59d.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_8e59d.dir\\Debug\\cmTC_8e59d.tlog\\cmTC_8e59d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-sn10v3\\cmTC_8e59d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.55
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-jx7vlg"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-jx7vlg"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-jx7vlg'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_a0060.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:29銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jx7vlg\\cmTC_a0060.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a0060.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jx7vlg\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_a0060.dir\\Debug\\cmTC_a0060.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_a0060.dir\\Debug\\cmTC_a0060.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_a0060.dir\\Debug\\cmTC_a0060.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a0060.dir\\Debug\\\\" /Fd"cmTC_a0060.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_a0060.dir\\Debug\\\\" /Fd"cmTC_a0060.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCCompilerABI.c"
          CMakeCCompilerABI.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jx7vlg\\Debug\\cmTC_a0060.exe" /INCREMENTAL /ILK:"cmTC_a0060.dir\\Debug\\cmTC_a0060.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-jx7vlg/Debug/cmTC_a0060.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-jx7vlg/Debug/cmTC_a0060.lib" /MACHINE:X64  /machine:x64 cmTC_a0060.dir\\Debug\\CMakeCCompilerABI.obj
          cmTC_a0060.vcxproj -> D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jx7vlg\\Debug\\cmTC_a0060.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_a0060.dir\\Debug\\cmTC_a0060.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_a0060.dir\\Debug\\cmTC_a0060.tlog\\cmTC_a0060.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-jx7vlg\\cmTC_a0060.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.73
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Parsed C implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'C': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:26 (project)"
    message: |
      Running the C compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35213.0
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindBoost.cmake:2160 (find_package)"
      - "deps/boost/CMakeLists.txt:34 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-o3gej3"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-o3gej3"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-o3gej3'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_49b38.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:37銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\cmTC_49b38.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_49b38.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_49b38.dir\\Debug\\cmTC_49b38.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_49b38.dir\\Debug\\cmTC_49b38.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_49b38.dir\\Debug\\cmTC_49b38.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_49b38.dir\\Debug\\\\" /Fd"cmTC_49b38.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\src.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_49b38.dir\\Debug\\\\" /Fd"cmTC_49b38.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\src.c"
          src.c
        D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\cmTC_49b38.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\cmTC_49b38.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\cmTC_49b38.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\src.c(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-o3gej3\\cmTC_49b38.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.24
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindBoost.cmake:2160 (find_package)"
      - "deps/boost/CMakeLists.txt:34 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-0bx4id"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-0bx4id"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-0bx4id'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_bd675.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:41銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\cmTC_bd675.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bd675.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_bd675.dir\\Debug\\cmTC_bd675.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_bd675.dir\\Debug\\cmTC_bd675.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_bd675.dir\\Debug\\cmTC_bd675.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bd675.dir\\Debug\\\\" /Fd"cmTC_bd675.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_bd675.dir\\Debug\\\\" /Fd"cmTC_bd675.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\Debug\\cmTC_bd675.exe" /INCREMENTAL /ILK:"cmTC_bd675.dir\\Debug\\cmTC_bd675.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-0bx4id/Debug/cmTC_bd675.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-0bx4id/Debug/cmTC_bd675.lib" /MACHINE:X64  /machine:x64 cmTC_bd675.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\cmTC_bd675.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\cmTC_bd675.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\cmTC_bd675.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-0bx4id\\cmTC_bd675.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:02.27
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindBoost.cmake:2160 (find_package)"
      - "deps/boost/CMakeLists.txt:34 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-oy3n56"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-oy3n56"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-oy3n56'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_27a1b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:34:44銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\cmTC_27a1b.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_27a1b.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_27a1b.dir\\Debug\\cmTC_27a1b.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_27a1b.dir\\Debug\\cmTC_27a1b.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_27a1b.dir\\Debug\\cmTC_27a1b.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_27a1b.dir\\Debug\\\\" /Fd"cmTC_27a1b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\CheckFunctionExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_27a1b.dir\\Debug\\\\" /Fd"cmTC_27a1b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\CheckFunctionExists.c"
          CheckFunctionExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\Debug\\cmTC_27a1b.exe" /INCREMENTAL /ILK:"cmTC_27a1b.dir\\Debug\\cmTC_27a1b.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-oy3n56/Debug/cmTC_27a1b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-oy3n56/Debug/cmTC_27a1b.lib" /MACHINE:X64  /machine:x64 cmTC_27a1b.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\cmTC_27a1b.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\cmTC_27a1b.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\cmTC_27a1b.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-oy3n56\\cmTC_27a1b.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:16.05
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckSymbolExists.cmake:163 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckSymbolExists.cmake:68 (__CHECK_SYMBOL_EXISTS_IMPL)"
      - "deps/fmt/CMakeLists.txt:22 (check_symbol_exists)"
    checks:
      - "Looking for _strtod_l"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-y72ghj"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-y72ghj"
    cmakeVariables:
      CMAKE_C_FLAGS: "/DWIN32 /D_WINDOWS"
      CMAKE_C_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "HAVE_STRTOD_L"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-y72ghj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_0da1d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:35:05銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\cmTC_0da1d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_0da1d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_0da1d.dir\\Debug\\cmTC_0da1d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_0da1d.dir\\Debug\\cmTC_0da1d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_0da1d.dir\\Debug\\cmTC_0da1d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0da1d.dir\\Debug\\\\" /Fd"cmTC_0da1d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\CheckSymbolExists.c"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"cmTC_0da1d.dir\\Debug\\\\" /Fd"cmTC_0da1d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TC /errorReport:queue "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\CheckSymbolExists.c"
          CheckSymbolExists.c
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\Debug\\cmTC_0da1d.exe" /INCREMENTAL /ILK:"cmTC_0da1d.dir\\Debug\\cmTC_0da1d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-y72ghj/Debug/cmTC_0da1d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-y72ghj/Debug/cmTC_0da1d.lib" /MACHINE:X64  /machine:x64 cmTC_0da1d.dir\\Debug\\CheckSymbolExists.obj
          cmTC_0da1d.vcxproj -> D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\Debug\\cmTC_0da1d.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_0da1d.dir\\Debug\\cmTC_0da1d.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_0da1d.dir\\Debug\\cmTC_0da1d.tlog\\cmTC_0da1d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-y72ghj\\cmTC_0da1d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:04.71
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFileCXX.cmake:98 (try_compile)"
      - "src/cmake/macros/FindFilesystem.cmake:149 (check_include_file_cxx)"
      - "deps/stdfs/CMakeLists.txt:13 (find_package)"
    checks:
      - "Looking for C++ include filesystem"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-mw64rv"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-mw64rv"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1 /bigobj"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "_CXX_FILESYSTEM_HAVE_HEADER"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-mw64rv'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_521c7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:35:11銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\cmTC_521c7.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_521c7.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_521c7.dir\\Debug\\cmTC_521c7.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_521c7.dir\\Debug\\cmTC_521c7.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_521c7.dir\\Debug\\cmTC_521c7.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_521c7.dir\\Debug\\\\" /Fd"cmTC_521c7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\CheckIncludeFile.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_521c7.dir\\Debug\\\\" /Fd"cmTC_521c7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\CheckIncludeFile.cxx"
          CheckIncludeFile.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\Debug\\cmTC_521c7.exe" /INCREMENTAL /ILK:"cmTC_521c7.dir\\Debug\\cmTC_521c7.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-mw64rv/Debug/cmTC_521c7.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-mw64rv/Debug/cmTC_521c7.lib" /MACHINE:X64  /machine:x64 cmTC_521c7.dir\\Debug\\CheckIncludeFile.obj
          cmTC_521c7.vcxproj -> D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\Debug\\cmTC_521c7.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_521c7.dir\\Debug\\cmTC_521c7.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_521c7.dir\\Debug\\cmTC_521c7.tlog\\cmTC_521c7.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-mw64rv\\cmTC_521c7.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:05.27
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "src/cmake/macros/FindFilesystem.cmake:197 (check_cxx_source_compiles)"
      - "deps/stdfs/CMakeLists.txt:13 (find_package)"
    checks:
      - "Performing Test CXX_FILESYSTEM_NO_LINK_NEEDED"
    directories:
      source: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-rigpg5"
      binary: "D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-rigpg5"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1 /bigobj"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "D:/keji/azerothcore-wotlk-Playerbot/src/cmake/macros"
    buildResult:
      variable: "CXX_FILESYSTEM_NO_LINK_NEEDED"
      cached: true
      stdout: |
        Change Dir: 'D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-rigpg5'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_82a9b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/8/9 14:35:17銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\cmTC_82a9b.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_82a9b.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_82a9b.dir\\Debug\\cmTC_82a9b.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_82a9b.dir\\Debug\\cmTC_82a9b.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_82a9b.dir\\Debug\\cmTC_82a9b.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CXX_FILESYSTEM_NO_LINK_NEEDED /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_82a9b.dir\\Debug\\\\" /Fd"cmTC_82a9b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35213 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CXX_FILESYSTEM_NO_LINK_NEEDED /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /std:c++20 /Fo"cmTC_82a9b.dir\\Debug\\\\" /Fd"cmTC_82a9b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue  /bigobj "D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\Debug\\cmTC_82a9b.exe" /INCREMENTAL /ILK:"cmTC_82a9b.dir\\Debug\\cmTC_82a9b.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-rigpg5/Debug/cmTC_82a9b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"D:/keji/build/CMakeFiles/CMakeScratch/TryCompile-rigpg5/Debug/cmTC_82a9b.lib" /MACHINE:X64  /machine:x64 cmTC_82a9b.dir\\Debug\\src.obj
          cmTC_82a9b.vcxproj -> D:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\Debug\\cmTC_82a9b.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_82a9b.dir\\Debug\\cmTC_82a9b.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_82a9b.dir\\Debug\\cmTC_82a9b.tlog\\cmTC_82a9b.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淒:\\keji\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rigpg5\\cmTC_82a9b.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:04.80
        
      exitCode: 0
...
