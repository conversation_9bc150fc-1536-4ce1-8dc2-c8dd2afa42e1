#pragma once

#include "Player.h"
#include "SpellMgr.h"
#include "../TalentSystem/YGbotTalentDetector.h"  // 使用天赋检测系统

// 职业技能基类 - 参考mod-pbot-keji的BotAISpells设计
class BotClassSpells
{
public:
    BotClassSpells() = default;
    virtual ~BotClassSpells() = default;

    // 纯虚函数，子类必须实现
    virtual void InitializeSpells(Player* player) = 0;
    
    // 通用方法
    uint32 FindMaxRankSpellByExist(uint32 spellID, Player* player);
    bool HasSpell(uint32 spellID, Player* player);
    
protected:
    // 通用技能查找
    uint32 GetSpellWithRank(uint32 baseSpellId, uint32 rank);
};

// 盗贼技能类 - 纯职业战斗逻辑（参考mod-pbot-keji架构）
class BotRogueSpells : public BotClassSpells
{
public:
    void InitializeSpells(Player* player) override;

    // 天赋检测方法 - 使用TalentSystem
    uint8 GetTalentType(Player* player);
    bool IsAssassinationSpec(Player* player);
    bool IsCombatSpec(Player* player);
    bool IsSubtletySpec(Player* player);

    // 盗贼专用战斗方法（需要在继承类中提供Player对象和辅助方法）
    bool ProcessSneakSpell(Unit* pTarget);
    bool ProcessMeleeBlind(Unit* pTarget);
    bool CanConsumeCombo(Unit* pTarget);
    bool CastCloakByNeed();
    bool CanBlind(Unit* pTarget);
    bool CanStartSpell();
    uint32 GetEnergyPowerPer();

    // 毒药系统
    uint32 GetPoisonEntryByWeaponType(EquipmentSlots equipSlot);
    bool ProcessUpPoison();

    // 潜行系统
    void OnCastSneak();
    void OnCastFlash(Unit* pTarget);

    // 光环机制检查（公共方法）
    virtual bool HasAuraMechanic(Unit* pTarget, Mechanics mask);

    // 毒药系统 - 完全按照mod-playerbots-liyunfan实现
    bool IsMainHandWeaponNoEnchant();
    bool IsOffHandWeaponNoEnchant();
    bool UseInstantPoisonOnMainHand();
    bool UseDeadlyPoisonOnOffHand();
    std::vector<Item*> FindPoisonItems(const std::string& baseName);
    bool UseItemOnWeapon(Item* item, Item* weapon);

    // 潜行系统 - 完全按照mod-playerbots-liyunfan实现
    bool ShouldStealth();
    bool CanStealth();

    // 辅助方法
    std::vector<Item*> FindItemsByName(Player* player, const std::string& itemName);
    uint32 GetPoisonEntryByName(const std::string& poisonName);
    std::string GetBestPoisonForLevel(uint32 level, EquipmentSlots slot);
    Item* FindItemFromAllBag(Player* player, uint32 itemEntry);
    Item* StoreNewItemByEntry(Player* player, uint32 itemEntry);

protected:
    // 纯虚方法，需要在继承类中实现
    virtual Player* GetPlayer() const = 0;
    virtual SpellCastResult TryCastSpell(uint32 spellID, Unit* pTarget = nullptr, bool force = false) = 0;

    // 防护技能
    uint32 RogueGuard_Sneak = 0;           // 潜行
    uint32 RogueGuard_ShadowCloak = 0;     // 暗影斗篷
    uint32 RogueGuard_Disappear = 0;       // 消失
    uint32 RogueGuard_Dodge = 0;           // 闪避
    uint32 RogueGuard_Sprint = 0;          // 疾跑

    // 潜行技能
    uint32 RogueSneak_Stick = 0;           // 预谋
    uint32 RogueSneak_Premeditate = 0;     // 预谋
    uint32 RogueSneak_Ambush = 0;          // 伏击
    uint32 RogueSneak_Surprise = 0;        // 偷袭

    // 辅助技能
    uint32 RogueAssist_ShadowDance = 0;    // 暗影之舞
    uint32 RogueAssist_ShadowFlash = 0;    // 暗影步
    uint32 RogueAssist_ReadyCD = 0;        // 预备
    uint32 RogueAssist_Blind = 0;          // 致盲
    uint32 RogueAssist_Disarm = 0;         // 缴械
    uint32 RogueAssist_NextCrit = 0;       // 冷血
    uint32 RogueAssist_Blood = 0;          // 嗜血
    uint32 RogueAssist_FastEnergy = 0;     // 冲动
    uint32 RogueAssist_BlockCast = 0;      // 脚踢
    uint32 RogueAssist_Paralyze = 0;       // 凿击
    uint32 RogueAssist_FastSpeed = 0;      // 急速

    // AOE技能
    uint32 RogueAOE_Knife = 0;             // 刀扇
    uint32 RogueAOE_AllDance = 0;          // 剑刃乱舞

    // 攻击技能
    uint32 RogueAttack_Blood = 0;          // 出血
    uint32 RogueAttack_Ghost = 0;          // 鬼魅攻击
    uint32 RogueAttack_Injure = 0;         // 致伤毒药
    uint32 RogueAttack_PoisonAtt = 0;      // 毒药攻击
    uint32 RogueAttack_BackAtt = 0;        // 背刺
    uint32 RogueAttack_EvilAtt = 0;        // 邪恶攻击

    uint32 RogueAttack_Damage = 0;         // 剔骨
    uint32 RogueAttack_Separate = 0;       // 切割
    uint32 RogueAttack_Stun = 0;           // 肾击
    uint32 RogueAttack_PoisonDmg = 0;      // 毒伤
    uint32 RogueAttack_Incision = 0;       // 切割（战斗天赋版本）
    uint32 RogueRange_Throw = 0;           // 投掷

    // 特殊标记
    uint32 RogueFlag_Dance = 0;            // 暗影之舞标记

protected:
    void InitializeGuardSpells(Player* player);
    void InitializeSneakSpells(Player* player);
    void InitializeAssistSpells(Player* player);
    void InitializeAttackSpells(Player* player);
};

// 战士技能类
class BotWarriorSpells : public BotClassSpells
{
public:
    void InitializeSpells(Player* player) override;

    // 防御技能
    uint32 WarriorDefance_Pull = 0;        // 嘲讽
    uint32 WarriorDefance_Charge = 0;      // 冲锋
    uint32 WarriorDefance_MaxLife = 0;     // 破釜沉舟
    uint32 WarriorDefance_ShieldBlock = 0; // 盾牌格挡
    uint32 WarriorDefance_ShieldWall = 0;  // 盾墙
    uint32 WarriorDefance_Disarm = 0;      // 缴械

    // 武器技能
    uint32 WarriorWeapon_DeadAtt = 0;      // 致死打击
    uint32 WarriorWeapon_WhirlwindAtt = 0; // 旋风斩
    uint32 WarriorWeapon_Sweep = 0;        // 横扫攻击
    uint32 WarriorWeapon_Heroic = 0;       // 英勇打击
    uint32 WarriorWeapon_Thunder = 0;      // 雷霆一击

    // 狂暴技能
    uint32 WarriorRage_Whirlwind = 0;      // 旋风斩
    uint32 WarriorRage_HeadAtt = 0;        // 拳击
    uint32 WarriorRage_Intercept = 0;      // 拦截
    uint32 WarriorRage_Berserker = 0;      // 狂暴

    // 通用技能
    uint32 WarriorCommon_PowerAtt = 0;     // 英勇打击
    uint32 WarriorCommon_PowerRelife = 0;  // 破釜沉舟

protected:
    void InitializeDefanceSpells(Player* player);
    void InitializeWeaponSpells(Player* player);
    void InitializeRageSpells(Player* player);
};

// 圣骑士技能类
class BotPaladinSpells : public BotClassSpells
{
public:
    void InitializeSpells(Player* player) override;

    // 神圣技能
    uint32 PaladinHoly_Light = 0;          // 圣光术
    uint32 PaladinHoly_Shock = 0;          // 神圣震击
    uint32 PaladinHoly_Wrath = 0;          // 愤怒之锤
    uint32 PaladinHoly_Consecration = 0;   // 奉献

    // 防护技能
    uint32 PaladinGuard_BlessArmor = 0;    // 护甲祝福
    uint32 PaladinGuard_BlessMight = 0;    // 力量祝福
    uint32 PaladinGuard_BlessWisdom = 0;   // 智慧祝福
    uint32 PaladinGuard_DivineShield = 0;  // 圣盾术
    uint32 PaladinGuard_DivineProtection = 0; // 圣佑术

    // 惩戒技能
    uint32 PaladinJudge_Light = 0;         // 审判之光
    uint32 PaladinJudge_Wisdom = 0;        // 审判之智
    uint32 PaladinJudge_Justice = 0;       // 审判之正义
    uint32 PaladinJudge_Command = 0;       // 审判之令

protected:
    void InitializeHolySpells(Player* player);
    void InitializeGuardSpells(Player* player);
    void InitializeJudgeSpells(Player* player);
};

// 法师技能类
class BotMageSpells : public BotClassSpells
{
public:
    void InitializeSpells(Player* player) override;

    // 火系技能
    uint32 MageFire_Fireball = 0;          // 火球术
    uint32 MageFire_Scorch = 0;            // 灼烧
    uint32 MageFire_Pyroblast = 0;         // 炎爆术
    uint32 MageFire_Combustion = 0;        // 燃烧

    // 冰系技能
    uint32 MageFrost_Frostbolt = 0;        // 寒冰箭
    uint32 MageFrost_Blizzard = 0;         // 暴风雪
    uint32 MageFrost_IceBlock = 0;         // 寒冰屏障
    uint32 MageFrost_FrostNova = 0;        // 冰霜新星

    // 奥术技能
    uint32 MageArcane_Missiles = 0;        // 奥术飞弹
    uint32 MageArcane_Explosion = 0;       // 奥术爆炸
    uint32 MageArcane_Polymorph = 0;       // 变形术
    uint32 MageArcane_Counterspell = 0;    // 法术反制

protected:
    void InitializeFireSpells(Player* player);
    void InitializeFrostSpells(Player* player);
    void InitializeArcaneSpells(Player* player);
};

// 其他职业技能类声明
class BotPriestSpells : public BotClassSpells { /* TODO */ };
class BotHunterSpells : public BotClassSpells { /* TODO */ };
class BotWarlockSpells : public BotClassSpells { /* TODO */ };
class BotShamanSpells : public BotClassSpells { /* TODO */ };
class BotDruidSpells : public BotClassSpells { /* TODO */ };
class BotDeathKnightSpells : public BotClassSpells { /* TODO */ };
