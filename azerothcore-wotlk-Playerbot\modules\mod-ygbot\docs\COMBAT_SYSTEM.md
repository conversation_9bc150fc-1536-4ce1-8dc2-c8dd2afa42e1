# YGbot 战斗系统文档

## 概述

YGbot 战斗系统是一个完整的机器人战斗AI框架，为 AzerothCore 的机器人提供智能战斗行为。该系统基于事件驱动架构，与现有的 BotEventSystem、MovementSystem 和 TalentSystem 完全集成。

## 系统架构

### 核心组件

1. **BotCombatAI** - 战斗AI核心引擎
   - 管理战斗状态和决策
   - 协调各个子系统
   - 处理战斗事件

2. **BotSpellManager** - 技能管理系统
   - 技能冷却管理
   - 法力/能量消耗计算
   - 技能条件检查
   - 技能优先级排序

3. **BotTargetManager** - 目标选择与威胁系统
   - 智能目标选择算法
   - 威胁值计算和管理
   - 仇恨列表维护
   - 目标切换决策

4. **BotCombatMovement** - 战斗移动控制器
   - 战斗中的移动控制
   - 与 MovementSystem 集成
   - 战术定位和躲避

5. **BotCombatStrategy** - 职业特定战斗策略
   - 为每个职业提供专门的战斗逻辑
   - 技能轮换管理
   - 资源管理

## 文件结构

```
src/CombatSystem/
├── BotCombatAI.h/.cpp          # 战斗AI核心
├── BotSpellManager.h/.cpp      # 技能管理
├── BotTargetManager.h/.cpp     # 目标管理
├── BotCombatMovement.h/.cpp    # 战斗移动
└── BotCombatStrategy.h/.cpp    # 战斗策略
```

## 配置选项

战斗系统的配置选项已添加到 `conf/mod_ygbot.conf.dist` 中：

### 基础配置
- `YGbot.Combat.Enable` - 启用战斗AI系统
- `YGbot.Combat.DebugMode` - 调试模式
- `YGbot.Combat.UpdateInterval` - 战斗更新间隔
- `YGbot.Combat.SpellInterval` - 技能使用间隔

### 距离配置
- `YGbot.Combat.Range.Default` - 默认战斗范围
- `YGbot.Combat.Range.Melee` - 近战攻击距离
- `YGbot.Combat.Range.Ranged` - 远程攻击距离
- `YGbot.Combat.Range.Spell` - 法术施放距离
- `YGbot.Combat.Range.Heal` - 治疗距离

### 策略配置
- `YGbot.Combat.Strategy.AggressionLevel` - 攻击性等级
- `YGbot.Combat.Strategy.DefensiveThreshold` - 防御阈值
- `YGbot.Combat.Strategy.RetreatThreshold` - 撤退阈值
- `YGbot.Combat.Strategy.UseAOE` - 启用AOE技能
- `YGbot.Combat.Strategy.UseCrowdControl` - 启用控制技能

### 职业特定配置
每个职业都有专门的配置选项，如：
- 战士的怒气阈值
- 法师的法力阈值
- 猎人的宠物使用
- 等等...

## 战斗状态

系统定义了以下战斗状态：

- `IDLE` - 空闲状态
- `ENGAGING` - 接敌中
- `FIGHTING` - 战斗中
- `RETREATING` - 撤退中
- `PURSUING` - 追击中
- `HEALING` - 治疗中
- `BUFFING` - 施加增益
- `DEAD` - 死亡状态

## 战斗角色

系统支持以下战斗角色：

- `TANK` - 坦克
- `HEALER` - 治疗
- `MELEE_DPS` - 近战DPS
- `RANGED_DPS` - 远程DPS
- `SUPPORT` - 辅助

## 目标选择策略

- `NEAREST` - 最近目标
- `HIGHEST_THREAT` - 最高威胁
- `LOWEST_HEALTH` - 最低生命值
- `HIGHEST_LEVEL` - 最高等级
- `ATTACKING_ME` - 正在攻击我的
- `CASTER_PRIORITY` - 施法者优先
- `HEALER_PRIORITY` - 治疗者优先

## 集成点

### 与现有系统的集成

1. **BotEventSystem 集成**
   - 通过 CombatEventHandler 处理战斗事件
   - 自动创建和管理战斗AI实例

2. **MovementSystem 集成**
   - BotCombatMovement 使用 BotMovementManager
   - 支持战斗中的复杂移动

3. **TalentSystem 集成**
   - 自动检测职业和天赋
   - 根据天赋调整战斗策略

## 使用方法

### 自动使用
战斗系统会自动为机器人创建战斗AI，无需手动干预。当机器人进入战斗时，系统会：

1. 创建 BotCombatAI 实例
2. 初始化相应的职业策略
3. 开始战斗循环

### 手动控制
可以通过以下方式手动控制战斗AI：

```cpp
// 获取机器人的战斗AI
auto combatAI = sBotCombatAIMgr->GetCombatAI(bot);

// 设置战斗角色
combatAI->SetCombatRole(BotCombatRole::TANK);

// 设置目标
combatAI->SetTarget(enemy);

// 进入战斗
combatAI->EnterCombat(enemy);
```

## 扩展开发

### 添加新的职业策略

1. 继承 `BotCombatStrategy` 类
2. 实现职业特定的方法
3. 在 `BotCombatStrategyFactory` 中注册

### 添加新的目标选择策略

1. 在 `BotTargetStrategy` 枚举中添加新策略
2. 在 `BotTargetManager::SelectTargetByStrategy` 中实现逻辑

### 添加新的移动类型

1. 在 `BotCombatMovementType` 枚举中添加新类型
2. 在 `BotCombatMovement` 中实现相应的处理方法

## 调试和监控

### 调试模式
启用 `YGbot.Combat.DebugMode = 1` 可以获得详细的调试信息。

### 日志级别
- `LOG_DEBUG` - 详细的战斗决策信息
- `LOG_INFO` - 重要的战斗事件
- `LOG_WARN` - 警告和异常情况
- `LOG_ERROR` - 错误和失败情况

## 性能考虑

- 战斗AI更新间隔默认为100ms，可根据服务器性能调整
- 技能使用间隔默认为1.5秒，模拟真实玩家行为
- 目标扫描间隔为500ms，平衡性能和响应性
- 威胁值会自动衰减，避免内存泄漏

## 未来扩展

1. **机器学习集成** - 让机器人从战斗中学习
2. **团队协作** - 更复杂的团队战斗策略
3. **PvP优化** - 针对玩家对战的特殊逻辑
4. **副本AI** - 针对副本的专门策略
5. **战场AI** - 战场特定的战斗逻辑

## 故障排除

### 常见问题

1. **机器人不进入战斗**
   - 检查 `YGbot.Combat.Enable` 是否为1
   - 确认机器人被正确标记为机器人

2. **技能不释放**
   - 检查技能冷却和法力消耗
   - 确认技能在机器人的技能列表中

3. **移动异常**
   - 检查 MovementSystem 是否正常工作
   - 确认地形验证通过

4. **目标选择错误**
   - 检查目标选择策略配置
   - 确认威胁系统正常工作

### 日志分析
查看服务器日志中的 "BotCombatAI"、"BotSpellManager"、"BotTargetManager" 等关键字来诊断问题。

## 贡献指南

欢迎为战斗系统贡献代码！请遵循以下指南：

1. 保持代码风格一致
2. 添加适当的注释和文档
3. 编写单元测试
4. 确保向后兼容性
5. 提交前进行充分测试

---

*本文档随系统更新而更新，最后修改时间：2025-08-02*
